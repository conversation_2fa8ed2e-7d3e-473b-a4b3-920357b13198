using System;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing application settings for WPF binding
    /// </summary>
    public partial class SettingsModel : ObservableObject
    {
        #region Observable Properties

        /// <summary>
        /// Indicates if auto calculation is enabled
        /// </summary>
        [ObservableProperty]
        private bool _autoCalculate = true;

        /// <summary>
        /// Default export path
        /// </summary>
        [ObservableProperty]
        private string _defaultExportPath = string.Empty;

        /// <summary>
        /// Node circuit path tolerance
        /// </summary>
        [ObservableProperty]
        private double _nodeCircuitPathTolerance = 0.0;

        /// <summary>
        /// Don't show warnings flag
        /// </summary>
        [ObservableProperty]
        private bool _dontShow = false;

        /// <summary>
        /// Settings controlled globally flag
        /// </summary>
        [ObservableProperty]
        private bool _settingsControlledGlobally = true;

        /// <summary>
        /// Auto-save interval in minutes
        /// </summary>
        [ObservableProperty]
        private int _autoSaveInterval = 5;

        /// <summary>
        /// Show advanced options
        /// </summary>
        [ObservableProperty]
        private bool _showAdvancedOptions = false;

        /// <summary>
        /// Enable logging
        /// </summary>
        [ObservableProperty]
        private bool _enableLogging = true;

        /// <summary>
        /// Log level (0=Error, 1=Warning, 2=Info, 3=Debug)
        /// </summary>
        [ObservableProperty]
        private int _logLevel = 2;

        /// <summary>
        /// Maximum log file size in MB
        /// </summary>
        [ObservableProperty]
        private int _maxLogFileSizeMb = 10;

        /// <summary>
        /// Number of log files to keep
        /// </summary>
        [ObservableProperty]
        private int _logFileRetentionCount = 5;

        /// <summary>
        /// Theme selection (Light, Dark, Auto)
        /// </summary>
        [ObservableProperty]
        private string _theme = "Light";

        /// <summary>
        /// UI scale factor
        /// </summary>
        [ObservableProperty]
        private double _uiScaleFactor = 1.0;

        /// <summary>
        /// Language/Culture setting
        /// </summary>
        [ObservableProperty]
        private string _culture = "en-US";

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        /// <summary>
        /// Error message
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        #endregion

        #region Properties

        /// <summary>
        /// Available themes
        /// </summary>
        public string[] AvailableThemes => new[] { "Light", "Dark", "Auto" };

        /// <summary>
        /// Available log levels
        /// </summary>
        public string[] LogLevels => new[] { "Error", "Warning", "Info", "Debug" };

        /// <summary>
        /// Log level description
        /// </summary>
        public string LogLevelDescription
        {
            get
            {
                return LogLevel switch
                {
                    0 => "Error",
                    1 => "Warning", 
                    2 => "Info",
                    3 => "Debug",
                    _ => "Unknown"
                };
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the SettingsModel with default values
        /// </summary>
        public SettingsModel()
        {
            // Load settings from application settings if available
            LoadFromApplicationSettings();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load settings from application configuration
        /// </summary>
        public void LoadFromApplicationSettings()
        {
            try
            {
                // Load from MEP.PowerBIM_5.Properties.Settings if available
                var settings = MEP.PowerBIM_5.Properties.Settings.Default;
                if (settings != null)
                {
                    NodeCircuitPathTolerance = settings.NodeCircuitPathTolerance;
                    DontShow = settings.DontShow;
                    SettingsControlledGlobally = settings.SettingsControlledGlobally;
                }
                
                ValidateSettings();
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Failed to load application settings: {ex.Message}";
            }
        }

        /// <summary>
        /// Save settings to application configuration
        /// </summary>
        public void SaveToApplicationSettings()
        {
            try
            {
                var settings = MEP.PowerBIM_5.Properties.Settings.Default;
                if (settings != null)
                {
                    settings.NodeCircuitPathTolerance = NodeCircuitPathTolerance;
                    settings.DontShow = DontShow;
                    settings.SettingsControlledGlobally = SettingsControlledGlobally;
                    settings.Save();
                }
                
                HasErrors = false;
                ErrorMessage = string.Empty;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Failed to save application settings: {ex.Message}";
            }
        }

        /// <summary>
        /// Reset settings to default values
        /// </summary>
        public void ResetToDefaults()
        {
            AutoCalculate = true;
            DefaultExportPath = string.Empty;
            NodeCircuitPathTolerance = 0.0;
            DontShow = false;
            SettingsControlledGlobally = true;
            AutoSaveInterval = 5;
            ShowAdvancedOptions = false;
            EnableLogging = true;
            LogLevel = 2;
            MaxLogFileSizeMb = 10;
            LogFileRetentionCount = 5;
            Theme = "Light";
            UiScaleFactor = 1.0;
            Culture = "en-US";
            
            HasErrors = false;
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// Validate current settings
        /// </summary>
        public bool ValidateSettings()
        {
            try
            {
                var errors = new System.Collections.Generic.List<string>();
                
                // Validate numeric ranges
                if (NodeCircuitPathTolerance < 0)
                    errors.Add("Node circuit path tolerance cannot be negative");
                
                if (AutoSaveInterval < 1 || AutoSaveInterval > 60)
                    errors.Add("Auto-save interval must be between 1 and 60 minutes");
                
                if (LogLevel < 0 || LogLevel > 3)
                    errors.Add("Log level must be between 0 and 3");
                
                if (MaxLogFileSizeMb < 1 || MaxLogFileSizeMb > 100)
                    errors.Add("Max log file size must be between 1 and 100 MB");
                
                if (LogFileRetentionCount < 1 || LogFileRetentionCount > 20)
                    errors.Add("Log file retention count must be between 1 and 20");
                
                if (UiScaleFactor < 0.5 || UiScaleFactor > 3.0)
                    errors.Add("UI scale factor must be between 0.5 and 3.0");
                
                // Validate theme
                if (!Array.Exists(AvailableThemes, t => t == Theme))
                    errors.Add("Invalid theme selection");
                
                // Validate export path if specified
                if (!string.IsNullOrEmpty(DefaultExportPath))
                {
                    try
                    {
                        var path = System.IO.Path.GetFullPath(DefaultExportPath);
                        if (!System.IO.Directory.Exists(System.IO.Path.GetDirectoryName(path)))
                            errors.Add("Default export path directory does not exist");
                    }
                    catch
                    {
                        errors.Add("Invalid default export path format");
                    }
                }
                
                HasErrors = errors.Count > 0;
                ErrorMessage = HasErrors ? string.Join("; ", errors) : string.Empty;
                
                return !HasErrors;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
                return false;
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle LogLevel property change
        /// </summary>
        partial void OnLogLevelChanged(int value)
        {
            OnPropertyChanged(nameof(LogLevelDescription));
            ValidateSettings();
        }

        /// <summary>
        /// Handle property changes that require validation
        /// </summary>
        partial void OnNodeCircuitPathToleranceChanged(double value)
        {
            ValidateSettings();
        }

        partial void OnAutoSaveIntervalChanged(int value)
        {
            ValidateSettings();
        }

        partial void OnMaxLogFileSizeMbChanged(int value)
        {
            ValidateSettings();
        }

        partial void OnLogFileRetentionCountChanged(int value)
        {
            ValidateSettings();
        }

        partial void OnUiScaleFactorChanged(double value)
        {
            ValidateSettings();
        }

        partial void OnThemeChanged(string value)
        {
            ValidateSettings();
        }

        partial void OnDefaultExportPathChanged(string value)
        {
            ValidateSettings();
        }

        #endregion
    }
}
