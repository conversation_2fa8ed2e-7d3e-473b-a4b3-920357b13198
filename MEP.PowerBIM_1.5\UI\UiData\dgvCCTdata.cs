﻿using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_1._5.UI.UiData
{
    class dgvCCTdata
    {
        #region Properties

        public string Circuit_Number { get; set; }
        public double Device_Rating { get; set; }
        public string Device_Curve_Type { get; set; }
        public string Protection_Device { get; set; }
        public string RCD_Protection { get; set; }
        public string Other_Controls { get; set; }
        public string Cable_To_First_Circuit_Component { get; set; }
        public string Cable_To_Remainder_Of_Circuit_Components { get; set; }
        public string Cable_Installation_Method { get; set; }
        public double Derating_Factor { get; set; }
        public double Diversity { get; set; }
        public double Length_To_First { get; set; }
        public double Length_To_Final { get; set; }
        public double UnDiversified_Current { get; set; }
        public double Number_Of_Elements { get; set; }
        public string Circuit_Description { get; set; }

        public string Check_Trip_Rating { get; set; }
        public string Cable_1_Valid { get; set; }
        public string Cable_2_Valid { get; set; }
        public string Check_CPD_Descriminates { get; set; }
        public string Check_Load_Current { get; set; }
        public string Check_Cable_1_Current { get; set; }
        public string Check_Cable_2_Current { get; set; }
        public string Check_EFLI { get; set; }
        public string Check_Final_CCT_VD { get; set; }
        public string Check_System_Max_VD { get; set; }
        public string Check_Cable_1_SC_Withstand { get; set; }
        public string Check_Cable_2_SC_Withstand { get; set; }

        public string Circuit_Check_Result { get; set; }
        public string Circuit_Revision { get; set; }
        public bool isSpareOrSpace { get; set; }

        #endregion

        #region Constructors

        public dgvCCTdata(PowerBIM_CircuitData CCT)
        {
            // Inputs
            Circuit_Number = CCT.CCT_Number;
            Device_Rating = CCT.Breaker.Schedule_Trip_Rating;
            Device_Curve_Type = CCT.Breaker.Schedule_Curve_Type;
            Protection_Device = CCT.Breaker.Schedule_Protective_Device;
            RCD_Protection = CCT.Schedule_RCD;
            Other_Controls = CCT.Schedule_Other_Controls;
            Cable_To_First_Circuit_Component = CCT.Schedule_Cable_To_First;
            Cable_To_Remainder_Of_Circuit_Components = CCT.Schedule_Cable_To_Final;
            Cable_Installation_Method = CCT.Schedule_Install_Method;
            Derating_Factor = CCT.Schedule_Derating_Factor;
            Diversity = CCT.CCT_Diversity;
            Length_To_First = CCT.LengthClass.Length_To_First;
            Length_To_Final = CCT.LengthClass.Length_Total;
            UnDiversified_Current = CCT.GetCurrent();
            Number_Of_Elements = 0;
            Circuit_Description = CCT.Schedule_Description;

            // Outputs
            Check_Trip_Rating = CCT.Schedule_CCTCheck_1_Data;
            Cable_1_Valid = CCT.Schedule_CCTCheck_2_CableToFirst;
            Cable_2_Valid = CCT.Schedule_CCTCheck_3_CableToFinal;
            Check_CPD_Descriminates = CCT.Schedule_CCTCheck_4_Discrimination;
            Check_Load_Current = CCT.Schedule_CCTCheck_5_BreakerCurrent;
            Check_Cable_1_Current = CCT.Schedule_CCTCheck_6_CableToFirstCurrent;
            Check_Cable_2_Current = CCT.Schedule_CCTCheck_7_CableToFinalCurrent;
            Check_EFLI = CCT.Schedule_CCTCheck_8_EFLI;
            Check_Final_CCT_VD = CCT.Schedule_CCTCheck_9_FinalCircuitVD;
            Check_System_Max_VD = CCT.Schedule_CCTCheck_10_SystemVD;
            Check_Cable_1_SC_Withstand = CCT.Schedule_CCTCheck_11_CableToFirstSC;
            Check_Cable_2_SC_Withstand = CCT.Schedule_CCTCheck_12_CableToFinalSC;
            Circuit_Check_Result = CCT.Schedule_CCTCheck_OK;

            Circuit_Revision = CCT.Schedule_Revision;
            isSpareOrSpace = CCT.CCT_Is_Spare_Or_Space;
        }

        #endregion
    }
}