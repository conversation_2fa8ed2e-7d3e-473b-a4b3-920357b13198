&lt;Window x:Class="MEP.PowerBIM_6.Views.CircuitEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
        Title="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}"
        Height="900"
        Width="1600"
        WindowState="Maximized"
        WindowStartupLocation="CenterOwner"
        Background="White"&gt;

    &lt;Window.Resources&gt;
        &lt;ResourceDictionary&gt;
            &lt;ResourceDictionary.MergedDictionaries&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.DeepPurple.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" /&gt;
            &lt;/ResourceDictionary.MergedDictionaries&gt;

            &lt;converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" /&gt;
            &lt;converters:StatusColorConverter x:Key="StatusColorConverter" /&gt;

            &lt;!-- DataGrid Cell Style for validation results --&gt;
            &lt;Style x:Key="ValidationCellStyle" TargetType="DataGridCell"&gt;
                &lt;Style.Triggers&gt;
                    &lt;DataTrigger Binding="{Binding CircuitCheckResult}" Value="PASS"&gt;
                        &lt;Setter Property="Background" Value="LightGreen"/&gt;
                    &lt;/DataTrigger&gt;
                    &lt;DataTrigger Binding="{Binding CircuitCheckResult}" Value="FAIL"&gt;
                        &lt;Setter Property="Background" Value="LightCoral"/&gt;
                    &lt;/DataTrigger&gt;
                    &lt;DataTrigger Binding="{Binding CircuitCheckResult}" Value="WARNING"&gt;
                        &lt;Setter Property="Background" Value="LightYellow"/&gt;
                    &lt;/DataTrigger&gt;
                &lt;/Style.Triggers&gt;
            &lt;/Style&gt;
        &lt;/ResourceDictionary&gt;
    &lt;/Window.Resources&gt;

    &lt;materialDesign:DialogHost&gt;
        &lt;Grid&gt;
            &lt;Grid.RowDefinitions&gt;
                &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Header &amp; Toolbar --&gt;
                &lt;RowDefinition Height="*"/&gt;    &lt;!-- Main Content --&gt;
                &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Phase Summary --&gt;
                &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Action Buttons --&gt;
            &lt;/Grid.RowDefinitions&gt;

            &lt;!-- Header &amp; Toolbar --&gt;
            &lt;materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid"&gt;
                &lt;Grid&gt;
                    &lt;Grid.ColumnDefinitions&gt;
                        &lt;ColumnDefinition Width="*"/&gt;
                        &lt;ColumnDefinition Width="Auto"/&gt;
                    &lt;/Grid.ColumnDefinitions&gt;

                    &lt;!-- Title --&gt;
                    &lt;StackPanel Grid.Column="0" Orientation="Horizontal" Margin="16,8"&gt;
                        &lt;Image Source="../BecaLogoBlack.png" Height="24" Margin="0,0,8,0"/&gt;
                        &lt;TextBlock Text="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}"
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Foreground="White" VerticalAlignment="Center"/&gt;
                    &lt;/StackPanel&gt;

                    &lt;!-- Toolbar --&gt;
                    &lt;StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,8"&gt;
                        &lt;Button Content="{Binding AutoCalculateButtonText}"
                                Command="{Binding ToggleAutoCalculateCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="4"/&gt;
                        &lt;Button Content="Activate Path Edit"
                                Command="{Binding ActivatePathEditCommand}"
                                Style="{StaticResource MaterialDesignRaisedAccentButton}"
                                Margin="4"/&gt;
                        &lt;Button Content="Refresh"
                                Command="{Binding RefreshFromRevitCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="4"/&gt;
                    &lt;/StackPanel&gt;
                &lt;/Grid&gt;
            &lt;/materialDesign:ColorZone&gt;

            &lt;!-- Main Content Area --&gt;
            &lt;Grid Grid.Row="1"&gt;
                &lt;Grid.RowDefinitions&gt;
                    &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Search Bar --&gt;
                    &lt;RowDefinition Height="*"/&gt;    &lt;!-- DataGrid --&gt;
                &lt;/Grid.RowDefinitions&gt;

                &lt;!-- Search Bar --&gt;
                &lt;materialDesign:Card Grid.Row="0" Margin="16,16,16,8"&gt;
                    &lt;Grid Margin="16,8"&gt;
                        &lt;Grid.ColumnDefinitions&gt;
                            &lt;ColumnDefinition Width="*"/&gt;
                            &lt;ColumnDefinition Width="Auto"/&gt;
                        &lt;/Grid.ColumnDefinitions&gt;

                        &lt;TextBox Grid.Column="0"
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 materialDesign:HintAssist.Hint="Search circuits by number or description..."
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"/&gt;

                        &lt;Button Grid.Column="1" Content="Clear"
                                Command="{Binding ClearSearchCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="8,0,0,0"/&gt;
                    &lt;/Grid&gt;
                &lt;/materialDesign:Card&gt;

                &lt;!-- Main Circuit DataGrid --&gt;
                &lt;DataGrid Grid.Row="1"
                          ItemsSource="{Binding CircuitData}"
                          SelectedItem="{Binding SelectedCircuit}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Margin="16,0,16,8"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          ScrollViewer.CanContentScroll="True"
                          ScrollViewer.HorizontalScrollBarVisibility="Auto"
                          ScrollViewer.VerticalScrollBarVisibility="Auto"&gt;

                    &lt;DataGrid.Columns&gt;
                        &lt;!-- Circuit Number --&gt;
                        &lt;DataGridTextColumn Header="Circuit #"
                                            Binding="{Binding CircuitNumber}"
                                            IsReadOnly="True"
                                            Width="80"
                                            CellStyle="{StaticResource ValidationCellStyle}"/&gt;

                        &lt;!-- Description --&gt;
                        &lt;DataGridTextColumn Header="Description"
                                            Binding="{Binding Description}"
                                            Width="150"/&gt;

                        &lt;!-- Device Rating --&gt;
                        &lt;DataGridComboBoxColumn Header="Device Rating"
                                                SelectedItemBinding="{Binding DeviceRating}"
                                                ItemsSource="{Binding DataContext.DeviceRatings, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/&gt;

                        &lt;!-- Device Curve Type --&gt;
                        &lt;DataGridComboBoxColumn Header="Curve Type"
                                                SelectedItemBinding="{Binding DeviceCurveType}"
                                                ItemsSource="{Binding DataContext.CurveTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="80"/&gt;

                        &lt;!-- Protection Device --&gt;
                        &lt;DataGridComboBoxColumn Header="Protection Device"
                                                SelectedItemBinding="{Binding ProtectionDevice}"
                                                ItemsSource="{Binding DataContext.ProtectionDevices, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/&gt;

                        &lt;!-- RCD Protection --&gt;
                        &lt;DataGridComboBoxColumn Header="RCD Protection"
                                                SelectedItemBinding="{Binding RcdProtection}"
                                                ItemsSource="{Binding DataContext.RcdProtectionOptions, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/&gt;

                        &lt;!-- Other Controls --&gt;
                        &lt;DataGridTextColumn Header="Other Controls"
                                            Binding="{Binding OtherControls}"
                                            Width="120"/&gt;

                        &lt;!-- Cable to First --&gt;
                        &lt;DataGridComboBoxColumn Header="Cable to First"
                                                SelectedItemBinding="{Binding CableToFirst}"
                                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/&gt;

                        &lt;!-- Cable to Remainder --&gt;
                        &lt;DataGridComboBoxColumn Header="Cable to Remainder"
                                                SelectedItemBinding="{Binding CableToRemainder}"
                                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/&gt;

                        &lt;!-- Installation Method --&gt;
                        &lt;DataGridComboBoxColumn Header="Installation Method"
                                                SelectedItemBinding="{Binding InstallationMethod}"
                                                ItemsSource="{Binding DataContext.InstallationMethods, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/&gt;

                        &lt;!-- Derating Factor --&gt;
                        &lt;DataGridTextColumn Header="Derating Factor"
                                            Binding="{Binding DeratingFactor, StringFormat=N2}"
                                            Width="100"/&gt;

                        &lt;!-- Diversity --&gt;
                        &lt;DataGridTextColumn Header="Diversity"
                                            Binding="{Binding Diversity, StringFormat=N2}"
                                            Width="80"/&gt;

                        &lt;!-- Manual Override --&gt;
                        &lt;DataGridCheckBoxColumn Header="Manual"
                                                Binding="{Binding IsManual}"
                                                Width="60"/&gt;

                        &lt;!-- Path Mode --&gt;
                        &lt;DataGridComboBoxColumn Header="Path Mode"
                                                SelectedItemBinding="{Binding PathMode}"
                                                ItemsSource="{Binding DataContext.PathModes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/&gt;

                        &lt;!-- Set Path Button --&gt;
                        &lt;DataGridTemplateColumn Header="Set Path" Width="80"&gt;
                            &lt;DataGridTemplateColumn.CellTemplate&gt;
                                &lt;DataTemplate&gt;
                                    &lt;Button Content="Set Path"
                                            Command="{Binding DataContext.SetPathCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            IsEnabled="{Binding CanEditPath}"
                                            FontSize="10"
                                            Padding="4,2"/&gt;
                                &lt;/DataTemplate&gt;
                            &lt;/DataGridTemplateColumn.CellTemplate&gt;
                        &lt;/DataGridTemplateColumn&gt;

                        &lt;!-- Length to First --&gt;
                        &lt;DataGridTextColumn Header="Length to First"
                                            Binding="{Binding LengthToFirst, StringFormat=N2}"
                                            Width="100"/&gt;

                        &lt;!-- Length to Final --&gt;
                        &lt;DataGridTextColumn Header="Total Length"
                                            Binding="{Binding LengthToFinal, StringFormat=N2}"
                                            Width="100"/&gt;

                        &lt;!-- Manual Current Checkbox --&gt;
                        &lt;DataGridCheckBoxColumn Header="Manual Current"
                                                Binding="{Binding ManualCurrent}"
                                                Width="100"/&gt;

                        &lt;!-- Current Value --&gt;
                        &lt;DataGridTextColumn Header="Current (A)"
                                            Binding="{Binding Current, StringFormat=N2}"
                                            IsReadOnly="True"
                                            Width="80"/&gt;

                        &lt;!-- Number of Elements Button --&gt;
                        &lt;DataGridTemplateColumn Header="Elements" Width="80"&gt;
                            &lt;DataGridTemplateColumn.CellTemplate&gt;
                                &lt;DataTemplate&gt;
                                    &lt;Button Content="{Binding NumberOfElements}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            FontSize="10"
                                            Padding="4,2"/&gt;
                                &lt;/DataTemplate&gt;
                            &lt;/DataGridTemplateColumn.CellTemplate&gt;
                        &lt;/DataGridTemplateColumn&gt;

                        &lt;!-- Validation Results Columns --&gt;
                        &lt;DataGridTextColumn Header="Trip Rating Check"
                                            Binding="{Binding CheckTripRating}"
                                            IsReadOnly="True"
                                            Width="120"/&gt;

                        &lt;DataGridTextColumn Header="Cable 1 Valid"
                                            Binding="{Binding Cable1Valid}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 2 Valid"
                                            Binding="{Binding Cable2Valid}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="CPD Discriminates"
                                            Binding="{Binding CheckCpdDiscriminates}"
                                            IsReadOnly="True"
                                            Width="120"/&gt;

                        &lt;DataGridTextColumn Header="Load Current Check"
                                            Binding="{Binding CheckLoadCurrent}"
                                            IsReadOnly="True"
                                            Width="120"/&gt;

                        &lt;DataGridTextColumn Header="Cable 1 Current"
                                            Binding="{Binding CheckCable1Current}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 2 Current"
                                            Binding="{Binding CheckCable2Current}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 1 VD%"
                                            Binding="{Binding CheckCable1VoltageDropPercent}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 2 VD%"
                                            Binding="{Binding CheckCable2VoltageDropPercent}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 1 SC"
                                            Binding="{Binding CheckCable1ScWithstand}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Cable 2 SC"
                                            Binding="{Binding CheckCable2ScWithstand}"
                                            IsReadOnly="True"
                                            Width="100"/&gt;

                        &lt;DataGridTextColumn Header="Check Summary"
                                            Binding="{Binding CircuitCheckSummary}"
                                            IsReadOnly="True"
                                            Width="150"/&gt;

                        &lt;DataGridTextColumn Header="Check Result"
                                            Binding="{Binding CircuitCheckResult}"
                                            IsReadOnly="True"
                                            Width="100"
                                            CellStyle="{StaticResource ValidationCellStyle}"/&gt;

                        &lt;DataGridTextColumn Header="Revision"
                                            Binding="{Binding CircuitRevision}"
                                            Width="80"/&gt;

                        &lt;DataGridCheckBoxColumn Header="Spare/Space"
                                                Binding="{Binding IsSpareOrSpace}"
                                                Width="80"/&gt;
                    &lt;/DataGrid.Columns&gt;
                &lt;/DataGrid&gt;
            &lt;/Grid&gt;

            &lt;!-- Phase Loading Summary --&gt;
            &lt;Grid Grid.Row="2" Margin="16,8"&gt;
                &lt;Grid.ColumnDefinitions&gt;
                    &lt;ColumnDefinition Width="*"/&gt;
                    &lt;ColumnDefinition Width="20"/&gt;
                    &lt;ColumnDefinition Width="*"/&gt;
                &lt;/Grid.ColumnDefinitions&gt;

                &lt;!-- Diversified Phase Loading --&gt;
                &lt;materialDesign:Card Grid.Column="0"&gt;
                    &lt;StackPanel Margin="16"&gt;
                        &lt;TextBlock Text="Diversified Phase Loading (Run PowerBIM)"
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,8"
                                   Background="{StaticResource PrimaryHueMidBrush}"
                                   Foreground="White"
                                   Padding="8,4"/&gt;
                        &lt;DataGrid ItemsSource="{Binding DiversifiedPhaseData}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="None"
                                  Background="Transparent"&gt;
                            &lt;DataGrid.Columns&gt;
                                &lt;DataGridTextColumn Header="Phase R" Binding="{Binding PhaseR, StringFormat=N2}" Width="*"/&gt;
                                &lt;DataGridTextColumn Header="Phase W" Binding="{Binding PhaseW, StringFormat=N2}" Width="*"/&gt;
                                &lt;DataGridTextColumn Header="Phase B" Binding="{Binding PhaseB, StringFormat=N2}" Width="*"/&gt;
                            &lt;/DataGrid.Columns&gt;
                        &lt;/DataGrid&gt;
                    &lt;/StackPanel&gt;
                &lt;/materialDesign:Card&gt;

                &lt;!-- Un-Diversified Phase Loading --&gt;
                &lt;materialDesign:Card Grid.Column="2"&gt;
                    &lt;StackPanel Margin="16"&gt;
                        &lt;TextBlock Text="Un-Diversified Phase Loading"
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,8"
                                   Background="{StaticResource PrimaryHueMidBrush}"
                                   Foreground="White"
                                   Padding="8,4"/&gt;
                        &lt;DataGrid ItemsSource="{Binding UnDiversifiedPhaseData}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="None"
                                  Background="Transparent"&gt;
                            &lt;DataGrid.Columns&gt;
                                &lt;DataGridTextColumn Header="Revit Phase R" Binding="{Binding RevitPhaseR, StringFormat=N2}" Width="*"/&gt;
                                &lt;DataGridTextColumn Header="Revit Phase W" Binding="{Binding RevitPhaseW, StringFormat=N2}" Width="*"/&gt;
                                &lt;DataGridTextColumn Header="Revit Phase B" Binding="{Binding RevitPhaseB, StringFormat=N2}" Width="*"/&gt;
                            &lt;/DataGrid.Columns&gt;
                        &lt;/DataGrid&gt;
                    &lt;/StackPanel&gt;
                &lt;/materialDesign:Card&gt;
            &lt;/Grid&gt;

            &lt;!-- Action Buttons &amp; Status --&gt;
            &lt;Grid Grid.Row="3" Background="{StaticResource MaterialDesignPaper}"&gt;
                &lt;Grid.ColumnDefinitions&gt;
                    &lt;ColumnDefinition Width="*"/&gt;
                    &lt;ColumnDefinition Width="Auto"/&gt;
                &lt;/Grid.ColumnDefinitions&gt;

                &lt;!-- Tips &amp; Status --&gt;
                &lt;StackPanel Grid.Column="0" Margin="16,8" VerticalAlignment="Center"&gt;
                    &lt;TextBlock Text="{Binding TipsText}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               TextWrapping="Wrap"
                               Foreground="{StaticResource MaterialDesignBodyLight}"/&gt;
                    &lt;TextBlock Text="{Binding StatusMessage}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{StaticResource PrimaryHueMidBrush}"
                               Margin="0,4,0,0"
                               Visibility="{Binding StatusMessage, Converter={StaticResource BoolToVisConverter}}"/&gt;
                &lt;/StackPanel&gt;

                &lt;!-- Action Buttons --&gt;
                &lt;StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,8"&gt;
                    &lt;Button Content="Cancel"
                            Command="{Binding CancelCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="4"
                            Padding="16,8"/&gt;
                    &lt;Button Content="Save"
                            Command="{Binding SaveCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="4"
                            Padding="16,8"/&gt;
                &lt;/StackPanel&gt;
            &lt;/Grid&gt;
        &lt;/Grid&gt;
    &lt;/materialDesign:DialogHost&gt;
&lt;/Window&gt;
