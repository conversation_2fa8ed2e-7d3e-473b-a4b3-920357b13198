&lt;Window x:Class="MEP.PowerBIM_6.Views.CircuitEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Circuit Edit - PowerBIM 6" 
        Height="600" 
        Width="1000"
        WindowState="Maximized"
        WindowStartupLocation="CenterOwner"
        Background="White"&gt;

    &lt;Window.Resources&gt;
        &lt;ResourceDictionary&gt;
            &lt;ResourceDictionary.MergedDictionaries&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" /&gt;
            &lt;/ResourceDictionary.MergedDictionaries&gt;
        &lt;/ResourceDictionary&gt;
    &lt;/Window.Resources&gt;

    &lt;Grid&gt;
        &lt;Grid.RowDefinitions&gt;
            &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Toolbar --&gt;
            &lt;RowDefinition Height="*"/&gt;    &lt;!-- Content --&gt;
            &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Actions --&gt;
        &lt;/Grid.RowDefinitions&gt;

        &lt;!-- Toolbar --&gt;
        &lt;ToolBarTray Grid.Row="0"&gt;
            &lt;ToolBar&gt;
                &lt;Button Content="Save" Command="{Binding SaveCommand}"/&gt;
                &lt;Button Content="Recalculate" Command="{Binding RecalculateCommand}"/&gt;
                &lt;Separator/&gt;
                &lt;Button Content="Activate Path Edit" /&gt;
                &lt;Button Content="Bulk Edit" /&gt;
            &lt;/ToolBar&gt;
        &lt;/ToolBarTray&gt;

        &lt;!-- Main Content --&gt;
        &lt;materialDesign:Card Grid.Row="1" Margin="16"&gt;
            &lt;StackPanel Margin="16"&gt;
                &lt;TextBlock Text="Enhanced Circuit Editor" 
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}" 
                           Margin="0,0,0,16"/&gt;
                
                &lt;TextBlock Text="{Binding DistributionBoardName, StringFormat='Distribution Board: {0}'}"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,0,0,16"/&gt;
                
                &lt;TextBlock Text="Circuit editing functionality will be implemented in Phase 3" 
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="Gray"/&gt;
                
                &lt;TextBlock Text="{Binding CircuitCount, StringFormat='Circuits: {0}'}" 
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           Margin="0,16,0,0"/&gt;
            &lt;/StackPanel&gt;
        &lt;/materialDesign:Card&gt;

        &lt;!-- Action Buttons --&gt;
        &lt;StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="16"&gt;
            &lt;Button Content="Cancel" Command="{Binding CancelCommand}" Margin="4"/&gt;
            &lt;Button Content="Save &amp; Close" Command="{Binding SaveCommand}" Margin="4"/&gt;
        &lt;/StackPanel&gt;
    &lt;/Grid&gt;
&lt;/Window&gt;
