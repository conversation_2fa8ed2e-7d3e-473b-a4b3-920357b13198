﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Color = System.Drawing.Color;
using Form = System.Windows.Forms.Form;
using Point = System.Drawing.Point;

namespace MEP.PowerBIM_5.UI.Forms
{
    //
    // G_ProgressBar Class
    //
    // Implements a modeless progress bar with reporting (errors, warnings, and notes). Up to five levels are available (level < 0 means hidden):
    //  - Top (level 1)
    //  - Upr (level 2)
    //  - Mid (level 3)
    //  - Lwr (level 4)
    //  - Bot (level 5).
    //
    // The interface is ...
    //
    // TODOs:
    //   1) Define the interface.
    //
    public partial class PB_ProgressBar : Form
    {
        //
        // Fields
        //

        // Percentage constant.
        const int intPC = 100;

        // Maximum number of progress bars to be used (to negate the need to update the redundant ones). Currently cannot exceed 5.
        const int intNumPBs = 5;

        // Values to calculate the required height of the window (as Base + Bar * Level) and progress bar width. Will be scaled by DPI setting (in percent).
        int intBaseHeight = 110;
        int intBaseWidth = 832;
        int intBarHeight = 68;
        int intBarWidth = 800;
        int intDPIScaling = 100;

        // Default Progress Bar values.
        const int intDefaultScope = 1;
        const int intDefaultTasksComplete = 0;
        const int intDefaultPercent = 0;
        const int intDefaultBarComplete = 0;
        const string strDefaultTask = "Task";
        const string strDefaultCaption = "Initialising ...";

        // The current level of the progress bar. This defines how many bars show and what bar is updated. We give this a public read-only property too.
        int intLevel = 0;
        public int Level { get { return intLevel; } }

        // Reporting counts and text strings.
        public int intErrors = 0;
        public int intWarnings = 0;
        public int intNotes = 0;

        // Level data fields. We add one so we can index starting at 1 (ie we ignore the first 0th element).
        int[] intScope = new int[intNumPBs + 1];             // Number of tasks (updates) required for each bar to reach 100%.
        int[] intTasksComplete = new int[intNumPBs + 1];     // Number of completed tasks for each bar.
        int[] intPercent = new int[intNumPBs + 1];           // Percent of scope completion.
        int[] intBarComplete = new int[intNumPBs + 1];       // Amount of the bar that is complete.
        string[] strTask = new string[intNumPBs + 1];        // Strings for the current tasks of each bar.
        string[] strCaption = new string[intNumPBs + 1];     // Strings for the caption line of each bar.


        //
        // Constructor
        //

        public PB_ProgressBar()
        {
            int i;

            // Default initialisation.
            InitializeComponent();

            // Customising the defaults.
            for (i = 1; i <= intNumPBs; i++)
            {
                strTask[i] = strDefaultTask + " - 0% Completed";
                strCaption[i] = strDefaultCaption;
                intScope[i] = intDefaultScope;
                intTasksComplete[i] = intDefaultTasksComplete;
                intPercent[i] = intDefaultPercent;
                intBarComplete[i] = intDefaultBarComplete;
            }

            // Scaling the form dimension parameters for an odd DPI (pretty rough but better than nothing).
            intDPIScaling = GetDPIScale();
            intBaseHeight = intBaseHeight * intDPIScaling / 100;
            intBaseWidth = intBaseWidth * intDPIScaling / 100;
            intBarHeight = intBarHeight * intDPIScaling / 100;
            intBarWidth = intBarWidth * intDPIScaling / 100;

            // Defining the starting position.
            this.StartPosition = FormStartPosition.CenterScreen;

            return;
        }


        //
        // Public Methods
        //

        public void SetLevel(int level)
        {
            //
            // SetLevel
            //
            // Updates the level and adapts the form size to suit. Redundant bars are not reset - do that manually with the Reset method if required.
            //

            // Checking level is valid.
            if (level < 0 || level > intNumPBs)
                return;

            // Setting the size of the form if we are (or will be) active.
            if (level > 0)
                this.Size = new Size(832, intBaseHeight + level * intBarHeight);

            // Showing the form (modeless) if we have changed from inactive to active.
            if (intLevel == 0 && level > 0)
                this.Show();

            // Hiding the form if we have changed from active to inactive.
            if (intLevel > 0 && level == 0)
                this.Hide();

            // Updating the level now we know it is OK.
            intLevel = level;

            // Refreshing the form to show the updates.
            this.Refresh();
            Application.DoEvents();

            return;
        }

        public void LevelUp()
        {
            //
            // LevelUp
            //
            // Shows an additional bar and sets it as the current to be updated.
            //

            // We just set the level to the next one up. This will do the error checking and the refresh.
            SetLevel(intLevel + 1);

            return;
        }

        public void LevelDown()
        {
            //
            // LevelDown
            //
            // Hides the current bar sets the previous one as the current to be updated. No bars are reset.
            //

            // We just set the level to the next one down. This will do the error checking too.
            SetLevel(intLevel - 1);

            return;
        }

        public void SetScope(int scope, int level = 0)
        {
            //
            // SetScope
            //
            // Sets the number of steps for the given level. The level is optional and will default to the current level.
            //

            // Aborting if scope is invalid or if level is out of range.
            if (scope <= 0 || level < 0 || level > intNumPBs)
                return;

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // Setting the scope.
            intScope[level] = scope;

            return;
        }

        public void SetTask(string task, int level = 0)
        {
            //
            // SetTask
            //
            // Sets the task of the passed level. The level is optional and will default to the current level.
            //

            // Aborting if level is out of range.
            if (level < 0 || level > intNumPBs)
                return;

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // Setting the scope.
            strTask[level] = task;

            // Updating the captions.
            UpdateTasks();

            // Refreshing and doing events.
            this.Refresh();
            Application.DoEvents();

            return;

        }

        public void SetCaption(string cap, int level = 0)
        {
            //
            // SetCaption
            //
            // Sets the caption of the passed level. The level is optional and will default to the current level.
            //

            // Aborting if level is out of range.
            if (level < 0 || level > intNumPBs)
                return;

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // Setting the scope.
            strCaption[level] = cap;

            // Updating the captions.
            UpdateCaptions();

            // Refreshing and doing events.
            this.Refresh();
            Application.DoEvents();

            return;

        }

        public void SetLocation(Point p)
        {
            //
            // SetLocation
            //
            // Sets the form position to manual at the given point.
            //
            // TODOs:
            //  1) Add validation for the screen dimensions and a way to revert to Center of Parent or Screen.
            //

            // Manualisation.
            this.StartPosition = FormStartPosition.Manual;

            // Setting the new location.
            this.Location = p;

            // Refreshing and doing events.
            Refresh();
            Application.DoEvents();

            return;
        }

        public void Reset(int level = 0)
        {
            //
            // Reset
            //
            // Resets the given bar level to the defaults. The level is optional and will default to the current level.
            //

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // Resetting the bar parameters.
            strTask[level] = strDefaultTask;
            strCaption[level] = strDefaultCaption;
            intScope[level] = intDefaultScope;
            intTasksComplete[level] = intDefaultTasksComplete;
            intPercent[level] = intDefaultPercent;
            intBarComplete[level] = intDefaultBarComplete;

            // Updating the data in the form.
            Recalculate();
            UpdatePercents();
            UpdateCaptions();

            // Refreshing and doing events.
            Refresh();
            Application.DoEvents();

            return;
        }

        public void ResetAll()
        {
            //
            // ResetAll
            //
            // Resets all of the progress bars to the defaults.
            //

            // Looping through each bar.
            for (int i = 0; i <= intNumPBs; i++)
                Reset(i);

            // Refreshing and doing events.
            Refresh();
            Application.DoEvents();

            return;
        }

        public void Step(string cap = "", int steps = 1, int level = 0)
        {
            //
            // Step
            //
            // Updates the progress bar of the level given for the competion of a steps (or steps). All options are optional.
            //

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // We can't update anything if we are at level 0, or if level is not in the valid range.
            if (level <= 0 || level > intNumPBs)
                return;

            // Updating the completion (ie number of completed tasks) for this level.            
            intTasksComplete[level] += steps;

            // We recalculate the progress of this and the previous levels that may need their percentage complete updated.
            Recalculate(level);

            // We can update the percentages and bar width within the form.
            UpdatePercents();

            // Updating the caption if required.
            if (!cap.Equals(""))
            {
                // Saving the string.
                strCaption[level] = cap;

                // Updating the captions.
                UpdateCaptions();
            }

            // Refreshing the form to show the updates to text. // We also give the application a chance to clear its action queue.
            this.Refresh();
            Application.DoEvents();

            return;
        }

        public void AddError(string str)
        {
            //
            // AddError
            //
            // Adds an error to the reporting window and increments the counter. Errors will be coloured red.
            //

            // If this is the first error we change the text style to bold and colour to red.
            // TODO - ForeColor change not working so BackColor has been used as workaround for now.
            if (intErrors == 0)
            {
                //this.PB_ErrorText.Font = new Font(this.PB_ErrorText.Font, FontStyle.Bold);
                this.PB_ErrorText.ForeColor = Color.FromArgb(255, 96, 96);
                this.PB_ErrorText.BackColor = Color.FromArgb(255, 96, 96);
            }

            // Updaing the error count.
            intErrors += 1;

            // Updating the error text.
            this.PB_ErrorText.Text = "Errors - " + intErrors.ToString() + " (" + str + ")";

            // Refreshing the form so the new note shows.
            this.Refresh();

            return;
        }

        public void AddWarning(string str)
        {
            //
            // AddWarning
            //
            // Adds a warning to the reporting window and increments the counter. Warnings will be coloured orange.
            //

            // If this is the first warning we change the text style to bold and colour to orange.
            // TODO - ForeColor change not working so BackColor has been used as workaround for now.
            if (intWarnings == 0)
            {
                this.PB_WarningText.Font = new Font(this.PB_WarningText.Font, FontStyle.Bold);
                this.PB_WarningText.ForeColor = Color.FromArgb(255, 192, 96);
                this.PB_WarningText.BackColor = Color.FromArgb(255, 192, 96);
            }

            // Updaing the warning count.
            intWarnings += 1;

            // Updating the warning text.
            this.PB_WarningText.Text = "Warnings - " + intWarnings.ToString() + " (" + str + ")";

            // Refreshing the form so the new note shows.
            this.Refresh();

            return;
        }

        public void AddNote(string str)
        {
            //
            // AddNote
            //
            // Adds a note to the reporting window and increments the counter. Notes will be coloured green.
            //

            // If this is the first note we change the text style to bold and colour to green.
            // TODO - ForeColor change not working so BackColor has been used as workaround for now.
            if (intNotes == 0)
            {
                this.PB_NoteText.Font = new Font(this.PB_NoteText.Font, FontStyle.Bold);
                this.PB_NoteText.ForeColor = Color.FromArgb(255, 0, 0);
                this.PB_NoteText.BackColor = Color.FromArgb(192, 255, 192);
            }

            // Updaing the note count.
            intNotes += 1;

            // Updating the note text.
            this.PB_NoteText.Text = "Notes - " + intNotes.ToString() + " (" + str + ")";

            // Refreshing the form so the new note shows.
            this.Refresh();

            return;
        }


        //
        // Private Methods
        //

        private void Recalculate(int level = 0)
        {
            //
            // Recalculate
            //
            // Updates the progress bar values based on intComplete and intScope starting at the passed level down to 0. The completion of lower levels is
            // taken into account for higher level bars. Level is optional and defaults to the current level. Use intNumPBs if you want to update all.
            //

            double temp = 0;

            // Aborting if level is out of range.
            if (level < 0 || level > intNumPBs)
                return;

            // Setting the default level if relevant.
            if (level == 0)
                level = intLevel;

            // Running through the bars upwards.
            while (level > 0)
            {
                // Calculating the exact percent complete for this level including contribution for the prevous bar.
                temp = (double)intTasksComplete[level] / (double)intScope[level] + temp / (double)intScope[level];

                // Checking temp is in the valid range (0,1). This enforces 0 <= Percent <= intPC.
                if (temp < 0)
                    temp = 0;
                else if (temp > 1)
                    temp = 1;

                // Updating percent and bar width values.
                intPercent[level] = (int)(temp * intPC);
                intBarComplete[level] = (int)(temp * intBarWidth);

                // Moving up to the previous bar level.
                level--;
            }

            return;
        }

        private void UpdatePercents()
        {
            //
            // UpdatePercents
            //
            // Updates the form progress bar values with those currently defined in intPercent. We do this because it is quicker and easier than converting
            // into the relevant label names within the main body of code.
            //

            // Updating the progress bar values.
            if (intNumPBs >= 1) { this.PB_TopProgressBar.Width = intBarComplete[1]; }
            if (intNumPBs >= 2) { this.PB_UprProgressBar.Width = intBarComplete[2]; }
            if (intNumPBs >= 3) { this.PB_MidProgressBar.Width = intBarComplete[3]; }
            if (intNumPBs >= 4) { this.PB_LwrProgressBar.Width = intBarComplete[4]; }
            if (intNumPBs >= 5) { this.PB_BotProgressBar.Width = intBarComplete[5]; }

            // We also need to update the tasks ...
            UpdateTasks();

            return;
        }

        private void UpdateTasks()
        {
            //
            // UpdateTasks
            //
            // Updates the form task text strings with those currently defined in strTask. We do this because it is quicker and easier than converting into the
            // relevant label names within the main body of code.
            //

            // Updating the task strings for the new percent complete.
            if (intNumPBs >= 1) { this.PB_TopTask.Text = strTask[1] + " - " + intPercent[1].ToString() + "% Complete"; }
            if (intNumPBs >= 2) { this.PB_UprTask.Text = strTask[2] + " - " + intPercent[2].ToString() + "% Complete"; }
            if (intNumPBs >= 3) { this.PB_MidTask.Text = strTask[3] + " - " + intPercent[3].ToString() + "% Complete"; }
            if (intNumPBs >= 4) { this.PB_LwrTask.Text = strTask[4] + " - " + intPercent[4].ToString() + "% Complete"; }
            if (intNumPBs >= 5) { this.PB_BotTask.Text = strTask[5] + " - " + intPercent[5].ToString() + "% Complete"; }

            return;
        }

        private void UpdateCaptions()
        {
            //
            // UpdateCaptions
            //
            // Updates the form captions with those currently defined in strCaptions. We do this because it is quicker and easier than converting into the
            // relevant lable names within the main body of code.
            //

            // Updating the captions.
            if (intNumPBs >= 1) { this.PB_TopCaption.Text = strCaption[1]; }
            if (intNumPBs >= 2) { this.PB_UprCaption.Text = strCaption[2]; }
            if (intNumPBs >= 3) { this.PB_MidCaption.Text = strCaption[3]; }
            if (intNumPBs >= 4) { this.PB_LwrCaption.Text = strCaption[4]; }
            if (intNumPBs >= 5) { this.PB_BotCaption.Text = strCaption[5]; }
        }

        private int GetDPIScale()
        {
            //
            // GetDPIScale()
            //
            // Gets the current DPI scale setting in percent (ie 100 = no scaling, 125 = 125%, etc). Note that this is not 100% reliable as can often be wrong
            // when multiple monitors are used with different scales.
            //

            // We take this from the graphics object. The default (for 100%) is 96 DPI. 120 therefore means 125% etc.
            Graphics gpx = this.CreateGraphics();
            return (int)Math.Round(gpx.DpiX * 100 / 96);
        }
    }
}
