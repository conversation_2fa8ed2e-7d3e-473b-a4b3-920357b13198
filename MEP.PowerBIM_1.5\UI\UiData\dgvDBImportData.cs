﻿using Autodesk.Revit.DB;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.PowerBIM_1._5.UI.UiData
{
    public class dgvDBImportData
    {
        #region Properties
        public string DB_Name { get; set; }
        public double Main_Switch_Rating { get; set; }
        public string Feeder_Cable { get; set; }
        public string Location { get; set; }
        public string Seismic_Category { get; set; }
        public string Form_Rating { get; set; }
        public string Bus_Fault_Level { get; set; }
        public string Surge_Protection { get; set; }
        public string Metering { get; set; }
        public double Upstream_Device_Rating { get; set; }
        public double EFLI_R { get; set; }
        public double EFLI_X { get; set; }
        public double DBVD { get; set; }
        public double PSCC { get; set; }
        public string Device_Fault_Rating { get; set; }
        public double Final_Circuit_MaxVD { get; set; }
        public bool Data_Good { get; set; }


        public bool Manual { get; set; }
        public string Circuit_Path_Mode { get; set; }
        public double Length_Total { get; set; }

        #endregion

        #region Constructors

        public dgvDBImportData(PowerBIM_DBData DB)
        {
            DB_Name = DB.Schedule_DB_Name;
            Main_Switch_Rating = DB.Schedule_Main_Switch_Rating;
            Feeder_Cable = DB.Schedule_Feeder_Cable;
            Location = DB.Schedule_Location;
            Seismic_Category = DB.Schedule_Seismic_Category;
            Form_Rating = DB.Schedule_Form_Rating;
            Bus_Fault_Level = DB.Schedule_Bus_Fault_Level;
            Surge_Protection = DB.Schedule_Surge_Protection;
            Metering = DB.Schedule_Metering;
            Upstream_Device_Rating = DB.Upstream_Device_Rating;
            EFLI_R = DB.EFLI_R;
            EFLI_X = DB.EFLI_X;
            DBVD = DB.DBVD;
            PSCC = DB.PSCC;
            Device_Fault_Rating = DB.Schedule_Device_Fault_Rating;
            Final_Circuit_MaxVD = DB.Schedule_Final_Circuit_MaxVD;
            Data_Good = DB.Data_Good;

            Manual = DB.DB_IsManual();
            Circuit_Path_Mode = DB.LengthClass.Circuit_Path_Mode;
            Length_Total = Math.Round(DB.LengthClass.Length_Total / 1000, 1);
        }

        #endregion

        #region Methods

        public override bool Equals(Object obj)
        {

            //Check for null and compare run-time types.
            if ((obj == null))
            {
                return false;
            }
            else if (this.GetType().Equals(obj.GetType()))
            {
                dgvDBImportData p = (dgvDBImportData)obj;
                return (DB_Name == p.DB_Name)
                       && (Main_Switch_Rating == p.Main_Switch_Rating)
                       && (Feeder_Cable == p.Feeder_Cable)
                       && (Location == p.Location)
                       && (Seismic_Category == p.Seismic_Category)
                       && (Form_Rating == p.Form_Rating)
                       && (Bus_Fault_Level == p.Bus_Fault_Level)
                       && (Surge_Protection == p.Surge_Protection)
                       && (Metering == p.Metering)
                       && (Upstream_Device_Rating == p.Upstream_Device_Rating)
                       && (EFLI_R == p.EFLI_R)
                       && (EFLI_X == p.EFLI_X)
                       && (DBVD == p.DBVD)
                       && (PSCC == p.PSCC)
                       && (Device_Fault_Rating == p.Device_Fault_Rating)
                       && (Final_Circuit_MaxVD == p.Final_Circuit_MaxVD)
                       && (Data_Good == p.Data_Good);

            }
            else if (obj.GetType() == typeof( MEP.PowerBIM_5.DataSets.DBInfo.DBInformationRow))
            {
                var objInfo = obj as MEP.PowerBIM_5.DataSets.DBInfo.DBInformationRow;

                return (DB_Name == objInfo.DB_Name)
                       && (Main_Switch_Rating == objInfo.Main_Switch_Rating)
                       && (Feeder_Cable == objInfo.Feeder_Cable)
                       && (Location == objInfo.Location)
                       && (Seismic_Category == objInfo.Seismic_Category)
                       && (Form_Rating == objInfo.Form_Rating)
                       && (Bus_Fault_Level == objInfo.Bus_Fault_Level)
                       && (Surge_Protection == objInfo.Surge_Protection)
                       && (Metering == objInfo.Metering)
                       && (Upstream_Device_Rating == objInfo.Upstream_Device_Rating)
                       && (EFLI_R == objInfo.EFLI_R)
                       && (EFLI_X == objInfo.EFLI_X)
                       && (DBVD == objInfo.DBVD)
                       && (PSCC == objInfo.PSCC)
                       && (Device_Fault_Rating == objInfo.Device_Fault_Rating)
                       && (Final_Circuit_MaxVD == objInfo.Final_Circuit_MaxVD);
            }
            else
            {
                return false;
            }
        }

        public override int GetHashCode()
        {
            return (DB_Name +
              Main_Switch_Rating +
              Feeder_Cable +
              Location +
              Seismic_Category +
              Form_Rating +
              Bus_Fault_Level +
              Surge_Protection +
              Metering +
              Upstream_Device_Rating +
              EFLI_R +
              EFLI_X +
              DBVD +
              PSCC +
              Device_Fault_Rating +
              Final_Circuit_MaxVD +
              Data_Good).GetHashCode();
        }

        public override string ToString()
        {
            return string.Format("{0}, {1}", DB_Name, Data_Good);
        }

        #endregion
    }

    public class dgvDBImportDataStatus
    {
        #region Properties
        public bool Upstream_Device_Rating { get; set; }
        public bool EFLI_R { get; set; }
        public bool EFLI_X { get; set; }
        public bool DBVD { get; set; }
        public bool PSCC { get; set; }
        public bool Main_Switch_Rating { get; set; }

        public int dgvRowNumber { get; set; }

        public dgvDBImportDataStatus(int dgvRowNumber)
        {
            this.dgvRowNumber = dgvRowNumber;
        }

        #endregion

        #region Constructors


        #endregion

        #region Methods

        #endregion
    }
}