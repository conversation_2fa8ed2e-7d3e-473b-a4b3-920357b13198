﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DBSummaryDataSet" targetNamespace="http://tempuri.org/DataSet1.xsd" xmlns:mstns="http://tempuri.org/DataSet1.xsd" xmlns="http://tempuri.org/DataSet1.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DBSummaryDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DBSummaryDataSet" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DBSummaryDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="DBSummaryTable" msprop:Generator_RowClassName="DBSummaryTableRow" msprop:Generator_RowEvHandlerName="DBSummaryTableRowChangeEventHandler" msprop:Generator_RowDeletedName="DBSummaryTableRowDeleted" msprop:Generator_RowDeletingName="DBSummaryTableRowDeleting" msprop:Generator_RowEvArgName="DBSummaryTableRowChangeEvent" msprop:Generator_TablePropName="DBSummaryTable" msprop:Generator_RowChangedName="DBSummaryTableRowChanged" msprop:Generator_UserTableName="DBSummaryTable" msprop:Generator_RowChangingName="DBSummaryTableRowChanging" msprop:Generator_TableClassName="DBSummaryTableDataTable" msprop:Generator_TableVarName="tableDBSummaryTable">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="DB_Name" msprop:Generator_UserColumnName="DB_Name" msprop:Generator_ColumnPropNameInTable="DB_NameColumn" msprop:Generator_ColumnPropNameInRow="DB_Name" msprop:Generator_ColumnVarNameInTable="columnDB_Name" type="xs:string" minOccurs="0" />
              <xs:element name="DB_PassCount" msprop:Generator_UserColumnName="DB_PassCount" msprop:Generator_ColumnPropNameInTable="DB_PassCountColumn" msprop:Generator_ColumnPropNameInRow="DB_PassCount" msprop:Generator_ColumnVarNameInTable="columnDB_PassCount" type="xs:string" minOccurs="0" />
              <xs:element name="DB_WarningCount" msprop:Generator_UserColumnName="DB_WarningCount" msprop:Generator_ColumnPropNameInTable="DB_WarningCountColumn" msprop:Generator_ColumnPropNameInRow="DB_WarningCount" msprop:Generator_ColumnVarNameInTable="columnDB_WarningCount" type="xs:string" minOccurs="0" />
              <xs:element name="DB_FailCount" msprop:Generator_UserColumnName="DB_FailCount" msprop:Generator_ColumnPropNameInTable="DB_FailCountColumn" msprop:Generator_ColumnPropNameInRow="DB_FailCount" msprop:Generator_ColumnVarNameInTable="columnDB_FailCount" type="xs:string" minOccurs="0" />
              <xs:element name="DB_Notes" msprop:Generator_UserColumnName="DB_Notes" msprop:Generator_ColumnPropNameInTable="DB_NotesColumn" msprop:Generator_ColumnPropNameInRow="DB_Notes" msprop:Generator_ColumnVarNameInTable="columnDB_Notes" type="xs:string" minOccurs="0" />
              <xs:element name="DB_UpdateRequired" msprop:Generator_UserColumnName="DB_UpdateRequired" msprop:Generator_ColumnPropNameInTable="DB_UpdateRequiredColumn" msprop:Generator_ColumnPropNameInRow="DB_UpdateRequired" msprop:Generator_ColumnVarNameInTable="columnDB_UpdateRequired" type="xs:string" minOccurs="0" />
              <xs:element name="DB_Settings" msprop:Generator_UserColumnName="DB_Settings" msprop:Generator_ColumnPropNameInTable="DB_SettingsColumn" msprop:Generator_ColumnPropNameInRow="DB_Settings" msprop:Generator_ColumnVarNameInTable="columnDB_Settings" type="xs:string" minOccurs="0" />
              <xs:element name="DB_UserNotes" msprop:Generator_ColumnPropNameInRow="DB_UserNotes" msprop:Generator_ColumnPropNameInTable="DB_UserNotesColumn" msprop:Generator_ColumnVarNameInTable="columnDB_UserNotes" msprop:Generator_UserColumnName="DB_UserNotes" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>