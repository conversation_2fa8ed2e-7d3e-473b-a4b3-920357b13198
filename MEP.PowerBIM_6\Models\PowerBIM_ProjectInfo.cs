using System;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Project Information class for MEP.PowerBIM_6
    /// Contains project-level data and settings for electrical calculations
    /// </summary>
    public class PowerBIM_ProjectInfo
    {
        #region Properties

        /// <summary>
        /// Revit document reference
        /// </summary>
        public Document Document { get; set; }

        /// <summary>
        /// Revit UI document reference
        /// </summary>
        public UIDocument UIDocument { get; set; }

        /// <summary>
        /// Project name from Revit
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Project number from Revit
        /// </summary>
        public string ProjectNumber { get; set; }

        /// <summary>
        /// Project address
        /// </summary>
        public string ProjectAddress { get; set; }

        /// <summary>
        /// Client name
        /// </summary>
        public string ClientName { get; set; }

        /// <summary>
        /// Project status
        /// </summary>
        public string ProjectStatus { get; set; }

        /// <summary>
        /// Issue date
        /// </summary>
        public string IssueDate { get; set; }

        /// <summary>
        /// Author information
        /// </summary>
        public string Author { get; set; }

        /// <summary>
        /// Organization name
        /// </summary>
        public string Organization { get; set; }

        /// <summary>
        /// Building type
        /// </summary>
        public string BuildingType { get; set; }

        /// <summary>
        /// Electrical design standards
        /// </summary>
        public string ElectricalStandards { get; set; }

        /// <summary>
        /// Voltage level
        /// </summary>
        public double VoltageLevel { get; set; }

        /// <summary>
        /// Frequency
        /// </summary>
        public double Frequency { get; set; }

        /// <summary>
        /// Default cable installation method
        /// </summary>
        public string DefaultInstallationMethod { get; set; }

        /// <summary>
        /// Default derating factor
        /// </summary>
        public double DefaultDeratingFactor { get; set; }

        /// <summary>
        /// Default diversity factor
        /// </summary>
        public double DefaultDiversityFactor { get; set; }

        /// <summary>
        /// Maximum voltage drop percentage
        /// </summary>
        public double MaxVoltageDropPercent { get; set; }

        /// <summary>
        /// Indicates if project data is valid
        /// </summary>
        public bool IsValid { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_ProjectInfo()
        {
            InitializeDefaults();
        }

        /// <summary>
        /// Constructor with UIDocument
        /// </summary>
        /// <param name="uidoc">Revit UI document</param>
        public PowerBIM_ProjectInfo(UIDocument uidoc)
        {
            UIDocument = uidoc;
            Document = uidoc?.Document;
            
            InitializeDefaults();
            LoadProjectData();
        }

        /// <summary>
        /// Constructor with Document
        /// </summary>
        /// <param name="doc">Revit document</param>
        public PowerBIM_ProjectInfo(Document doc)
        {
            Document = doc;
            
            InitializeDefaults();
            LoadProjectData();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            VoltageLevel = 230.0; // Default voltage
            Frequency = 50.0; // Default frequency
            DefaultInstallationMethod = "Clipped Direct";
            DefaultDeratingFactor = 1.0;
            DefaultDiversityFactor = 1.0;
            MaxVoltageDropPercent = 5.0;
            ElectricalStandards = "IEC";
            IsValid = false;
        }

        /// <summary>
        /// Load project data from Revit document
        /// </summary>
        private void LoadProjectData()
        {
            if (Document == null) return;

            try
            {
                // Get project information from Revit
                ProjectInformation projInfo = Document.ProjectInformation;
                
                if (projInfo != null)
                {
                    ProjectName = GetParameterValue(projInfo, BuiltInParameter.PROJECT_NAME) ?? "Unknown Project";
                    ProjectNumber = GetParameterValue(projInfo, BuiltInParameter.PROJECT_NUMBER) ?? "000000";
                    ProjectAddress = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ADDRESS) ?? "";
                    ClientName = GetParameterValue(projInfo, BuiltInParameter.CLIENT_NAME) ?? "";
                    ProjectStatus = GetParameterValue(projInfo, BuiltInParameter.PROJECT_STATUS) ?? "";
                    IssueDate = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ISSUE_DATE) ?? "";
                    Author = GetParameterValue(projInfo, BuiltInParameter.PROJECT_AUTHOR) ?? "";
                    Organization = GetParameterValue(projInfo, BuiltInParameter.PROJECT_ORGANIZATION_NAME) ?? "";
                    BuildingType = GetParameterValue(projInfo, BuiltInParameter.PROJECT_BUILDING_TYPE) ?? "";
                }

                // Load electrical-specific parameters if they exist
                LoadElectricalParameters();

                IsValid = true;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - use defaults
                System.Diagnostics.Debug.WriteLine($"Error loading project data: {ex.Message}");
                IsValid = false;
            }
        }

        /// <summary>
        /// Load electrical-specific parameters
        /// </summary>
        private void LoadElectricalParameters()
        {
            // Try to load custom electrical parameters if they exist
            // These would be project parameters set up for PowerBIM
            
            // For now, use defaults - in a real implementation, these would be
            // loaded from project parameters or a configuration file
        }

        /// <summary>
        /// Get parameter value as string
        /// </summary>
        /// <param name="element">Element to get parameter from</param>
        /// <param name="paramId">Built-in parameter ID</param>
        /// <returns>Parameter value as string</returns>
        private string GetParameterValue(Element element, BuiltInParameter paramId)
        {
            try
            {
                Parameter param = element.get_Parameter(paramId);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.String:
                            return param.AsString();
                        case StorageType.Integer:
                            return param.AsInteger().ToString();
                        case StorageType.Double:
                            return param.AsDouble().ToString();
                        default:
                            return param.AsValueString();
                    }
                }
            }
            catch
            {
                // Ignore parameter read errors
            }
            
            return null;
        }

        /// <summary>
        /// Commit project information changes back to Revit
        /// </summary>
        public void CommitProjectInfo()
        {
            if (Document == null) return;

            try
            {
                using (Transaction trans = new Transaction(Document, "Update PowerBIM Project Info"))
                {
                    trans.Start();
                    
                    // Update project information parameters
                    ProjectInformation projInfo = Document.ProjectInformation;
                    if (projInfo != null)
                    {
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_NAME, ProjectName);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_NUMBER, ProjectNumber);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_ADDRESS, ProjectAddress);
                        SetParameterValue(projInfo, BuiltInParameter.CLIENT_NAME, ClientName);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_STATUS, ProjectStatus);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_AUTHOR, Author);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_ORGANIZATION_NAME, Organization);
                        SetParameterValue(projInfo, BuiltInParameter.PROJECT_BUILDING_TYPE, BuildingType);
                    }
                    
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error committing project info: {ex.Message}");
            }
        }

        /// <summary>
        /// Set parameter value
        /// </summary>
        /// <param name="element">Element to set parameter on</param>
        /// <param name="paramId">Built-in parameter ID</param>
        /// <param name="value">Value to set</param>
        private void SetParameterValue(Element element, BuiltInParameter paramId, string value)
        {
            try
            {
                Parameter param = element.get_Parameter(paramId);
                if (param != null && !param.IsReadOnly && !string.IsNullOrEmpty(value))
                {
                    param.Set(value);
                }
            }
            catch
            {
                // Ignore parameter write errors
            }
        }

        #endregion
    }
}
