&lt;Window x:Class="MEP.PowerBIM_6.Views.ExportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Export Data - PowerBIM 6" 
        Height="400" 
        Width="500"
        WindowStartupLocation="CenterOwner"
        Background="White"&gt;

    &lt;Grid&gt;
        &lt;materialDesign:Card Margin="16"&gt;
            &lt;StackPanel Margin="16"&gt;
                &lt;TextBlock Text="Export Data" 
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}" 
                           Margin="0,0,0,16"/&gt;
                
                &lt;TextBlock Text="Export functionality will be implemented in Phase 4" 
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="Gray"
                           Margin="0,0,0,32"/&gt;
                
                &lt;StackPanel Orientation="Horizontal" HorizontalAlignment="Right"&gt;
                    &lt;Button Content="Browse" Command="{Binding BrowseOutputPathCommand}" Margin="4"/&gt;
                    &lt;Button Content="Cancel" Command="{Binding CancelCommand}" Margin="4"/&gt;
                    &lt;Button Content="Export" Command="{Binding ExportCommand}" Margin="4"/&gt;
                &lt;/StackPanel&gt;
            &lt;/StackPanel&gt;
        &lt;/materialDesign:Card&gt;
    &lt;/Grid&gt;
&lt;/Window&gt;
