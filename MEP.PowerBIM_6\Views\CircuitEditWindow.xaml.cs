using System;
using System.Windows;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Enhanced Circuit Edit Window for PowerBIM 6
    /// The most critical form where users perform electrical calculations
    /// </summary>
    public partial class CircuitEditWindow : Window
    {
        private CircuitEditViewModel _viewModel;

        public CircuitEditWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Initialize the window with distribution board data
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="distributionBoardData">Distribution board data</param>
        /// <param name="projectInfo">Project information</param>
        public void Initialize(IServiceProvider serviceProvider, PowerBIM_DBData distributionBoardData, PowerBIM_ProjectInfo projectInfo)
        {
            try
            {
                // Create and initialize ViewModel
                _viewModel = serviceProvider.GetRequiredService<CircuitEditViewModel>();
                _viewModel.Initialize(distributionBoardData, projectInfo);

                // Set DataContext
                DataContext = _viewModel;

                // Subscribe to close request
                _viewModel.RequestClose += OnRequestClose;

                // Set window title
                Title = $"Live Circuit Editor: {distributionBoardData.Schedule_DB_Name}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize Circuit Edit window: {ex.Message}",
                               "Initialization Error",
                               MessageBoxButton.OK,
                               MessageBoxImage.Error);
                Close();
            }
        }

        /// <summary>
        /// Handle close request from ViewModel
        /// </summary>
        private void OnRequestClose()
        {
            Close();
        }

        /// <summary>
        /// Handle window closing
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.RequestClose -= OnRequestClose;
            }
            base.OnClosed(e);
        }
    }
}
