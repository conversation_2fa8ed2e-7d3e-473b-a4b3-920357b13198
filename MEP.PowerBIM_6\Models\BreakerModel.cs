using System;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a PowerBIM Breaker for WPF binding
    /// Wraps PowerBIM_BreakerData with ObservableObject for MVVM
    /// </summary>
    public partial class BreakerModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_BreakerData _originalData;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Schedule protective device type
        /// </summary>
        [ObservableProperty]
        private string _scheduleProtectiveDevice = string.Empty;

        /// <summary>
        /// Schedule curve type
        /// </summary>
        [ObservableProperty]
        private string _scheduleCurveType = string.Empty;

        /// <summary>
        /// Schedule trip rating
        /// </summary>
        [ObservableProperty]
        private double _scheduleTripRating;

        /// <summary>
        /// Minimum kA rating
        /// </summary>
        [ObservableProperty]
        private double _minKaRating;

        /// <summary>
        /// kA rating used
        /// </summary>
        [ObservableProperty]
        private double _kaRatingUsed;

        /// <summary>
        /// I2t Phase value
        /// </summary>
        [ObservableProperty]
        private double _i2tPhase;

        /// <summary>
        /// I2t Earth value
        /// </summary>
        [ObservableProperty]
        private double _i2tEarth;

        /// <summary>
        /// kA Earth value
        /// </summary>
        [ObservableProperty]
        private double _kaEarth;

        /// <summary>
        /// EFLI 0.4 second value
        /// </summary>
        [ObservableProperty]
        private double _efli04;

        /// <summary>
        /// EFLI 5.0 second value
        /// </summary>
        [ObservableProperty]
        private double _efli50;

        /// <summary>
        /// Maximum EFLI value
        /// </summary>
        [ObservableProperty]
        private double _efliMax;

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        [ObservableProperty]
        private bool _dataGood;

        /// <summary>
        /// Indicates if parameters are good
        /// </summary>
        [ObservableProperty]
        private bool _parametersGood;

        /// <summary>
        /// Indicates if values are missing
        /// </summary>
        [ObservableProperty]
        private bool _valuesMissing;

        /// <summary>
        /// Error message
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        /// <summary>
        /// Indicates if this is a spare or space
        /// </summary>
        [ObservableProperty]
        private bool _isSpareOrSpace;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_BreakerData
        /// </summary>
        public PowerBIM_BreakerData OriginalData => _originalData;

        /// <summary>
        /// Indicates if the breaker data is valid
        /// </summary>
        public bool IsValid => DataGood && ParametersGood && !ValuesMissing;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the BreakerModel with PowerBIM_BreakerData
        /// </summary>
        /// <param name="breakerData">Original breaker data</param>
        public BreakerModel(PowerBIM_BreakerData breakerData)
        {
            _originalData = breakerData ?? throw new ArgumentNullException(nameof(breakerData));
            
            // Initialize properties from original data
            LoadFromOriginalData();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_BreakerData
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                ScheduleProtectiveDevice = _originalData.Schedule_Protective_Device ?? string.Empty;
                ScheduleCurveType = _originalData.Schedule_Curve_Type ?? string.Empty;
                ScheduleTripRating = _originalData.Schedule_Trip_Rating;
                MinKaRating = _originalData.Min_kA_Rating;
                KaRatingUsed = _originalData.kA_Rating_Used;
                I2tPhase = _originalData.I2t_Phase;
                I2tEarth = _originalData.I2t_Earth;
                KaEarth = _originalData.kA_Earth;
                Efli04 = _originalData.EFLI_04;
                Efli50 = _originalData.EFLI_50;
                EfliMax = _originalData.EFLI_Max;
                DataGood = _originalData.Data_Good;
                ParametersGood = _originalData.Parameters_Good;
                ValuesMissing = _originalData.Values_Missing;
                ErrorMessage = _originalData.Error_Message ?? string.Empty;
                IsSpareOrSpace = _originalData.Is_Spare_Or_Space;
                
                // Update validation status
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_BreakerData
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.Schedule_Protective_Device = ScheduleProtectiveDevice;
                _originalData.Schedule_Curve_Type = ScheduleCurveType;
                _originalData.Schedule_Trip_Rating = ScheduleTripRating;
                _originalData.Min_kA_Rating = MinKaRating;
                _originalData.kA_Rating_Used = KaRatingUsed;
                _originalData.I2t_Phase = I2tPhase;
                _originalData.I2t_Earth = I2tEarth;
                _originalData.kA_Earth = KaEarth;
                _originalData.EFLI_04 = Efli04;
                _originalData.EFLI_50 = Efli50;
                _originalData.EFLI_Max = EfliMax;
                _originalData.Data_Good = DataGood;
                _originalData.Parameters_Good = ParametersGood;
                _originalData.Values_Missing = ValuesMissing;
                _originalData.Error_Message = ErrorMessage;
                _originalData.Is_Spare_Or_Space = IsSpareOrSpace;
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        /// <summary>
        /// Find maximum EFLI value
        /// </summary>
        /// <param name="clearingTimeIs5Sec">Whether clearing time is 5 seconds</param>
        /// <returns>Maximum EFLI value</returns>
        public double FindEfliMax(bool clearingTimeIs5Sec)
        {
            try
            {
                var result = _originalData?.Find_EFLIMax(clearingTimeIs5Sec) ?? 0;
                EfliMax = result;
                return result;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error calculating EFLI Max: {ex.Message}";
                HasErrors = true;
                return 0;
            }
        }

        /// <summary>
        /// Find maximum short circuit withstand
        /// </summary>
        /// <returns>Maximum SC withstand value</returns>
        public double FindMaxScWithstand()
        {
            try
            {
                return _originalData?.Find_MaxSCWithstand() ?? 0;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error calculating Max SC Withstand: {ex.Message}";
                HasErrors = true;
                return 0;
            }
        }

        /// <summary>
        /// Commit all breaker data to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool CommitAllBreakerDataToRevit()
        {
            try
            {
                SaveToOriginalData();
                var result = _originalData?.Commit_AllBreakerDataToRevit() ?? false;
                if (!result)
                {
                    ErrorMessage = "Failed to commit breaker data to Revit";
                    HasErrors = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error committing to Revit: {ex.Message}";
                HasErrors = true;
                return false;
            }
        }

        /// <summary>
        /// Commit breaker rating only to Revit
        /// </summary>
        /// <returns>True if successful</returns>
        public bool CommitBreakerRatingOnlyToRevit()
        {
            try
            {
                SaveToOriginalData();
                var result = _originalData?.Commit_BreakerRatingOnlyToRevit() ?? false;
                if (!result)
                {
                    ErrorMessage = "Failed to commit breaker rating to Revit";
                    HasErrors = true;
                }
                return result;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error committing rating to Revit: {ex.Message}";
                HasErrors = true;
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validate the breaker data
        /// </summary>
        private void ValidateData()
        {
            try
            {
                HasErrors = !DataGood || !ParametersGood || ValuesMissing || !string.IsNullOrEmpty(ErrorMessage);
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle DataGood property change
        /// </summary>
        partial void OnDataGoodChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        /// <summary>
        /// Handle ParametersGood property change
        /// </summary>
        partial void OnParametersGoodChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        /// <summary>
        /// Handle ValuesMissing property change
        /// </summary>
        partial void OnValuesMissingChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        #endregion
    }
}
