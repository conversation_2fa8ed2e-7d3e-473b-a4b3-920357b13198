﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Common.Utilities;
using MEP.PowerBIM_5.CoreLogic;
using Autodesk.Revit.UI;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_Export : BecaBaseForm
    {
        private PowerBIM_ProjectInfo projInfo;
        private List<PowerBIM_DBData> DBs;
        private PowerBIM_ExcelExport ExcelExport;
        private PowerBIM_CSVExport CSVExport;

        public frmPowerBIM_Export(PowerBIM_ProjectInfo pi, List<PowerBIM_DBData> SelectedDBs)
        {
            projInfo = pi;
            DBs = SelectedDBs;
            ExcelExport = new PowerBIM_ExcelExport(projInfo);
            CSVExport = new PowerBIM_CSVExport(projInfo);

            // show form
            InitializeComponent();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cb_ExportCircuitsPathImages_CheckedChanged(object sender, EventArgs e)
        {
            if (cb_ExportCircuitsPathImages.Checked)
            {
                this.Enabled = false;
                TaskDialogResult result = TaskDialog.Show(
                   "Confirm Image Export",
                   "Rendering Circuit Path images needs to happen before export and may take some time. Would you like to continue?",
                   TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No);

                if (result == TaskDialogResult.Yes)
                {
                    cbExportExcelVerificationSchedule.Enabled = false;
                    ModelessPowerBIM_StartFormHandler.RequestExportCircuitPathImages();
                }
                else
                {
                    cb_ExportCircuitsPathImages.Checked = false;
                }
                this.Enabled = true;
            }
            else
            {
                cbExportExcelVerificationSchedule.Enabled = true;
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            // Check that something was selected
            if (!cbExportCSVSummary.Checked && !cbExportCSVVerification.Checked && !cbExportExcelDBSchedule.Checked && !cbExportExcelVerificationSchedule.Checked)
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning", "You need to select at least one export method!");
                return;
            }
            

            // Run the selected export processes.
            if (cbExportCSVSummary.Checked)
                Export_CSVSummary();

            if (cbExportCSVVerification.Checked)
                Export_CSVVerification();

            if (cbExportExcelDBSchedule.Checked)
                Export_ExcelSchedule();

            if (cbExportExcelVerificationSchedule.Checked)
                Export_ExcelVerification();

            // Open folder location is export is complete
            if (cbExportCSVSummary.Checked || cbExportCSVVerification.Checked || cbExportExcelDBSchedule.Checked || cbExportExcelVerificationSchedule.Checked)
                DirectoryUtility.OpenFolder(projInfo.Folder_Path);

            this.Close();
        }

        private void cbExportExcelVerificationSchedule_CheckedChanged(object sender, EventArgs e)
        {
            cb_ExportCircuitsPathImages.Enabled = cbExportExcelVerificationSchedule.Checked;
        }

        #region Private Methods

        private void Export_CSVSummary()
        {
            CSVExport.Export_CSV(DBs, false);
        }

        private void Export_CSVVerification()
        {
            if (MessageBox.Show("WARNING: This will export PowerBIM calculation parameters that are not stored into Revit.\n \n" +
                "Make sure you have run PowerBIM 'Run Circuit Check (Manual)' or 'Circuit Editor (Live)' before proceeding", "Info", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                CSVExport.Export_CSV(DBs, true);
            }
        }
        private void Export_ExcelSchedule()
        {
            ExcelExport.Export_DBSCheduleWorkbook(DBs, false, false);
        }

        private void Export_ExcelVerification()
        {
            ExcelExport.Export_DBSCheduleWorkbook(DBs, true, cb_ExportCircuitsPathImages.Checked);
        }

        

        private void cbExportExcelVerificationSchedule_CheckedChanged_1(object sender, EventArgs e)
        {
            
            cb_ExportCircuitsPathImages.Enabled = cbExportExcelVerificationSchedule.Checked;
        }
        #endregion


    }
}