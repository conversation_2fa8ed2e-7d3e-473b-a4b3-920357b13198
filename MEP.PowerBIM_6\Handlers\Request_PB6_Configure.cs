using System;
using System.Threading;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enumeration of all request types for PowerBIM 6 WPF application
    /// Based on the original RequestId from MEP.PowerBIM_1.5 but enhanced for WPF MVVM
    /// </summary>
    public enum RequestId_PB6 : int
    {
        None = 0,

        // Project Operations
        SaveProject = 1,
        SaveSettings = 2,
        CommitProjectInfo = 3,
        LoadProjectInfo = 4,

        // Distribution Board Operations
        LoadDistributionBoards = 10,
        SaveDistributionBoard = 11,
        ImportDistributionBoardSettings = 12,
        RefreshDistributionBoardSummary = 13,
        UpdaterRequired_All = 14,

        // Circuit Operations
        UpdateCircuits = 20,
        SaveCircuitData = 21,
        RecalculateCircuits = 22,
        RecalculateAllCircuits = 23,
        BulkEditLighting = 24,
        BulkEditPower = 25,
        BulkEditOther = 26,
        RunCircuitCheckManual = 27,
        AutoCalc = 28,

        // Circuit Editing (Enhanced)
        SaveCircuitEditEnhanced = 30,
        RecalcAndRefreshCircuitToForm = 31,
        RecalcAndRefreshAllCircuitsToForm = 32,
        RecalcAndRefreshLengthToFormClick = 33,
        Refresh_DerrivedCircuitProperties = 34,
        WriteLengthAfterUserInputToCircuitParameter = 35,

        // Path Editing Operations
        OpenPathCustomizing = 40,
        OpenPathCustomizingForDB = 41,
        ActivatePathEditView = 42,
        CheckViewCreation = 43,
        SetCircuitLengthManual = 44,
        SetDbLengthManual = 45,
        SetFirstLengthManual = 46,
        SetTotalLengthManual = 47,
        RecalcAndRefreshLengthToDbForm = 48,

        // Export Operations
        ExportData = 50,
        ExportCircuitImages = 51,
        ExportCircuitPathImages = 52,

        // Import Operations
        ImportFromCsv = 60,
        ProcessImportSettings = 61,

        // Advanced Settings
        CommitAdvancedSettings = 70,
        CommitDistributionBoardSettings = 71,
        RevertToOldSave = 72,

        // UI Operations
        WakeFormUp = 80,
        WakeFormUpDistributionBoardEdit = 81,
        WakeFormUpCircuitEditEnhanced = 82,
        WakeFormUpStartForm = 83,
        RefreshData = 84,

        // Manual Operations
        SetManualLock = 90,
        SaveUserNotes = 91,

        // Initialization
        InitializeAllCircuits = 100
    }

    /// <summary>
    /// Thread-safe request configuration class for PowerBIM 6
    /// Manages request queuing between WPF UI and Revit API via ExternalEvent
    /// </summary>
    public class Request_PB6_Configure
    {
        #region Fields
        
        // Storing the value as a plain Int makes using the interlocking mechanism simpler
        private int _request = (int)RequestId_PB6.None;

        #endregion

        #region Public Methods

        /// <summary>
        /// Make a request for the given request ID
        /// </summary>
        /// <param name="request">The request to make</param>
        public void Make(RequestId_PB6 request)
        {
            Interlocked.Exchange(ref _request, (int)request);
        }

        /// <summary>
        /// Take the current request and reset to None
        /// This is called by the RequestHandler to process the request
        /// </summary>
        /// <returns>The current request ID</returns>
        public RequestId_PB6 Take()
        {
            return (RequestId_PB6)Interlocked.Exchange(ref _request, (int)RequestId_PB6.None);
        }

        /// <summary>
        /// Check if there is a pending request without taking it
        /// </summary>
        /// <returns>True if there is a pending request</returns>
        public bool HasPendingRequest()
        {
            return _request != (int)RequestId_PB6.None;
        }

        /// <summary>
        /// Get the current request without taking it
        /// </summary>
        /// <returns>The current request ID</returns>
        public RequestId_PB6 Peek()
        {
            return (RequestId_PB6)_request;
        }

        #endregion
    }

    /// <summary>
    /// Data container for requests that need additional context
    /// Used for complex operations that require specific data
    /// </summary>
    public class PendingData_PB6
    {
        public RequestId_PB6 RequestId { get; set; }
        public object Data { get; set; }
        public string Context { get; set; }
        public DateTime Timestamp { get; set; }

        public PendingData_PB6(RequestId_PB6 requestId)
        {
            RequestId = requestId;
            Timestamp = DateTime.Now;
        }

        public PendingData_PB6(RequestId_PB6 requestId, object data, string context = null)
        {
            RequestId = requestId;
            Data = data;
            Context = context;
            Timestamp = DateTime.Now;
        }
    }
}
