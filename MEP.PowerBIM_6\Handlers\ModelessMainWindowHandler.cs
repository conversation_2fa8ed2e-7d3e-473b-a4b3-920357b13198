using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.ViewModels;
using MEP.PowerBIM_6.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace MEP.PowerBIM_6.Handlers
{
    /// <summary>
    /// Enhanced modeless window handler for PowerBIM 6 WPF application
    /// Manages the lifecycle of the main modeless window and provides cross-form communication
    /// Maintains the proven ExternalEvent architecture while adding modern dependency injection
    /// </summary>
    public static class ModelessMainWindowHandler
    {
        #region Fields

        private static MainWindow _mainWindow;
        private static RequestHandler_PB6 _requestHandler;
        private static ExternalEvent _externalEvent;
        private static IServiceProvider _serviceProvider;
        private static ILogger _logger;

        // Static references for cross-form communication (preserving original pattern)
        public static PowerBIM_ProjectInfo ProjectInfo { get; private set; }
        public static List<PowerBIM_DBData> AllDatabases { get; private set; }
        public static BecaActivityLoggerData ActivityLogger { get; private set; }

        #endregion

        #region Properties

        /// <summary>
        /// Indicates if the main window is currently open and active
        /// </summary>
        public static bool IsWindowOpen => _mainWindow != null && _mainWindow.IsLoaded;

        /// <summary>
        /// Get the current service provider for dependency injection
        /// </summary>
        public static IServiceProvider ServiceProvider => _serviceProvider;

        #endregion

        #region Public Methods

        /// <summary>
        /// Show the main PowerBIM window (singleton pattern)
        /// This is the main entry point called from PowerBIM_6_Command
        /// </summary>
        /// <param name="dbs">List of databases from PowerBIM core logic</param>
        /// <param name="projInfo">Project information from PowerBIM core logic</param>
        /// <param name="logger">Activity logger for tracking operations</param>
        public static void ShowWindow(List<PowerBIM_DBData> dbs, PowerBIM_ProjectInfo projInfo, BecaActivityLoggerData logger)
        {
            try
            {
                // Store references for cross-form communication
                AllDatabases = dbs ?? throw new ArgumentNullException(nameof(dbs));
                ProjectInfo = projInfo ?? throw new ArgumentNullException(nameof(projInfo));
                ActivityLogger = logger ?? throw new ArgumentNullException(nameof(logger));

                // Singleton pattern - only one main window allowed
                if (_mainWindow == null || !_mainWindow.IsLoaded)
                {
                    InitializeServices();
                    CreateModelessArchitecture();
                    CreateMainWindow();
                }
                else
                {
                    // Bring existing window to front
                    _mainWindow.Activate();
                    _mainWindow.WindowState = WindowState.Normal;
                    _logger?.LogInformation("PowerBIM main window activated");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to show PowerBIM main window");
                ShowErrorMessage("Failed to open PowerBIM", ex.Message);
            }
        }

        /// <summary>
        /// Close the main window and clean up resources
        /// </summary>
        public static void CloseWindow()
        {
            try
            {
                _logger?.LogInformation("Closing PowerBIM main window");

                // Close and dispose main window
                _mainWindow?.Close();
                _mainWindow = null;

                // Dispose ExternalEvent
                _externalEvent?.Dispose();
                _externalEvent = null;

                // Clear references
                _requestHandler = null;
                _serviceProvider = null;
                ProjectInfo = null;
                AllDatabases = null;
                ActivityLogger = null;

                _logger?.LogInformation("PowerBIM main window closed successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error closing PowerBIM main window");
            }
        }

        /// <summary>
        /// Show database edit window as modal dialog
        /// Preserves the original form-to-form communication pattern
        /// </summary>
        /// <param name="database">Database to edit</param>
        public static void ShowDbEditWindow(object database)
        {
            try
            {
                if (_serviceProvider == null)
                {
                    _logger?.LogError("Service provider not initialized");
                    return;
                }

                var dbEditViewModel = _serviceProvider.GetRequiredService<DbEditViewModel>();
                dbEditViewModel.Initialize(_requestHandler, _externalEvent);
                // TODO: Initialize with database data

                var dbEditWindow = new DbEditWindow 
                { 
                    DataContext = dbEditViewModel,
                    Owner = _mainWindow
                };

                _logger?.LogInformation("Showing database edit window");
                dbEditWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to show database edit window");
                ShowErrorMessage("Error", "Failed to open database editor");
            }
        }

        /// <summary>
        /// Show circuit edit window as modal dialog
        /// Preserves the original form-to-form communication pattern
        /// </summary>
        /// <param name="database">Database containing circuits to edit</param>
        public static void ShowCircuitEditWindow(object database)
        {
            try
            {
                if (_serviceProvider == null)
                {
                    _logger?.LogError("Service provider not initialized");
                    return;
                }

                if (database is not PowerBIM_DBData distributionBoardData)
                {
                    _logger?.LogError("Invalid database type for circuit edit window");
                    ShowErrorMessage("Error", "Invalid distribution board data");
                    return;
                }

                if (ProjectInfo == null)
                {
                    _logger?.LogError("Project info not available for circuit edit window");
                    ShowErrorMessage("Error", "Project information not available");
                    return;
                }

                // Check if DB is locked
                if (distributionBoardData.IsManuallyLocked)
                {
                    ShowInfoMessage("Distribution Board Locked",
                        $"Distribution Board '{distributionBoardData.Schedule_DB_Name}' is currently locked and cannot be edited.");
                    return;
                }

                var circuitEditWindow = new CircuitEditWindow
                {
                    Owner = _mainWindow,
                    WindowState = WindowState.Maximized
                };

                // Initialize the window with proper data
                circuitEditWindow.Initialize(_serviceProvider, distributionBoardData, ProjectInfo);

                _logger?.LogInformation($"Showing circuit edit window for DB: {distributionBoardData.Schedule_DB_Name}");
                circuitEditWindow.ShowDialog();

                // After closing, update the main window data (similar to original WinForms behavior)
                RefreshMainWindowData();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to show circuit edit window");
                ShowErrorMessage("Error", $"Failed to open circuit editor: {ex.Message}");
            }
        }

        /// <summary>
        /// Show advanced settings window
        /// </summary>
        public static void ShowAdvancedSettingsWindow()
        {
            try
            {
                if (_serviceProvider == null) return;

                var settingsViewModel = _serviceProvider.GetRequiredService<AdvancedSettingsViewModel>();
                settingsViewModel.Initialize(_requestHandler, _externalEvent);

                var settingsWindow = new AdvancedSettingsWindow 
                { 
                    DataContext = settingsViewModel,
                    Owner = _mainWindow
                };

                _logger?.LogInformation("Showing advanced settings window");
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to show advanced settings window");
                ShowErrorMessage("Error", "Failed to open settings");
            }
        }

        /// <summary>
        /// Show export window
        /// </summary>
        public static void ShowExportWindow()
        {
            try
            {
                if (_serviceProvider == null) return;

                var exportViewModel = _serviceProvider.GetRequiredService<ExportViewModel>();
                exportViewModel.Initialize(_requestHandler, _externalEvent);

                var exportWindow = new ExportWindow 
                { 
                    DataContext = exportViewModel,
                    Owner = _mainWindow
                };

                _logger?.LogInformation("Showing export window");
                exportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to show export window");
                ShowErrorMessage("Error", "Failed to open export dialog");
            }
        }

        /// <summary>
        /// Wake up the main window after ExternalEvent processing
        /// Called by RequestHandler_PB6 to update UI state
        /// </summary>
        public static void WakeUpMainWindow()
        {
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (_mainWindow?.DataContext is MainViewModel mainViewModel)
                    {
                        mainViewModel.WakeUp();
                        _logger?.LogDebug("Main window woken up");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to wake up main window");
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initialize the dependency injection services
        /// </summary>
        private static void InitializeServices()
        {
            try
            {
                _serviceProvider = ServiceConfiguration.BuildServiceProvider(ProjectInfo.UIDocument, ActivityLogger);
                _logger = _serviceProvider.GetService<ILogger<ModelessMainWindowHandler>>();
                _logger?.LogInformation("Services initialized successfully");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to initialize services", ex);
            }
        }

        /// <summary>
        /// Create the modeless architecture components
        /// </summary>
        private static void CreateModelessArchitecture()
        {
            try
            {
                _requestHandler = new RequestHandler_PB6(_serviceProvider, ActivityLogger);
                _externalEvent = ExternalEvent.Create(_requestHandler);
                _logger?.LogInformation("Modeless architecture created successfully");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create modeless architecture", ex);
            }
        }

        /// <summary>
        /// Create and show the main window
        /// </summary>
        private static void CreateMainWindow()
        {
            try
            {
                var mainViewModel = _serviceProvider.GetRequiredService<MainViewModel>();
                mainViewModel.InitializeWithData(AllDatabases, ProjectInfo, _requestHandler, _externalEvent);

                _mainWindow = new MainWindow 
                { 
                    DataContext = mainViewModel,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                // Handle window closing
                _mainWindow.Closed += OnMainWindowClosed;

                _mainWindow.Show();
                _logger?.LogInformation("Main window created and shown successfully");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to create main window", ex);
            }
        }

        /// <summary>
        /// Handle main window closed event
        /// </summary>
        private static void OnMainWindowClosed(object sender, EventArgs e)
        {
            CloseWindow();
        }

        /// <summary>
        /// Show error message to user
        /// </summary>
        private static void ShowErrorMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Error: {title} - {message}");
            }
        }

        /// <summary>
        /// Show info message to user
        /// </summary>
        /// <param name="title">Message title</param>
        /// <param name="message">Message content</param>
        private static void ShowInfoMessage(string title, string message)
        {
            try
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Info: {title} - {message}");
            }
        }

        /// <summary>
        /// Refresh main window data after circuit editing
        /// Mimics the behavior from original WinForms implementation
        /// </summary>
        private static void RefreshMainWindowData()
        {
            try
            {
                if (_mainWindow?.DataContext is MainViewModel mainViewModel)
                {
                    // Refresh distribution board data
                    mainViewModel.RefreshDistributionBoards();
                    _logger?.LogInformation("Main window data refreshed after circuit edit");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to refresh main window data");
            }
        }

        #endregion
    }
}

// Note: RequestHandler_PB6 will be implemented in a separate file
