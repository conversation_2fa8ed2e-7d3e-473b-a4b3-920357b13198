﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Common.Utilities;

namespace MEP.PowerBIM_5.CoreLogic
{
    public static class PowerBIM_Constants
    {
        //
        // PowerBIM_Constants
        //
        // A class for all of the constant fields associated with BecaLPD and the methods that relate to them.
        //
        //

        #region Fields

        // Parameter Names.
        public const string BecaElectricalEngineer = "Beca_Electrical_Engineer";
        public const string BecaElectricalVerifier = "Beca_Electrical_Verifier";
        public const string BecaAuBuildingClass = "Beca_Au_Building_Class";
        public const string BecaTypeMark = "Beca Type Mark";
        public const string BecaApparentPower = "Beca Apparent Power";

        // Contant Strings
        public const string Copyright = "Copyright(C) 2024 Beca Ltd"; // TODO: Make year dynamic
        public const string CableDatabaseName = "CableDatabaseNZandAUrev6.xlsx";
        public const string DatabaseName_SCWithstand = "SC Withstand Simplified.csv";
        public const string Default_CableDatabase = "Cable Database NZ";

        public static string Default_InputPath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "PowerBIM Database Files - Version 6 Release");
        public static string SharedParameterPath = Path.Combine(Path.GetFullPath(Path.Combine(System.Reflection.Assembly.GetExecutingAssembly().Location, @"../../../../")),
            "Addins", "MEP Tools", "_Shared Parameters");
        public static string Default_CableDatabaseLocation = Default_InputPath;
        public static string Default_DBTemplatePath = Default_InputPath + "\\PowerBIM DB Schedule Template.xlsm";
        public static string Default_DBTemplatePath_MacrosDisabled = Default_InputPath + "\\PowerBIM DB Schedule Template.xlsx";

        public static int Length_IsInternalThreshold = 500;

        //Cable instation method
        public static string[] strInstallMethodsList = new string[11];

        //Breaker Spec
        public static string[] strCPDRangeList = new string[4];

        //Cable Data Range on the Spreadsheet
        public static int file_CableRowsMax = 158;
        public static int file_CableColumnsMax = 45;
        public static int file_CableRowStart = 5;
        public static int file_CableColumnStart = 1;

        public static int file_EFLIRowsMax = 14;
        public static int file_EFLIColumnsMax = 9;
        public static int file_EFLIRowStart = 9;
        public static int file_EFLIColumnStart = 1;

        public static int file_PowerCADRowsMax = 500;
        public static int file_PowerCADColumnsMax = 43; //WAS 42! Changed for PowerCAD compat
        public static int file_PowerCADRowStart = 4;
        public static int file_PowerCADColumnStart = 1;

        // Excel export constants
        // Instructions header info
        public const int Excel_HeaderInfoCol = 3;
        public const int Excel_JobNameRow = 19;
        public const int Excel_JobNumberRow = 20;
        public const int Excel_JobDateRow = 21;
        public const int Excel_EngineerRow = 22;
        public const int Excel_VerifierRow = 23;
        public const int Excel_RevisionRow = 25;

        public static int Excel_DB_StartRow = 13;
        public static int Excel_DB_EndRow = 156;

        // Excel PB Settings sheet constants
        public const int Excel_PBSettingsHeaderInfoCol = 4;
        public const int Excel_SystemMaxVoltDrop = 7;
        public const int Excel_DiscriminationTest = 8;
        public const int Excel_CableDatabase = 9;
        public const int Excel_ProtectiveDeviceProduct = 10;
        public const int Excel_VoltDropCalculation_Lighting = 14;
        public const int Excel_VoltDropCalculation_Power = 15;
        public const int Excel_VoltDropCalculation_Other = 16;
        public const int Excel_GPOSettings = 17;
        public const int Excel_LightingCircuitClearingTimes = 18;
        public const int Excel_NonGPOPowerCircuitClearingTimes = 19;
        public const int Excel_ExtraLengthPerCircuitEnabled = 21;
        public const int Excel_ExtraLengthPerCircuitLighting = 22;
        public const int Excel_ExtraLengthPerCircuitPower = 23;
        public const int Excel_ExtraLengthPerCircuitOther = 24;
        public const int Excel_ExtraLengthPerElementEnabled = 25;
        public const int Excel_ExtraLengthPerElementLighting = 26;
        public const int Excel_ExtraLengthPerElementPower = 27;
        public const int Excel_ExtraLengthPerElementOther = 28;
        public const int Excel_PathNodeVsElementIntersection = 30;
        public const int Excel_CableDatabaseLocation = 31;
        public const int Excel_JobName = 34;
        public const int Excel_JobNumber = 35;
        public const int Excel_Date = 36;
        public const int Excel_Engineer = 37;
        public const int Excel_Verifier = 38;
        public const int Excel_Revision = 39;

        // Excel summary sheet constants
        public static int Excel_Summary_StartRow = 15;
        public static int Excel_Summary_DBNameCol = 2;
        public static int Excel_Summary_CCTPassCol = 15;
        public static int Excel_Summary_CCTFailCol = 16;
        public static int Excel_Summary_CCTWarningCol = 17;

        // Min header info
        public static int Excel_HeaderCol_1 = 4;
        public static int Excel_HeaderCol_2 = 10;
        public static int Excel_HeaderCol_3 = 16;
        public static int Excel_HeaderCol_4 = 24;
        public static int Excel_NumberWaysCol = 14;

        public static int Excel_HeaderRow_SwitchRating = 5;
        public static int Excel_HeaderRow_FeederCable = 6;
        public static int Excel_HeaderRow_Location = 7;
        public static int Excel_HeaderRow_Seismic = 8;
        public static int Excel_HeaderRow_FaultRating = 9;

        public static int Excel_HeaderRow_FormRating = 5;
        public static int Excel_HeaderRow_BusFault = 6;
        public static int Excel_HeaderRow_Surge = 7;
        public static int Excel_HeaderRow_Metering = 8;
        public static int Excel_HeaderRow_MaxVD = 9;

        public static int Excel_NumberWaysRow = 2;

        // Circuit data columns
        public static string Excel_Col_CCTNumber = "A";
        public static string Excel_Col_CCTTripRating = "B";
        public static string Excel_Col_CCTCurveType = "C";
        public static string Excel_Col_CCTProtectiveDevice = "D";
        public static string Excel_Col_CCTRCD = "E";
        public static string Excel_Col_CCTOtherCOntrols = "F";
        public static string Excel_Col_CCTCable1 = "G";
        public static string Excel_Col_CCTCable2 = "H";
        public static string Excel_Col_CCTDesignLength = "I";
        public static string Excel_Col_CCTDescription = "J";
        public static string Excel_Col_CCTRevision = "K";
        public static string Excel_Col_CCTImage = "L";

        // Circuit verification columns
        public static string Excel_Col_CCT_CheckTripRating = "N";
        public static string Excel_Col_CCT_CheckCable1Valid = "O";
        public static string Excel_Col_CCT_CheckCable2Valid = "P";
        public static string Excel_Col_CCT_CheckCPDDiscrim = "Q";
        public static string Excel_Col_CCT_CheckLoadCurrent = "R";
        public static string Excel_Col_CCT_CheckCable1Current = "S";
        public static string Excel_Col_CCT_CheckCable2Current = "T";
        public static string Excel_Col_CCT_CheckEFLI = "U";
        public static string Excel_Col_CCT_CheckFinalVD = "V";
        public static string Excel_Col_CCT_CheckSystemVD = "W";
        public static string Excel_Col_CCT_CheckSC1 = "X";
        public static string Excel_Col_CCT_CheckSC2 = "Y";
        public static string Excel_Col_CCT_CheckSummary = "Z";

        // Extras
        public static string Excel_Col_CCTInstallMethod = "AC";
        public static string Excel_Col_CCTRevitPathMode = "AD";
        public static string Excel_Col_CCTLegnthFirst = "AE";
        public static string Excel_Col_CCTLengthTotal = "AF";
        public static string Excel_Col_CCTDerating = "AG";
        public static string Excel_Col_CCTDiversity = "AH";


        //TB SC withdtand added 7/20
        public static int file_KValueRowsMax = 17;
        public static int file_KValueColumnsMax = 9;
        public static int file_KValueRowStart = 8;
        public static int file_KValueColumnStart = 1;

        public static int file_MCBRowsMax = 14;
        public static int file_MCBColumnsMax = 76;
        public static int file_MCBRowStart = 11;
        public static int file_MCBColumnStart = 1;

        // CIRCUIT LENGTH CALC CONSTANTS
        public static double ExtraLengthPerCCT = 0;
        public static double ExtraLengthPerElem = 0;
        public static double ExtraLengthPerTermination = 2500;
        public static double ElementAtNodeTolerance = 30; // 30 cm

        //
        public static double TemperatureInKelvin = 273.15;
        public static double ft2m = 0.3048;

        //
        // AUTO CABLE SIZING SET POINTS
        //
        // Lighting
        public static int CableTryStart_Lighting = 1;
        public static int CableTryFinish_Lighting = 3;
        // Power 1 Phase
        public static int CableTryStart_Power1Phase = 2;
        public static int CableTryFinish_Power1Phase = 4;
        // Lighting 3 Phase
        public static int CableTryStart_Power3Phase = 26;
        public static int CableTryFinish_Power3Phase = 28;

        //
        // ADVANCED SETTINGS FORM DEFAULTS
        //
        //set defaults
        public static int Default_PBVDCalc = 2;
        public static int Default_PBVDCalcPower = 2;
        public static int Default_PBVDCalcLighting = 1;
        public static int Default_PBGPOCalc = 80;
        public static double Default_ClearingTimePower = 400;
        public static double Default_ClearingTimeLighting = 400;
        // Lengths
        public static double Default_Length_ExtraPerCCT_Lighting = 0.10;
        public static double Default_Length_ExtraPerElement_Lighting = 500;
        public static double Default_Length_ExtraPerCCT_Power = 0.10;
        public static double Default_Length_ExtraPerElement_Power = 2800;
        public static double Default_Length_ExtraPerCCT_Other = 0.15;
        public static double Default_Length_ExtraPerElement_Other = 1000;
        public static double Default_Length_PathTolerance = 300;

        //
        // PROJECT INFORMATION DEFAULTS
        //
        //
        public static double Default_SystemMaxVoltDrop = 0.05;
        public static double Default_AmbientTemp = 30;
        public static double Default_DiscriminationMultiplier = 2;

        public static int Default_LightingInstallMethod = 3;
        public static int Default_PowerInstallMethod = 3;
        public static int Default_OtherInstallMethod = 3;

        public static string Default_CPDRange = "Terasaki DinT";


        // Set up and read project wide global parameters
        public static Guid paramGuidAmbientTemp = Guid.Parse("f7d768a8-50ce-434f-854b-13069842d284");
        public static Guid paramGuidSystemMaxVDpc = Guid.Parse("f3060a98-5c96-48d8-ab7f-e4de5182625a");
        public static Guid paramGuidDiscrimTestMultiplier = Guid.Parse("946bee77-2a93-4066-9fd8-99467878e6bf");
        public static Guid paramGuidPBCPDProduct = Guid.Parse("2e329710-abb9-42f4-a946-aac94864caa6");
        public static Guid paramGuidPBCPDManufacturer = Guid.Parse("3086bf25-c12f-4b81-b371-921a31004295");
        public static Guid paramGuidPBVDCalcOther = Guid.Parse("438c1bca-305e-4f61-a986-15501b37fba8");
        public static Guid paramGuidPBVDCalcPower = Guid.Parse("2b24be35-5052-40cf-b9dc-666bfbc0cba7");
        public static Guid paramGuidPBVDCalcLighting = Guid.Parse("6287d968-b609-441f-a95f-e0a79436736e");
        public static Guid paramGuidPBGPOCalc = Guid.Parse("99c29cd0-0331-4d7f-bee6-47e5b2d91b9d");
        public static Guid paramGuidPBClearingTimePower = Guid.Parse("f37feff5-0f75-4e3b-8f77-fd5cd9069844");
        public static Guid paramGuidPBClearingTimeLighting = Guid.Parse("af98f1d9-c715-4b63-a9a8-7ea0ed33099f");
        //Lengths factors
        public static Guid paramGuidPBLength_ExtraPerCCT_Other = Guid.Parse("a3d687b1-63e2-413e-be18-8011c798c883");
        public static Guid paramGuidPBLength_ExtraPerElement_Other = Guid.Parse("319daabb-4c52-4ea5-9d0c-86f74be8b07c");
        public static Guid paramGuidPBLength_ExtraPerCCT_Lighting = Guid.Parse("cfdd1fcb-a119-4bcc-888a-9d0c30882a60");
        public static Guid paramGuidPBLength_ExtraPerElement_Lighting = Guid.Parse("94a70357-b69d-493e-b13f-fd1b1917d8b5");
        public static Guid paramGuidPBLength_ExtraPerCCT_Power = Guid.Parse("63902a4d-781a-411a-89b6-5cd9107a8409");
        public static Guid paramGuidPBLength_ExtraPerElement_Power = Guid.Parse("a152a410-1911-4db2-be6d-03ab980427d6");
        public static Guid paramGuidPBLength_Circuit_Length_Manual = Guid.Parse("46d130eb-285b-4f7e-9ad3-70ba6323a007");
        public static Guid paramGuidPB_Revit_Path_Tolerance = Guid.Parse("08dda3f9-b004-47ce-8940-3831492875a7");

        //Preparing all DB parameter GUIDs
        public static Guid paramGuidDBFeederCable = Guid.Parse("7754e8fc-c41b-4fa8-adc6-41d3ec116c8b");
        public static Guid paramGuidDBLocation = Guid.Parse("6610a2d5-a4de-403d-bb41-e3157a592a5b");
        public static Guid paramGuidDBSeismicCategory = Guid.Parse("2906ce5f-4e2c-4004-9095-c9868c649040");
        public static Guid paramGuidDBFormRating = Guid.Parse("06a94b73-d35c-4607-a62b-b742c15e2417");
        public static Guid paramGuidDBFaultLevel = Guid.Parse("52b70945-ad86-4d07-a4c9-8d6508b87c0d");
        public static Guid paramGuidDSurgeProtection = Guid.Parse("72ba6c9f-cf09-4700-84a6-1add143f0595");
        public static Guid paramGuidDBMetering = Guid.Parse("7d2a396d-9106-45d3-aa97-e7f0871190c7");
        public static Guid paramGuidDBFinalCCTmaxVDPC = Guid.Parse("d2262171-2e66-4b41-a3a2-409757666d0f");

        public static Guid paramGuidDBEFLiR = Guid.Parse("107e549b-a029-4bd9-9411-fd0d91c2ec1d");
        public static Guid paramGuidDBEFLiX = Guid.Parse("e5397c83-a115-4626-8b73-379cc8c12d87");
        public static Guid paramGuidDBVD = Guid.Parse("2d9755a6-db85-4261-b798-ad0e9e8db2e0");
        public static Guid paramGuidDBPSCC = Guid.Parse("1f387094-4fb5-4556-a60d-26339264d7c4");
        public static Guid paramGuidDBupstreamRating = Guid.Parse("c72b133b-9768-41cc-aefa-230b88ad4879");


        //Preparing all CIRCUIT parameter GUIDs
        public static Guid paramGuidCbl_1stElem = Guid.Parse("044da93b-f841-4a06-a8ef-c54044c4510c");
        public static Guid paramGuidCbl_RemainderElem = Guid.Parse("e8e0e875-e851-4cfb-928a-e3febea5eddf");
        public static Guid paramGuidLen_1stElem = Guid.Parse("5b0b425a-6062-46cc-82a5-c15548094944");
        public static Guid paramGuidLen_Total = Guid.Parse("c08fdd34-b570-4687-8942-0edc32701e19");
        public static Guid paramGuidProtCurveType = Guid.Parse("8443b7a1-f8b3-4f3a-b389-f0e51abf11e4");
        public static Guid paramGuidProtDevice = Guid.Parse("879bd521-6b12-4444-a240-d13ac23d3f00");
        public static Guid paramGuidCctChkResult = Guid.Parse("553b2561-2dcb-48d2-ac02-9d2a6570b43b");
        public static Guid paramGuidRCDProtection = Guid.Parse("5b7b7f52-5643-457d-aa38-59a790907560");
        public static Guid paramGuidOtherControls = Guid.Parse("0b62193d-f605-4cce-83ea-b16582279885");
        public static Guid paramGuidDeratingFactor = Guid.Parse("efcef86e-8cf0-447f-9692-9ff45c531ed1");
        public static Guid paramGuidIsManualLightSwitch = Guid.Parse("5cbb22b7-1f39-4c1c-b764-0da21eafddaf");
        public static Guid paramGuidCabInstall = Guid.Parse("45a18a55-4ccb-4319-8adb-4e37fb39c582");
        public static Guid paramGuidCircuitRevision = Guid.Parse("4a988e2e-a90a-45a3-ac9f-ec9a9cabffa3");

        public static Guid paramGuidChkDB = Guid.Parse("5b0662c8-c224-4396-acaf-a2a32c2fe7f4");
        public static Guid paramGuidChkCCT_Data = Guid.Parse("0c4bf6b8-348f-4bba-b7a7-1fd287978f18");
        public static Guid paramGuidChkCCT_CBL1 = Guid.Parse("b25ab253-96fa-489e-a494-3add9fa36bab");
        public static Guid paramGuidChkCCT_CBRM = Guid.Parse("5983ba92-ef5c-4857-9e65-45257243e4dd");
        public static Guid paramGuidChkCCT_Current1 = Guid.Parse("51911009-e3dd-46de-a0cd-a3f936e677d4");
        public static Guid paramGuidChkCCT_Current2 = Guid.Parse("40c4eb15-6220-49f6-b168-b73f1ddeb4af");
        public static Guid paramRevitCircuitManualCurrent = Guid.Parse("01c655eb-fa3b-4a30-ba4b-9d0c2de96e30");
        public static Guid paramRevitCircuitManualCurrentValue = Guid.Parse("77b78499-fb6b-463d-8b90-9eaacbedd754");


        public static Guid paramGuidChkCCT_Current3 = Guid.Parse("2050ebfc-c085-4ba9-a0ba-8fdf9397174c");
        public static Guid paramGuidChkCCT_Current4 = Guid.Parse("46892b2b-adba-4f3b-bbdb-dee192bf352a");
        public static Guid paramGuidChkCCT_EFLI = Guid.Parse("d26d45ea-fb59-428b-8329-34096e5b0459");
        public static Guid paramGuidChkCCT_VD1 = Guid.Parse("d8810cad-e5d7-4f34-a5bf-01e23884a74e");
        public static Guid paramGuidChkCCT_VD2 = Guid.Parse("*************-42b8-95fa-1ddfeef8f375");
        //[V1/4] SC withstand added
        public static Guid paramGuidChkCCT_SC1 = Guid.Parse("707b3057-bb0e-4a50-b237-613528a64165");
        public static Guid paramGuidChkCCT_SC2 = Guid.Parse("2730e039-068c-43fb-a245-ea726660d039");
        public static Guid paramGuidChkCCT_ResultAPI = Guid.Parse("81278895-c146-42b8-b770-73ad167ef01f");
        //[V1.4] Diversity Added
        public static Guid paramGuidDiversity = Guid.Parse("288cad67-2d8d-45a4-90cd-e44878c52152");
        public static Guid paramGuidDiversifiedTotalLoadR = Guid.Parse("af83bc38-19cd-4772-970d-9c756d1ae15d");
        public static Guid paramGuidDiversifiedTotalLoadW = Guid.Parse("ae468924-c8b2-41f0-96ed-b493474b12d8");
        public static Guid paramGuidDiversifiedTotalLoadB = Guid.Parse("22d37f7d-1748-458d-bf21-3cc4590d8deb");

        // Database Default Location
        public static Guid paramDatabaseLocation = Guid.Parse("6147e7ca-1e63-4b21-b50e-c42250fb3d1d");

        #endregion

        // This will be used to check with the guids, either guids or names might be changed in shared aprameter file (from experience)
        #region ParameterNames
        public static string PB_Current_ManualName = "Beca_PB_Current_Manual";
        public static string PB_CurrentName = "Beca_PB_Current";

        public static string PBVDCalcName = "Beca_PB_VD_Calc_Other";
        public static string PBVDCalcPowerName = "Beca_PB_VD_Calc_Power";
        public static string PBVDCalcLightingName = "Beca_PB_VD_Calc_Lighting";
        public static string PB_GPO_CalcName = "Beca_PB_GPO_Calc";
        public static string PBClearingTimeLightingName = "Beca_PB_Clearing_Time_Lighting";
        public static string PBClearingTimePowerName = "Beca_PB_Clearing_Time_Power";

        public static string PB_Additional_Length_Per_Circuit_LightingName = "Beca_PB_Additional_Length_Per_Circuit_Lighting";
        public static string PB_Additional_Length_Per_Elem_LightingName = "Beca_PB_Additional_Length_Per_Elem_Lighting";
        public static string PB_Additional_Length_Per_Circuit_PowerName = "Beca_PB_Additional_Length_Per_Circuit_Power";
        public static string PB_Additional_Length_Per_Elem_PowerName = "Beca_PB_Additional_Length_Per_Elem_Power";
        public static string PB_Additional_Length_Per_CircuitName = "Beca_PB_Additional_Length_Per_Circuit";
        public static string PB_Additional_Length_Per_ElemName = "Beca_PB_Additional_Length_Per_Elem";

        public static string PB_Revit_Path_ToleranceName = "Beca_PB_Revit_Path_Tolerance";
        public static string CPDRangeName = "Beca_CPD_Manufacturer";
        public static string SystemMaxVDpcName = "PB_Max_VD_Perc";
        public static string DiscrimTestMultiplierName = "Discrimination_Test_Multiplier";

        public static string MansSWratingName = "Mains";
        public static string DBFeederCableName = "Beca Feeder Cable Size";
        public static string DBLocationName = "Beca Location";
        public static string DBSeismicCategoryName = "Beca Siesmic Category";
        public static string DeviceFaultRatingName = "Short Circuit Rating";
        public static string DBFormRatingName = "Beca_DB_Form_Rating";
        public static string DBBusFaultLevelName = "Beca_DB_Bus_Fault_Level";
        public static string DBSurgeProtectionName = "Beca_DB_Surge_Protection";
        public static string DBMeteringName = "Beca_DB_Metering";
        public static string DBfinalCCTMaxVDPCName = "Beca DB Final Circuit Max VD";
        public static string DBEFLiRName = "Beca DB EFLiR";
        public static string DBEFLiXName = "Beca DB EFLiX";
        public static string DBVDName = "Beca DB VD";
        public static string DBPSCCName = "Beca DB PSCC";
        public static string UpstreamRatingName = "Beca DB Upstream Device Rating";
        public static string DBchkName = "Beca DB Chk";
        public static string RevitDiversifiedTotalPhaseCurrentRName = "Beca_Diversified_Amps_R";
        public static string RevitDiversifiedTotalPhaseCurrentWName = "Beca_Diversified_Amps_W";
        public static string RevitDiversifiedTotalPhaseCurrentBName = "Beca_Diversified_Amps_B";
        public static string PBLength_Beca_Circuit_Length_ManualName = "Beca_Circuit_Length_Manual";

        public static string PB_Database_LocationName = "Beca_PB_Database_Path";
        #endregion

        //
        // Constructors
        //
        // Note that as this is a static class the constructor just initialises the variables that can't otherwise be.
        //

        // ----- Setup Report Head -----
        public const string strCableSummaryReportHead = "Circuit Number" + "," + "Protective Device Rating" + "," + "Protective Curve Type" + "," + "Protective Device Type"
                + "," + "RCD Protection" + "," + "Other Controls / Interfaces" + "," + "Cable to First Element" + "," + "Cable to Remainder of Circuit" + ","
                + "Total Circuit Length" + "," + "Load Description" + "," + "Circuit Revision";

        // ----- Setup Report Head -----
        public const string strVerificationReportHead = "Circuit Number" + "," + "Protective Device Rating" + "," + "Protective Curve Type" + "," + "Protective Device Type"
            + "," + "RCD Protection" + "," + "Other Controls / Interfaces" + "," + "Cable to First Element" + "," + "Cable to Remainder of Circuit" + "," + "Length to First Element" + "," +
            "Total Circuit Length" + "," + "Installation Method" + "," + "Derating Factor" + "," + "Number of Poles" + "," + "Load Description"

            + ", ," + "Resistance(Active) Cable to First" + "," + "Resistance(Active) Cable to Remainder" + "," + "Resistance(Earth) Cable to First" + "," + "Resistance(Earth) Cable to Remainder"
            + "," + "Reactance(Active) Cable to First" + "," + "Reactance(Active) Cable to Remainder" + "," + "Reactance(Earth) Cable to First" + "," + "Reactance(Earth) Cable to Remainder" + "," + "Impedance(Active) Cable to First"
            + "," + "Impedance(Active) Cable to Remainder" + "," + "Impedance(Earth) Cable to First" + "," + "Impedance(Earth) Cable to Remainder"

            + ", ," + "Cable to First - Rated Current" + "," + "Cable to Remainder - Rated Current" + "," + "Load current" + ","
            + "Cable to First - Temperature" + "," + "Cable to Remainder - Temperature"

            + ", ," + "Max Allowable EFLI" + "," + "Calculated EFLI"

            + ", ," + "Calculated Volt Drop (V)" + "," + "Calculated Volt Drop (%)"

            + ", ," + "Pass/Fail" + "," + "Error / Warning Messages";


        public static readonly Dictionary<double, int> Standard_kARatings = new Dictionary<double, int>
        {
            { 6,  0 },
            { 10, 1 },
            { 15, 2 },
            { 20, 3 },
            { 25, 4 }
        };

        public static readonly Dictionary<string, int> Standard_CurveTypes = new Dictionary<string, int>
        {
            { "B", 0 },
            { "C", 1 },
            { "D", 2 },
            { "FUSE", 3 },
            { "", 4 }
        };

        public static readonly Dictionary<string, int> Standard_ProtectiveDevices = new Dictionary<string, int>
        {
            { "MCB", 1 },
            { "RCBO", 2 },
            { "RCCB", 3 },
            { "", 4 }
        };

        public static readonly Dictionary<string, int> Standard_RCDProtection = new Dictionary<string, int>
        {
            { "", 0 },
            { "10mA", 1 },
            { "30mA", 2 },
            { "Type AC", 3 },
            { "Type A", 4 },
            { "Type F", 5 },
            { "Type B", 6 },
            { "Local 10mA", 7 },
            { "Local 30mA", 8 }
        };

        public static readonly Dictionary<double, int> Standard_TripRatings = new Dictionary<double, int>
        {
            { 2, 0 },
            { 3, 1 },
            { 4, 2 },
            { 6, 3 },
            { 10, 4 },
            { 16, 5 },
            { 20, 6 },
            { 25, 7 },
            { 32, 8 },
            { 40, 9 },
            { 50, 10 },
            { 63, 11 },
            { 80, 12 },
            { 100, 13 },
            { 0, 14 }
        };

        public static readonly Dictionary<double, int> Standard_IsolatorRatings = new Dictionary<double, int>
        {
            { 10, 0 },
            { 16, 1 },
            { 20, 2 },
            { 32, 3 },
            { 35, 4 },
            { 40, 5 },
            { 50, 6 },
            { 55, 7 },
            { 63, 8 },
            { 75, 9 },
            { 80, 10 },
            { 100, 11 },
            { 150, 12 },
            { 200, 13 },
            { 300, 14 },
            { 400, 15 },
            { 500, 16 },
            { 630, 17 }
        };


        static PowerBIM_Constants()
        {
            //
            // Constants
            //
            // Static constructor to initialise the strings. Does not need to be called.
            //

            PowerBIM_Constants.strInstallMethodsList[0] = "";
            PowerBIM_Constants.strInstallMethodsList[1] = "1. Unenclosed Spaced";
            PowerBIM_Constants.strInstallMethodsList[2] = "2. Unenclosed Spaced from Surface";
            PowerBIM_Constants.strInstallMethodsList[3] = "3. Unenclosed Touching";
            PowerBIM_Constants.strInstallMethodsList[4] = "4. Unenclosed Exposed to Sun";
            PowerBIM_Constants.strInstallMethodsList[5] = "5. Enclosed";
            PowerBIM_Constants.strInstallMethodsList[6] = "6. Partial Thermal Insulation";
            PowerBIM_Constants.strInstallMethodsList[7] = "7. Complete Thermal Insulation";
            PowerBIM_Constants.strInstallMethodsList[8] = "8. Direct Buried";
            PowerBIM_Constants.strInstallMethodsList[9] = "9. Buried Shared Enclosure";
            PowerBIM_Constants.strInstallMethodsList[10] = "10. Buried Separate Enclosure";

            PowerBIM_Constants.strCPDRangeList[0] = "<Please Select>";
            PowerBIM_Constants.strCPDRangeList[1] = "Schneider iC60"; // PowerBIM_Constants.strCPDRangeList[1] = "Schneider iC60";
            PowerBIM_Constants.strCPDRangeList[2] = "Terasaki DinT";
            PowerBIM_Constants.strCPDRangeList[3] = "ABB";

        }
    }
}
