﻿namespace MEP.PowerBIM_5.UI.Forms
{
    partial class frmPowerBIM_Start
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rqSettings = new System.Windows.Forms.Button();
            this.btnWriteLighting = new System.Windows.Forms.Button();
            this.rbOnlyUnassignedLighting = new System.Windows.Forms.RadioButton();
            this.rbOverwriteExistingLighting = new System.Windows.Forms.RadioButton();
            this.rqProjectParam = new System.Windows.Forms.GroupBox();
            this.btnProjectParametersSave = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.rqCPDrange = new System.Windows.Forms.ComboBox();
            this.pnl_DiscriminationTest = new System.Windows.Forms.Panel();
            this.guiDiscrim1pt5Times = new System.Windows.Forms.RadioButton();
            this.label11 = new System.Windows.Forms.Label();
            this.guiDiscrim2Times = new System.Windows.Forms.RadioButton();
            this.pnl_CableDatabase = new System.Windows.Forms.Panel();
            this.guiAUScableSel = new System.Windows.Forms.RadioButton();
            this.label23 = new System.Windows.Forms.Label();
            this.guiNZcableSel = new System.Windows.Forms.RadioButton();
            this.pnl_SystemMaxVoltDrop = new System.Windows.Forms.Panel();
            this.guiSysVD7pc = new System.Windows.Forms.RadioButton();
            this.label22 = new System.Windows.Forms.Label();
            this.guiSysVD5pc = new System.Windows.Forms.RadioButton();
            this.rqDBedit = new System.Windows.Forms.Button();
            this.rqNotice = new System.Windows.Forms.Label();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.btnEnhancedCircuitEdit = new System.Windows.Forms.Button();
            this.btnRun = new System.Windows.Forms.Button();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.label3 = new System.Windows.Forms.Label();
            this.rbSizerOnlyUnassigned = new System.Windows.Forms.RadioButton();
            this.rbSizerOverwriteExisting = new System.Windows.Forms.RadioButton();
            this.btnRunSizer = new System.Windows.Forms.Button();
            this.label2 = new System.Windows.Forms.Label();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnExport = new System.Windows.Forms.Button();
            this.dgvDBSel = new System.Windows.Forms.DataGridView();
            this.dBSummaryTableBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.dBSummaryDataSetBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.dBSummaryDataSet = new MEP.PowerBIM_5.DataSets.DBSummaryDataSet();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.tabBox = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.guiTxtDiversityLighting = new System.Windows.Forms.MaskedTextBox();
            this.cbDeviceLighting = new System.Windows.Forms.CheckBox();
            this.cbCurveLighting = new System.Windows.Forms.CheckBox();
            this.cbRatingLighting = new System.Windows.Forms.CheckBox();
            this.guiTxtRCDratingLighting = new System.Windows.Forms.MaskedTextBox();
            this.cbDiversityLighting = new System.Windows.Forms.CheckBox();
            this.cbRCDRatingLighting = new System.Windows.Forms.CheckBox();
            this.guiTxtRevisionLighting = new System.Windows.Forms.MaskedTextBox();
            this.cbDeratingFactorLighting = new System.Windows.Forms.CheckBox();
            this.cbInstallMethodLighting = new System.Windows.Forms.CheckBox();
            this.label6 = new System.Windows.Forms.Label();
            this.cbCircuitRevisionLighting = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.guiInstallLighting = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.guiTxtRatingLighting = new System.Windows.Forms.ComboBox();
            this.guiTxtCurveLighting = new System.Windows.Forms.ComboBox();
            this.guiTxtDeratingFactorLighting = new System.Windows.Forms.MaskedTextBox();
            this.guiTxtDeviceLighting = new System.Windows.Forms.ComboBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.guiTxtDiversityPower = new System.Windows.Forms.MaskedTextBox();
            this.rbOnlyUnassignedPower = new System.Windows.Forms.RadioButton();
            this.cbDevicePower = new System.Windows.Forms.CheckBox();
            this.btnWritePower = new System.Windows.Forms.Button();
            this.cbCurvePower = new System.Windows.Forms.CheckBox();
            this.rbOverwriteExistingPower = new System.Windows.Forms.RadioButton();
            this.cbRatingPower = new System.Windows.Forms.CheckBox();
            this.guiInstallPower = new System.Windows.Forms.ComboBox();
            this.cbDiversityPower = new System.Windows.Forms.CheckBox();
            this.cbRCDRatingPower = new System.Windows.Forms.CheckBox();
            this.guiTxtRevisionPower = new System.Windows.Forms.MaskedTextBox();
            this.cbDeratingFactorPower = new System.Windows.Forms.CheckBox();
            this.cbInstallMethodPower = new System.Windows.Forms.CheckBox();
            this.label9 = new System.Windows.Forms.Label();
            this.cbCircuitRevisionPower = new System.Windows.Forms.CheckBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.guiTxtRatingPower = new System.Windows.Forms.ComboBox();
            this.guiTxtCurvePower = new System.Windows.Forms.ComboBox();
            this.guiTxtDeratingFactorPower = new System.Windows.Forms.MaskedTextBox();
            this.guiTxtDevicePower = new System.Windows.Forms.ComboBox();
            this.guiTxtRCDratingPower = new System.Windows.Forms.MaskedTextBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.guiTxtDiversityOther = new System.Windows.Forms.MaskedTextBox();
            this.rbOnlyUnassignedOther = new System.Windows.Forms.RadioButton();
            this.cbDeviceOther = new System.Windows.Forms.CheckBox();
            this.btnWriteOther = new System.Windows.Forms.Button();
            this.cbCurveOther = new System.Windows.Forms.CheckBox();
            this.cbRatingOther = new System.Windows.Forms.CheckBox();
            this.rbOverwriteExistingOther = new System.Windows.Forms.RadioButton();
            this.cbDiversityOther = new System.Windows.Forms.CheckBox();
            this.guiInstallOther = new System.Windows.Forms.ComboBox();
            this.guiTxtRevisionOther = new System.Windows.Forms.MaskedTextBox();
            this.cbRCDRatingOther = new System.Windows.Forms.CheckBox();
            this.cbInstallMethodOther = new System.Windows.Forms.CheckBox();
            this.cbDeratingFactorOther = new System.Windows.Forms.CheckBox();
            this.cbCircuitRevisionOther = new System.Windows.Forms.CheckBox();
            this.label13 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.guiTxtRatingOther = new System.Windows.Forms.ComboBox();
            this.label10 = new System.Windows.Forms.Label();
            this.guiTxtDeratingFactorOther = new System.Windows.Forms.MaskedTextBox();
            this.guiTxtCurveOther = new System.Windows.Forms.ComboBox();
            this.guiTxtRCDratingOther = new System.Windows.Forms.MaskedTextBox();
            this.guiTxtDeviceOther = new System.Windows.Forms.ComboBox();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel4 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel5 = new System.Windows.Forms.TableLayoutPanel();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.ManualLock = new System.Windows.Forms.DataGridViewImageColumn();
            this.dBNameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dBPassCountDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dBWarningCountDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dBFailCountDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DB_UserNotes = new System.Windows.Forms.DataGridViewButtonColumn();
            this.dBNotesDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DB_Settings = new System.Windows.Forms.DataGridViewButtonColumn();
            this.DB_UpdateRequired = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.rqProjectParam.SuspendLayout();
            this.pnl_DiscriminationTest.SuspendLayout();
            this.pnl_CableDatabase.SuspendLayout();
            this.pnl_SystemMaxVoltDrop.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDBSel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryTableBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryDataSetBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryDataSet)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.tabBox.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            this.tableLayoutPanel4.SuspendLayout();
            this.tableLayoutPanel5.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.SuspendLayout();
            // 
            // rqSettings
            // 
            this.rqSettings.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rqSettings.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.rqSettings.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqSettings.Font = new System.Drawing.Font("Arial", 9.25F, System.Drawing.FontStyle.Bold);
            this.rqSettings.ForeColor = System.Drawing.Color.White;
            this.rqSettings.Location = new System.Drawing.Point(435, 86);
            this.rqSettings.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqSettings.Name = "rqSettings";
            this.rqSettings.Size = new System.Drawing.Size(171, 35);
            this.rqSettings.TabIndex = 189;
            this.rqSettings.Text = "Advanced Settings";
            this.rqSettings.UseVisualStyleBackColor = false;
            this.rqSettings.Click += new System.EventHandler(this.btnSettings_Click);
            // 
            // btnWriteLighting
            // 
            this.btnWriteLighting.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnWriteLighting.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnWriteLighting.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnWriteLighting.FlatAppearance.BorderSize = 0;
            this.btnWriteLighting.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnWriteLighting.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnWriteLighting.ForeColor = System.Drawing.Color.White;
            this.btnWriteLighting.Location = new System.Drawing.Point(49, 252);
            this.btnWriteLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnWriteLighting.Name = "btnWriteLighting";
            this.btnWriteLighting.Size = new System.Drawing.Size(223, 26);
            this.btnWriteLighting.TabIndex = 192;
            this.btnWriteLighting.Text = "Write (Lighting) To Schedule";
            this.btnWriteLighting.UseVisualStyleBackColor = false;
            this.btnWriteLighting.Click += new System.EventHandler(this.btnWriteLighting_Click);
            // 
            // rbOnlyUnassignedLighting
            // 
            this.rbOnlyUnassignedLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOnlyUnassignedLighting.AutoSize = true;
            this.rbOnlyUnassignedLighting.Checked = true;
            this.rbOnlyUnassignedLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOnlyUnassignedLighting.Location = new System.Drawing.Point(49, 206);
            this.rbOnlyUnassignedLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOnlyUnassignedLighting.Name = "rbOnlyUnassignedLighting";
            this.rbOnlyUnassignedLighting.Size = new System.Drawing.Size(187, 18);
            this.rbOnlyUnassignedLighting.TabIndex = 115;
            this.rbOnlyUnassignedLighting.TabStop = true;
            this.rbOnlyUnassignedLighting.Text = "Only Populate Unassigned Values";
            this.rbOnlyUnassignedLighting.UseMnemonic = false;
            this.rbOnlyUnassignedLighting.UseVisualStyleBackColor = true;
            // 
            // rbOverwriteExistingLighting
            // 
            this.rbOverwriteExistingLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOverwriteExistingLighting.AutoSize = true;
            this.rbOverwriteExistingLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOverwriteExistingLighting.Location = new System.Drawing.Point(49, 225);
            this.rbOverwriteExistingLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOverwriteExistingLighting.Name = "rbOverwriteExistingLighting";
            this.rbOverwriteExistingLighting.Size = new System.Drawing.Size(150, 18);
            this.rbOverwriteExistingLighting.TabIndex = 114;
            this.rbOverwriteExistingLighting.Text = "Overwrite Existing Values";
            this.rbOverwriteExistingLighting.UseVisualStyleBackColor = true;
            this.rbOverwriteExistingLighting.CheckedChanged += new System.EventHandler(this.rbOverwriteExistingLighting_CheckedChanged);
            // 
            // rqProjectParam
            // 
            this.rqProjectParam.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rqProjectParam.Controls.Add(this.btnProjectParametersSave);
            this.rqProjectParam.Controls.Add(this.label1);
            this.rqProjectParam.Controls.Add(this.rqCPDrange);
            this.rqProjectParam.Controls.Add(this.rqSettings);
            this.rqProjectParam.Controls.Add(this.pnl_DiscriminationTest);
            this.rqProjectParam.Controls.Add(this.pnl_CableDatabase);
            this.rqProjectParam.Controls.Add(this.pnl_SystemMaxVoltDrop);
            this.rqProjectParam.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqProjectParam.Location = new System.Drawing.Point(3, 2);
            this.rqProjectParam.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqProjectParam.MinimumSize = new System.Drawing.Size(597, 146);
            this.rqProjectParam.Name = "rqProjectParam";
            this.rqProjectParam.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqProjectParam.Size = new System.Drawing.Size(651, 156);
            this.rqProjectParam.TabIndex = 184;
            this.rqProjectParam.TabStop = false;
            this.rqProjectParam.Text = "Project Parameters";
            // 
            // btnProjectParametersSave
            // 
            this.btnProjectParametersSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnProjectParametersSave.Enabled = false;
            this.btnProjectParametersSave.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnProjectParametersSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnProjectParametersSave.Font = new System.Drawing.Font("Arial", 9.25F, System.Drawing.FontStyle.Bold);
            this.btnProjectParametersSave.ForeColor = System.Drawing.Color.Black;
            this.btnProjectParametersSave.Location = new System.Drawing.Point(435, 47);
            this.btnProjectParametersSave.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnProjectParametersSave.Name = "btnProjectParametersSave";
            this.btnProjectParametersSave.Size = new System.Drawing.Size(171, 35);
            this.btnProjectParametersSave.TabIndex = 193;
            this.btnProjectParametersSave.Text = "Save Changes";
            this.btnProjectParametersSave.UseVisualStyleBackColor = false;
            this.btnProjectParametersSave.Click += new System.EventHandler(this.btnProjectParametersSave_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(12, 103);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(131, 14);
            this.label1.TabIndex = 85;
            this.label1.Text = "Protective Device Product";
            // 
            // rqCPDrange
            // 
            this.rqCPDrange.FormattingEnabled = true;
            this.rqCPDrange.Location = new System.Drawing.Point(150, 99);
            this.rqCPDrange.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqCPDrange.Name = "rqCPDrange";
            this.rqCPDrange.Size = new System.Drawing.Size(235, 24);
            this.rqCPDrange.TabIndex = 85;
            this.rqCPDrange.SelectedIndexChanged += new System.EventHandler(this.rqCPDrange_SelectedIndexChanged);
            this.rqCPDrange.Click += new System.EventHandler(this.rqCPDrange_Click);
            // 
            // pnl_DiscriminationTest
            // 
            this.pnl_DiscriminationTest.Controls.Add(this.guiDiscrim1pt5Times);
            this.pnl_DiscriminationTest.Controls.Add(this.label11);
            this.pnl_DiscriminationTest.Controls.Add(this.guiDiscrim2Times);
            this.pnl_DiscriminationTest.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.pnl_DiscriminationTest.Location = new System.Drawing.Point(9, 47);
            this.pnl_DiscriminationTest.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.pnl_DiscriminationTest.Name = "pnl_DiscriminationTest";
            this.pnl_DiscriminationTest.Size = new System.Drawing.Size(375, 22);
            this.pnl_DiscriminationTest.TabIndex = 1;
            // 
            // guiDiscrim1pt5Times
            // 
            this.guiDiscrim1pt5Times.AutoSize = true;
            this.guiDiscrim1pt5Times.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiDiscrim1pt5Times.Location = new System.Drawing.Point(239, 3);
            this.guiDiscrim1pt5Times.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiDiscrim1pt5Times.Name = "guiDiscrim1pt5Times";
            this.guiDiscrim1pt5Times.Size = new System.Drawing.Size(108, 18);
            this.guiDiscrim1pt5Times.TabIndex = 1;
            this.guiDiscrim1pt5Times.Text = "1.5x (3000:2018)";
            this.guiDiscrim1pt5Times.UseVisualStyleBackColor = true;
            this.guiDiscrim1pt5Times.CheckedChanged += new System.EventHandler(this.guiDiscrim1pt5Times_CheckedChanged);
            this.guiDiscrim1pt5Times.Click += new System.EventHandler(this.guiDiscrim1pt5Times_Click);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.Location = new System.Drawing.Point(3, 5);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(99, 14);
            this.label11.TabIndex = 77;
            this.label11.Text = "Discrimination Test:";
            // 
            // guiDiscrim2Times
            // 
            this.guiDiscrim2Times.AutoSize = true;
            this.guiDiscrim2Times.Checked = true;
            this.guiDiscrim2Times.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiDiscrim2Times.Location = new System.Drawing.Point(141, 3);
            this.guiDiscrim2Times.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiDiscrim2Times.Name = "guiDiscrim2Times";
            this.guiDiscrim2Times.Size = new System.Drawing.Size(99, 18);
            this.guiDiscrim2Times.TabIndex = 0;
            this.guiDiscrim2Times.TabStop = true;
            this.guiDiscrim2Times.Text = "2x (3000:2007)";
            this.guiDiscrim2Times.UseVisualStyleBackColor = true;
            this.guiDiscrim2Times.Click += new System.EventHandler(this.guiDiscrim2Times_Click);
            // 
            // pnl_CableDatabase
            // 
            this.pnl_CableDatabase.Controls.Add(this.guiAUScableSel);
            this.pnl_CableDatabase.Controls.Add(this.label23);
            this.pnl_CableDatabase.Controls.Add(this.guiNZcableSel);
            this.pnl_CableDatabase.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.pnl_CableDatabase.Location = new System.Drawing.Point(9, 73);
            this.pnl_CableDatabase.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.pnl_CableDatabase.Name = "pnl_CableDatabase";
            this.pnl_CableDatabase.Size = new System.Drawing.Size(375, 22);
            this.pnl_CableDatabase.TabIndex = 1;
            // 
            // guiAUScableSel
            // 
            this.guiAUScableSel.AutoSize = true;
            this.guiAUScableSel.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiAUScableSel.Location = new System.Drawing.Point(239, 2);
            this.guiAUScableSel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiAUScableSel.Name = "guiAUScableSel";
            this.guiAUScableSel.Size = new System.Drawing.Size(71, 18);
            this.guiAUScableSel.TabIndex = 1;
            this.guiAUScableSel.Text = "Australia ";
            this.guiAUScableSel.UseVisualStyleBackColor = true;
            this.guiAUScableSel.CheckedChanged += new System.EventHandler(this.guiAUScableSel_CheckedChanged);
            this.guiAUScableSel.Click += new System.EventHandler(this.guiAUScableSel_Click);
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label23.Location = new System.Drawing.Point(3, 5);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(86, 14);
            this.label23.TabIndex = 77;
            this.label23.Text = "Cable Database:";
            // 
            // guiNZcableSel
            // 
            this.guiNZcableSel.AutoSize = true;
            this.guiNZcableSel.Checked = true;
            this.guiNZcableSel.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiNZcableSel.Location = new System.Drawing.Point(141, 2);
            this.guiNZcableSel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiNZcableSel.Name = "guiNZcableSel";
            this.guiNZcableSel.Size = new System.Drawing.Size(90, 18);
            this.guiNZcableSel.TabIndex = 0;
            this.guiNZcableSel.TabStop = true;
            this.guiNZcableSel.Text = "New Zealand";
            this.guiNZcableSel.UseVisualStyleBackColor = true;
            this.guiNZcableSel.Click += new System.EventHandler(this.guiNZcableSel_Click);
            // 
            // pnl_SystemMaxVoltDrop
            // 
            this.pnl_SystemMaxVoltDrop.Controls.Add(this.guiSysVD7pc);
            this.pnl_SystemMaxVoltDrop.Controls.Add(this.label22);
            this.pnl_SystemMaxVoltDrop.Controls.Add(this.guiSysVD5pc);
            this.pnl_SystemMaxVoltDrop.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.pnl_SystemMaxVoltDrop.Location = new System.Drawing.Point(9, 21);
            this.pnl_SystemMaxVoltDrop.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.pnl_SystemMaxVoltDrop.Name = "pnl_SystemMaxVoltDrop";
            this.pnl_SystemMaxVoltDrop.Size = new System.Drawing.Size(375, 22);
            this.pnl_SystemMaxVoltDrop.TabIndex = 0;
            // 
            // guiSysVD7pc
            // 
            this.guiSysVD7pc.AutoSize = true;
            this.guiSysVD7pc.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiSysVD7pc.Location = new System.Drawing.Point(239, 3);
            this.guiSysVD7pc.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiSysVD7pc.Name = "guiSysVD7pc";
            this.guiSysVD7pc.Size = new System.Drawing.Size(41, 18);
            this.guiSysVD7pc.TabIndex = 1;
            this.guiSysVD7pc.Text = "7%";
            this.guiSysVD7pc.UseVisualStyleBackColor = true;
            this.guiSysVD7pc.CheckedChanged += new System.EventHandler(this.guiSysVD7pc_CheckedChanged);
            this.guiSysVD7pc.Click += new System.EventHandler(this.guiSysVD7pc_Click);
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label22.Location = new System.Drawing.Point(3, 6);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(116, 14);
            this.label22.TabIndex = 2;
            this.label22.Text = "System Max Volt Drop:";
            // 
            // guiSysVD5pc
            // 
            this.guiSysVD5pc.AutoSize = true;
            this.guiSysVD5pc.Checked = true;
            this.guiSysVD5pc.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.guiSysVD5pc.Location = new System.Drawing.Point(141, 3);
            this.guiSysVD5pc.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiSysVD5pc.Name = "guiSysVD5pc";
            this.guiSysVD5pc.Size = new System.Drawing.Size(41, 18);
            this.guiSysVD5pc.TabIndex = 0;
            this.guiSysVD5pc.TabStop = true;
            this.guiSysVD5pc.Text = "5%";
            this.guiSysVD5pc.UseVisualStyleBackColor = true;
            this.guiSysVD5pc.Click += new System.EventHandler(this.guiSysVD5pc_Click);
            // 
            // rqDBedit
            // 
            this.rqDBedit.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rqDBedit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.rqDBedit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqDBedit.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold);
            this.rqDBedit.ForeColor = System.Drawing.Color.White;
            this.rqDBedit.Location = new System.Drawing.Point(3, 2);
            this.rqDBedit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rqDBedit.Name = "rqDBedit";
            this.rqDBedit.Size = new System.Drawing.Size(586, 38);
            this.rqDBedit.TabIndex = 73;
            this.rqDBedit.Text = "Edit DB Parameters";
            this.rqDBedit.UseVisualStyleBackColor = false;
            this.rqDBedit.Click += new System.EventHandler(this.btnDBedit_Click);
            // 
            // rqNotice
            // 
            this.rqNotice.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rqNotice.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqNotice.ForeColor = System.Drawing.Color.Black;
            this.rqNotice.Location = new System.Drawing.Point(3, 0);
            this.rqNotice.Name = "rqNotice";
            this.rqNotice.Size = new System.Drawing.Size(592, 40);
            this.rqNotice.TabIndex = 182;
            this.rqNotice.Text = "Select One Or Multiple DB Schedules";
            this.rqNotice.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox3.Controls.Add(this.btnEnhancedCircuitEdit);
            this.groupBox3.Controls.Add(this.btnRun);
            this.groupBox3.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox3.Location = new System.Drawing.Point(3, 2);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.MinimumSize = new System.Drawing.Size(325, 0);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox3.Size = new System.Drawing.Size(325, 122);
            this.groupBox3.TabIndex = 186;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Check DB and Circuits";
            // 
            // btnEnhancedCircuitEdit
            // 
            this.btnEnhancedCircuitEdit.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEnhancedCircuitEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.btnEnhancedCircuitEdit.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnEnhancedCircuitEdit.FlatAppearance.BorderSize = 2;
            this.btnEnhancedCircuitEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEnhancedCircuitEdit.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold);
            this.btnEnhancedCircuitEdit.ForeColor = System.Drawing.Color.White;
            this.btnEnhancedCircuitEdit.Location = new System.Drawing.Point(6, 20);
            this.btnEnhancedCircuitEdit.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnEnhancedCircuitEdit.Name = "btnEnhancedCircuitEdit";
            this.btnEnhancedCircuitEdit.Size = new System.Drawing.Size(309, 37);
            this.btnEnhancedCircuitEdit.TabIndex = 81;
            this.btnEnhancedCircuitEdit.Text = "Live Circuit Editor";
            this.btnEnhancedCircuitEdit.UseVisualStyleBackColor = false;
            this.btnEnhancedCircuitEdit.Click += new System.EventHandler(this.btnEnhancedCircuitEdit_Click);
            // 
            // btnRun
            // 
            this.btnRun.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRun.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnRun.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnRun.FlatAppearance.BorderSize = 0;
            this.btnRun.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRun.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRun.ForeColor = System.Drawing.Color.White;
            this.btnRun.Location = new System.Drawing.Point(6, 72);
            this.btnRun.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnRun.Name = "btnRun";
            this.btnRun.Size = new System.Drawing.Size(309, 38);
            this.btnRun.TabIndex = 192;
            this.btnRun.Text = "Run Circuit Check";
            this.btnRun.UseVisualStyleBackColor = false;
            this.btnRun.Click += new System.EventHandler(this.btnRun_Click);
            // 
            // groupBox4
            // 
            this.groupBox4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox4.Controls.Add(this.label3);
            this.groupBox4.Controls.Add(this.rbSizerOnlyUnassigned);
            this.groupBox4.Controls.Add(this.rbSizerOverwriteExisting);
            this.groupBox4.Controls.Add(this.btnRunSizer);
            this.groupBox4.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox4.Location = new System.Drawing.Point(363, 37);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.MaximumSize = new System.Drawing.Size(270, 9999);
            this.groupBox4.MinimumSize = new System.Drawing.Size(270, 175);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox4.Size = new System.Drawing.Size(270, 181);
            this.groupBox4.TabIndex = 187;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "Run Automatic Cable Sizing";
            // 
            // label3
            // 
            this.label3.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label3.Location = new System.Drawing.Point(9, 28);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(252, 50);
            this.label3.TabIndex = 85;
            this.label3.Text = "This module will try to automatically select a cable for all circuits on the sele" +
    "cted DBs. Once done, use the Circuit Editor to check the result";
            // 
            // rbSizerOnlyUnassigned
            // 
            this.rbSizerOnlyUnassigned.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbSizerOnlyUnassigned.AutoSize = true;
            this.rbSizerOnlyUnassigned.Checked = true;
            this.rbSizerOnlyUnassigned.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbSizerOnlyUnassigned.Location = new System.Drawing.Point(21, 87);
            this.rbSizerOnlyUnassigned.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbSizerOnlyUnassigned.Name = "rbSizerOnlyUnassigned";
            this.rbSizerOnlyUnassigned.Size = new System.Drawing.Size(187, 18);
            this.rbSizerOnlyUnassigned.TabIndex = 82;
            this.rbSizerOnlyUnassigned.TabStop = true;
            this.rbSizerOnlyUnassigned.Text = "Only Populate Unassigned Cables";
            this.rbSizerOnlyUnassigned.UseVisualStyleBackColor = true;
            // 
            // rbSizerOverwriteExisting
            // 
            this.rbSizerOverwriteExisting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbSizerOverwriteExisting.AutoSize = true;
            this.rbSizerOverwriteExisting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbSizerOverwriteExisting.Location = new System.Drawing.Point(21, 107);
            this.rbSizerOverwriteExisting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbSizerOverwriteExisting.Name = "rbSizerOverwriteExisting";
            this.rbSizerOverwriteExisting.Size = new System.Drawing.Size(124, 18);
            this.rbSizerOverwriteExisting.TabIndex = 81;
            this.rbSizerOverwriteExisting.Text = "Overwrite All Cables";
            this.rbSizerOverwriteExisting.UseVisualStyleBackColor = true;
            // 
            // btnRunSizer
            // 
            this.btnRunSizer.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnRunSizer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnRunSizer.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnRunSizer.FlatAppearance.BorderSize = 0;
            this.btnRunSizer.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRunSizer.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnRunSizer.ForeColor = System.Drawing.Color.White;
            this.btnRunSizer.Location = new System.Drawing.Point(21, 138);
            this.btnRunSizer.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnRunSizer.Name = "btnRunSizer";
            this.btnRunSizer.Size = new System.Drawing.Size(223, 26);
            this.btnRunSizer.TabIndex = 192;
            this.btnRunSizer.Text = "Run Auto-sizer";
            this.btnRunSizer.UseVisualStyleBackColor = false;
            this.btnRunSizer.Click += new System.EventHandler(this.btnRunSizer_Click);
            // 
            // label2
            // 
            this.label2.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label2.Location = new System.Drawing.Point(27, 20);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(282, 37);
            this.label2.TabIndex = 85;
            this.label2.Text = "Please save before exiting. Closing without saving could cause changes to be lost" +
    "!\r\n";
            this.label2.Visible = false;
            // 
            // btnSave
            // 
            this.btnSave.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnSave.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSave.ForeColor = System.Drawing.Color.Black;
            this.btnSave.Location = new System.Drawing.Point(7, 72);
            this.btnSave.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(147, 38);
            this.btnSave.TabIndex = 192;
            this.btnSave.Text = "Save";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Visible = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.btnCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(170, 72);
            this.btnCancel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(147, 38);
            this.btnCancel.TabIndex = 191;
            this.btnCancel.Text = "Close";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btnExport
            // 
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnExport.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnExport.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold);
            this.btnExport.ForeColor = System.Drawing.Color.White;
            this.btnExport.Location = new System.Drawing.Point(3, 54);
            this.btnExport.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(586, 38);
            this.btnExport.TabIndex = 73;
            this.btnExport.Text = "Export Results";
            this.btnExport.UseVisualStyleBackColor = false;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // dgvDBSel
            // 
            this.dgvDBSel.AllowUserToAddRows = false;
            this.dgvDBSel.AllowUserToDeleteRows = false;
            this.dgvDBSel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgvDBSel.AutoGenerateColumns = false;
            this.dgvDBSel.BackgroundColor = System.Drawing.SystemColors.ControlLightLight;
            this.dgvDBSel.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.Raised;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.ButtonHighlight;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDBSel.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvDBSel.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvDBSel.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.ManualLock,
            this.dBNameDataGridViewTextBoxColumn,
            this.dBPassCountDataGridViewTextBoxColumn,
            this.dBWarningCountDataGridViewTextBoxColumn,
            this.dBFailCountDataGridViewTextBoxColumn,
            this.DB_UserNotes,
            this.dBNotesDataGridViewTextBoxColumn,
            this.DB_Settings,
            this.DB_UpdateRequired});
            this.dgvDBSel.DataSource = this.dBSummaryTableBindingSource;
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvDBSel.DefaultCellStyle = dataGridViewCellStyle8;
            this.dgvDBSel.EnableHeadersVisualStyles = false;
            this.dgvDBSel.Location = new System.Drawing.Point(3, 42);
            this.dgvDBSel.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.dgvDBSel.Name = "dgvDBSel";
            this.dgvDBSel.RowHeadersVisible = false;
            this.dgvDBSel.RowHeadersWidth = 51;
            this.dgvDBSel.RowTemplate.Height = 24;
            this.dgvDBSel.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvDBSel.Size = new System.Drawing.Size(592, 491);
            this.dgvDBSel.TabIndex = 194;
            this.dgvDBSel.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvDBSel_CellContentClick);
            this.dgvDBSel.CellContentDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvDBSel_CellContentDoubleClick);
            this.dgvDBSel.SelectionChanged += new System.EventHandler(this.dgv_SelectedIndexChanged);
            // 
            // dBSummaryTableBindingSource
            // 
            this.dBSummaryTableBindingSource.DataMember = "DBSummaryTable";
            this.dBSummaryTableBindingSource.DataSource = this.dBSummaryDataSetBindingSource;
            // 
            // dBSummaryDataSetBindingSource
            // 
            this.dBSummaryDataSetBindingSource.DataSource = this.dBSummaryDataSet;
            this.dBSummaryDataSetBindingSource.Position = 0;
            // 
            // dBSummaryDataSet
            // 
            this.dBSummaryDataSet.DataSetName = "DBSummaryDataSet";
            this.dBSummaryDataSet.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.tabBox);
            this.groupBox1.Controls.Add(this.groupBox4);
            this.groupBox1.Font = new System.Drawing.Font("Arial", 10F, System.Drawing.FontStyle.Bold);
            this.groupBox1.Location = new System.Drawing.Point(3, 162);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox1.Size = new System.Drawing.Size(651, 350);
            this.groupBox1.TabIndex = 195;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Bulk Data Entry";
            // 
            // tabBox
            // 
            this.tabBox.Controls.Add(this.tabPage1);
            this.tabBox.Controls.Add(this.tabPage2);
            this.tabBox.Controls.Add(this.tabPage3);
            this.tabBox.Location = new System.Drawing.Point(6, 21);
            this.tabBox.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabBox.Name = "tabBox";
            this.tabBox.SelectedIndex = 0;
            this.tabBox.Size = new System.Drawing.Size(333, 320);
            this.tabBox.TabIndex = 189;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.guiTxtDiversityLighting);
            this.tabPage1.Controls.Add(this.rbOnlyUnassignedLighting);
            this.tabPage1.Controls.Add(this.cbDeviceLighting);
            this.tabPage1.Controls.Add(this.btnWriteLighting);
            this.tabPage1.Controls.Add(this.cbCurveLighting);
            this.tabPage1.Controls.Add(this.rbOverwriteExistingLighting);
            this.tabPage1.Controls.Add(this.cbRatingLighting);
            this.tabPage1.Controls.Add(this.guiTxtRCDratingLighting);
            this.tabPage1.Controls.Add(this.cbDiversityLighting);
            this.tabPage1.Controls.Add(this.cbRCDRatingLighting);
            this.tabPage1.Controls.Add(this.guiTxtRevisionLighting);
            this.tabPage1.Controls.Add(this.cbDeratingFactorLighting);
            this.tabPage1.Controls.Add(this.cbInstallMethodLighting);
            this.tabPage1.Controls.Add(this.label6);
            this.tabPage1.Controls.Add(this.cbCircuitRevisionLighting);
            this.tabPage1.Controls.Add(this.label7);
            this.tabPage1.Controls.Add(this.guiInstallLighting);
            this.tabPage1.Controls.Add(this.label8);
            this.tabPage1.Controls.Add(this.guiTxtRatingLighting);
            this.tabPage1.Controls.Add(this.guiTxtCurveLighting);
            this.tabPage1.Controls.Add(this.guiTxtDeratingFactorLighting);
            this.tabPage1.Controls.Add(this.guiTxtDeviceLighting);
            this.tabPage1.Location = new System.Drawing.Point(4, 25);
            this.tabPage1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage1.Size = new System.Drawing.Size(325, 291);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "Lighting Circuits";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // guiTxtDiversityLighting
            // 
            this.guiTxtDiversityLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDiversityLighting.Location = new System.Drawing.Point(207, 106);
            this.guiTxtDiversityLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDiversityLighting.Mask = "000%";
            this.guiTxtDiversityLighting.Name = "guiTxtDiversityLighting";
            this.guiTxtDiversityLighting.PromptChar = ' ';
            this.guiTxtDiversityLighting.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDiversityLighting.TabIndex = 122;
            this.guiTxtDiversityLighting.Text = "100";
            this.guiTxtDiversityLighting.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbDeviceLighting
            // 
            this.cbDeviceLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDeviceLighting.AutoSize = true;
            this.cbDeviceLighting.Checked = true;
            this.cbDeviceLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDeviceLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDeviceLighting.Location = new System.Drawing.Point(189, 37);
            this.cbDeviceLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDeviceLighting.Name = "cbDeviceLighting";
            this.cbDeviceLighting.Size = new System.Drawing.Size(15, 14);
            this.cbDeviceLighting.TabIndex = 121;
            this.cbDeviceLighting.UseVisualStyleBackColor = true;
            this.cbDeviceLighting.CheckedChanged += new System.EventHandler(this.cbDeviceLighting_CheckedChanged);
            this.cbDeviceLighting.EnabledChanged += new System.EventHandler(this.cbDeviceLighting_CheckedChanged);
            // 
            // cbCurveLighting
            // 
            this.cbCurveLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCurveLighting.AutoSize = true;
            this.cbCurveLighting.Checked = true;
            this.cbCurveLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbCurveLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCurveLighting.Location = new System.Drawing.Point(121, 37);
            this.cbCurveLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCurveLighting.Name = "cbCurveLighting";
            this.cbCurveLighting.Size = new System.Drawing.Size(15, 14);
            this.cbCurveLighting.TabIndex = 121;
            this.cbCurveLighting.UseVisualStyleBackColor = true;
            this.cbCurveLighting.CheckedChanged += new System.EventHandler(this.cbCurveLighting_CheckedChanged);
            this.cbCurveLighting.EnabledChanged += new System.EventHandler(this.cbCurveLighting_CheckedChanged);
            // 
            // cbRatingLighting
            // 
            this.cbRatingLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRatingLighting.AutoSize = true;
            this.cbRatingLighting.Checked = true;
            this.cbRatingLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbRatingLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRatingLighting.Location = new System.Drawing.Point(49, 37);
            this.cbRatingLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRatingLighting.Name = "cbRatingLighting";
            this.cbRatingLighting.Size = new System.Drawing.Size(15, 14);
            this.cbRatingLighting.TabIndex = 121;
            this.cbRatingLighting.UseVisualStyleBackColor = true;
            this.cbRatingLighting.CheckedChanged += new System.EventHandler(this.cbRatingLighting_CheckedChanged);
            this.cbRatingLighting.EnabledChanged += new System.EventHandler(this.cbRatingLighting_CheckedChanged);
            // 
            // guiTxtRCDratingLighting
            // 
            this.guiTxtRCDratingLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRCDratingLighting.Enabled = false;
            this.guiTxtRCDratingLighting.Location = new System.Drawing.Point(207, 61);
            this.guiTxtRCDratingLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRCDratingLighting.Name = "guiTxtRCDratingLighting";
            this.guiTxtRCDratingLighting.PromptChar = ' ';
            this.guiTxtRCDratingLighting.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRCDratingLighting.TabIndex = 111;
            this.guiTxtRCDratingLighting.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbDiversityLighting
            // 
            this.cbDiversityLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDiversityLighting.AutoSize = true;
            this.cbDiversityLighting.Checked = true;
            this.cbDiversityLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDiversityLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDiversityLighting.Location = new System.Drawing.Point(49, 109);
            this.cbDiversityLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDiversityLighting.Name = "cbDiversityLighting";
            this.cbDiversityLighting.Size = new System.Drawing.Size(71, 18);
            this.cbDiversityLighting.TabIndex = 121;
            this.cbDiversityLighting.Text = "Diversity:";
            this.cbDiversityLighting.UseVisualStyleBackColor = true;
            this.cbDiversityLighting.CheckedChanged += new System.EventHandler(this.cbDiversityLighting_CheckedChanged);
            this.cbDiversityLighting.EnabledChanged += new System.EventHandler(this.cbDiversityLighting_CheckedChanged);
            // 
            // cbRCDRatingLighting
            // 
            this.cbRCDRatingLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRCDRatingLighting.AutoSize = true;
            this.cbRCDRatingLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRCDRatingLighting.Location = new System.Drawing.Point(49, 64);
            this.cbRCDRatingLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRCDRatingLighting.Name = "cbRCDRatingLighting";
            this.cbRCDRatingLighting.Size = new System.Drawing.Size(151, 18);
            this.cbRCDRatingLighting.TabIndex = 86;
            this.cbRCDRatingLighting.Text = "RCD Rating (if applicable):";
            this.cbRCDRatingLighting.UseVisualStyleBackColor = true;
            this.cbRCDRatingLighting.CheckedChanged += new System.EventHandler(this.cbRCDRatingLighting_CheckedChanged);
            this.cbRCDRatingLighting.EnabledChanged += new System.EventHandler(this.cbRCDRatingLighting_CheckedChanged);
            // 
            // guiTxtRevisionLighting
            // 
            this.guiTxtRevisionLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRevisionLighting.Enabled = false;
            this.guiTxtRevisionLighting.Location = new System.Drawing.Point(207, 130);
            this.guiTxtRevisionLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRevisionLighting.Name = "guiTxtRevisionLighting";
            this.guiTxtRevisionLighting.PromptChar = ' ';
            this.guiTxtRevisionLighting.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRevisionLighting.TabIndex = 120;
            this.guiTxtRevisionLighting.Text = "A";
            this.guiTxtRevisionLighting.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbDeratingFactorLighting
            // 
            this.cbDeratingFactorLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDeratingFactorLighting.AutoSize = true;
            this.cbDeratingFactorLighting.Checked = true;
            this.cbDeratingFactorLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDeratingFactorLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDeratingFactorLighting.Location = new System.Drawing.Point(49, 87);
            this.cbDeratingFactorLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDeratingFactorLighting.Name = "cbDeratingFactorLighting";
            this.cbDeratingFactorLighting.Size = new System.Drawing.Size(103, 18);
            this.cbDeratingFactorLighting.TabIndex = 86;
            this.cbDeratingFactorLighting.Text = "Derating Factor:";
            this.cbDeratingFactorLighting.UseVisualStyleBackColor = true;
            this.cbDeratingFactorLighting.CheckedChanged += new System.EventHandler(this.cbDeratingFactorLighting_CheckedChanged);
            this.cbDeratingFactorLighting.EnabledChanged += new System.EventHandler(this.cbDeratingFactorLighting_CheckedChanged);
            // 
            // cbInstallMethodLighting
            // 
            this.cbInstallMethodLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbInstallMethodLighting.AutoSize = true;
            this.cbInstallMethodLighting.Checked = true;
            this.cbInstallMethodLighting.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbInstallMethodLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbInstallMethodLighting.Location = new System.Drawing.Point(49, 154);
            this.cbInstallMethodLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbInstallMethodLighting.Name = "cbInstallMethodLighting";
            this.cbInstallMethodLighting.Size = new System.Drawing.Size(114, 18);
            this.cbInstallMethodLighting.TabIndex = 119;
            this.cbInstallMethodLighting.Text = "Installation Method";
            this.cbInstallMethodLighting.UseVisualStyleBackColor = true;
            this.cbInstallMethodLighting.CheckedChanged += new System.EventHandler(this.cbInstallMethodLighting_CheckedChanged);
            this.cbInstallMethodLighting.EnabledChanged += new System.EventHandler(this.cbInstallMethodLighting_CheckedChanged);
            // 
            // label6
            // 
            this.label6.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(65, 15);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(53, 14);
            this.label6.TabIndex = 83;
            this.label6.Text = "Rating(A)";
            // 
            // cbCircuitRevisionLighting
            // 
            this.cbCircuitRevisionLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCircuitRevisionLighting.AutoSize = true;
            this.cbCircuitRevisionLighting.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCircuitRevisionLighting.Location = new System.Drawing.Point(49, 132);
            this.cbCircuitRevisionLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCircuitRevisionLighting.Name = "cbCircuitRevisionLighting";
            this.cbCircuitRevisionLighting.Size = new System.Drawing.Size(103, 18);
            this.cbCircuitRevisionLighting.TabIndex = 119;
            this.cbCircuitRevisionLighting.Text = "Circuit Revision:";
            this.cbCircuitRevisionLighting.UseVisualStyleBackColor = true;
            this.cbCircuitRevisionLighting.CheckedChanged += new System.EventHandler(this.cbCircuitRevisionLighting_CheckedChanged);
            this.cbCircuitRevisionLighting.EnabledChanged += new System.EventHandler(this.cbCircuitRevisionLighting_CheckedChanged);
            // 
            // label7
            // 
            this.label7.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(138, 15);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(36, 14);
            this.label7.TabIndex = 87;
            this.label7.Text = "Curve";
            // 
            // guiInstallLighting
            // 
            this.guiInstallLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiInstallLighting.FormattingEnabled = true;
            this.guiInstallLighting.Location = new System.Drawing.Point(49, 174);
            this.guiInstallLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiInstallLighting.Name = "guiInstallLighting";
            this.guiInstallLighting.Size = new System.Drawing.Size(223, 24);
            this.guiInstallLighting.TabIndex = 85;
            // 
            // label8
            // 
            this.label8.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(207, 15);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(30, 14);
            this.label8.TabIndex = 88;
            this.label8.Text = "Type";
            // 
            // guiTxtRatingLighting
            // 
            this.guiTxtRatingLighting.AllowDrop = true;
            this.guiTxtRatingLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRatingLighting.FormattingEnabled = true;
            this.guiTxtRatingLighting.Items.AddRange(new object[] {
            "0",
            "2",
            "3",
            "4",
            "6",
            "10",
            "16",
            "20",
            "25",
            "32",
            "40",
            "50",
            "63",
            "80",
            "100"});
            this.guiTxtRatingLighting.Location = new System.Drawing.Point(67, 32);
            this.guiTxtRatingLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRatingLighting.Name = "guiTxtRatingLighting";
            this.guiTxtRatingLighting.Size = new System.Drawing.Size(49, 24);
            this.guiTxtRatingLighting.TabIndex = 114;
            this.guiTxtRatingLighting.Text = "10";
            this.guiTxtRatingLighting.SelectedValueChanged += new System.EventHandler(this.guiTxtRatingLighting_SelectedValueChanged);
            // 
            // guiTxtCurveLighting
            // 
            this.guiTxtCurveLighting.AllowDrop = true;
            this.guiTxtCurveLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtCurveLighting.FormattingEnabled = true;
            this.guiTxtCurveLighting.Location = new System.Drawing.Point(137, 32);
            this.guiTxtCurveLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtCurveLighting.Name = "guiTxtCurveLighting";
            this.guiTxtCurveLighting.Size = new System.Drawing.Size(49, 24);
            this.guiTxtCurveLighting.TabIndex = 105;
            this.guiTxtCurveLighting.Text = "C";
            // 
            // guiTxtDeratingFactorLighting
            // 
            this.guiTxtDeratingFactorLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDeratingFactorLighting.Location = new System.Drawing.Point(207, 83);
            this.guiTxtDeratingFactorLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDeratingFactorLighting.Mask = "000%";
            this.guiTxtDeratingFactorLighting.Name = "guiTxtDeratingFactorLighting";
            this.guiTxtDeratingFactorLighting.PromptChar = ' ';
            this.guiTxtDeratingFactorLighting.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDeratingFactorLighting.TabIndex = 111;
            this.guiTxtDeratingFactorLighting.Text = "90";
            this.guiTxtDeratingFactorLighting.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // guiTxtDeviceLighting
            // 
            this.guiTxtDeviceLighting.AllowDrop = true;
            this.guiTxtDeviceLighting.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDeviceLighting.FormattingEnabled = true;
            this.guiTxtDeviceLighting.Location = new System.Drawing.Point(207, 32);
            this.guiTxtDeviceLighting.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDeviceLighting.Name = "guiTxtDeviceLighting";
            this.guiTxtDeviceLighting.Size = new System.Drawing.Size(67, 24);
            this.guiTxtDeviceLighting.TabIndex = 108;
            this.guiTxtDeviceLighting.Text = "MCB";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.guiTxtDiversityPower);
            this.tabPage2.Controls.Add(this.rbOnlyUnassignedPower);
            this.tabPage2.Controls.Add(this.cbDevicePower);
            this.tabPage2.Controls.Add(this.btnWritePower);
            this.tabPage2.Controls.Add(this.cbCurvePower);
            this.tabPage2.Controls.Add(this.rbOverwriteExistingPower);
            this.tabPage2.Controls.Add(this.cbRatingPower);
            this.tabPage2.Controls.Add(this.guiInstallPower);
            this.tabPage2.Controls.Add(this.cbDiversityPower);
            this.tabPage2.Controls.Add(this.cbRCDRatingPower);
            this.tabPage2.Controls.Add(this.guiTxtRevisionPower);
            this.tabPage2.Controls.Add(this.cbDeratingFactorPower);
            this.tabPage2.Controls.Add(this.cbInstallMethodPower);
            this.tabPage2.Controls.Add(this.label9);
            this.tabPage2.Controls.Add(this.cbCircuitRevisionPower);
            this.tabPage2.Controls.Add(this.label5);
            this.tabPage2.Controls.Add(this.label4);
            this.tabPage2.Controls.Add(this.guiTxtRatingPower);
            this.tabPage2.Controls.Add(this.guiTxtCurvePower);
            this.tabPage2.Controls.Add(this.guiTxtDeratingFactorPower);
            this.tabPage2.Controls.Add(this.guiTxtDevicePower);
            this.tabPage2.Controls.Add(this.guiTxtRCDratingPower);
            this.tabPage2.Location = new System.Drawing.Point(4, 25);
            this.tabPage2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage2.Size = new System.Drawing.Size(325, 291);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "Power Circuits";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // guiTxtDiversityPower
            // 
            this.guiTxtDiversityPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDiversityPower.Location = new System.Drawing.Point(207, 106);
            this.guiTxtDiversityPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDiversityPower.Mask = "000%";
            this.guiTxtDiversityPower.Name = "guiTxtDiversityPower";
            this.guiTxtDiversityPower.PromptChar = ' ';
            this.guiTxtDiversityPower.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDiversityPower.TabIndex = 122;
            this.guiTxtDiversityPower.Text = "75";
            this.guiTxtDiversityPower.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rbOnlyUnassignedPower
            // 
            this.rbOnlyUnassignedPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOnlyUnassignedPower.AutoSize = true;
            this.rbOnlyUnassignedPower.Checked = true;
            this.rbOnlyUnassignedPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOnlyUnassignedPower.Location = new System.Drawing.Point(49, 206);
            this.rbOnlyUnassignedPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOnlyUnassignedPower.Name = "rbOnlyUnassignedPower";
            this.rbOnlyUnassignedPower.Size = new System.Drawing.Size(151, 18);
            this.rbOnlyUnassignedPower.TabIndex = 194;
            this.rbOnlyUnassignedPower.TabStop = true;
            this.rbOnlyUnassignedPower.Text = "Only Populate Unassigned";
            this.rbOnlyUnassignedPower.UseMnemonic = false;
            this.rbOnlyUnassignedPower.UseVisualStyleBackColor = true;
            // 
            // cbDevicePower
            // 
            this.cbDevicePower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDevicePower.AutoSize = true;
            this.cbDevicePower.Checked = true;
            this.cbDevicePower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDevicePower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDevicePower.Location = new System.Drawing.Point(189, 37);
            this.cbDevicePower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDevicePower.Name = "cbDevicePower";
            this.cbDevicePower.Size = new System.Drawing.Size(15, 14);
            this.cbDevicePower.TabIndex = 121;
            this.cbDevicePower.UseVisualStyleBackColor = true;
            this.cbDevicePower.CheckedChanged += new System.EventHandler(this.cbDevicePower_CheckedChanged);
            this.cbDevicePower.EnabledChanged += new System.EventHandler(this.cbDevicePower_CheckedChanged);
            // 
            // btnWritePower
            // 
            this.btnWritePower.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnWritePower.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnWritePower.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnWritePower.FlatAppearance.BorderSize = 0;
            this.btnWritePower.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnWritePower.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnWritePower.ForeColor = System.Drawing.Color.White;
            this.btnWritePower.Location = new System.Drawing.Point(49, 252);
            this.btnWritePower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnWritePower.Name = "btnWritePower";
            this.btnWritePower.Size = new System.Drawing.Size(223, 26);
            this.btnWritePower.TabIndex = 197;
            this.btnWritePower.Text = "Write (Power) To Schedule";
            this.btnWritePower.UseVisualStyleBackColor = false;
            this.btnWritePower.Click += new System.EventHandler(this.btnWritePower_Click);
            // 
            // cbCurvePower
            // 
            this.cbCurvePower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCurvePower.AutoSize = true;
            this.cbCurvePower.Checked = true;
            this.cbCurvePower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbCurvePower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCurvePower.Location = new System.Drawing.Point(121, 37);
            this.cbCurvePower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCurvePower.Name = "cbCurvePower";
            this.cbCurvePower.Size = new System.Drawing.Size(15, 14);
            this.cbCurvePower.TabIndex = 121;
            this.cbCurvePower.UseVisualStyleBackColor = true;
            this.cbCurvePower.CheckedChanged += new System.EventHandler(this.cbCurvePower_CheckedChanged);
            this.cbCurvePower.EnabledChanged += new System.EventHandler(this.cbCurvePower_CheckedChanged);
            // 
            // rbOverwriteExistingPower
            // 
            this.rbOverwriteExistingPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOverwriteExistingPower.AutoSize = true;
            this.rbOverwriteExistingPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOverwriteExistingPower.Location = new System.Drawing.Point(49, 225);
            this.rbOverwriteExistingPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOverwriteExistingPower.Name = "rbOverwriteExistingPower";
            this.rbOverwriteExistingPower.Size = new System.Drawing.Size(150, 18);
            this.rbOverwriteExistingPower.TabIndex = 193;
            this.rbOverwriteExistingPower.Text = "Overwrite Existing Values";
            this.rbOverwriteExistingPower.UseVisualStyleBackColor = true;
            // 
            // cbRatingPower
            // 
            this.cbRatingPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRatingPower.AutoSize = true;
            this.cbRatingPower.Checked = true;
            this.cbRatingPower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbRatingPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRatingPower.Location = new System.Drawing.Point(49, 37);
            this.cbRatingPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRatingPower.Name = "cbRatingPower";
            this.cbRatingPower.Size = new System.Drawing.Size(15, 14);
            this.cbRatingPower.TabIndex = 121;
            this.cbRatingPower.UseVisualStyleBackColor = true;
            this.cbRatingPower.CheckedChanged += new System.EventHandler(this.cbRatingPower_CheckedChanged);
            this.cbRatingPower.EnabledChanged += new System.EventHandler(this.cbRatingPower_CheckedChanged);
            // 
            // guiInstallPower
            // 
            this.guiInstallPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiInstallPower.FormattingEnabled = true;
            this.guiInstallPower.Location = new System.Drawing.Point(49, 174);
            this.guiInstallPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiInstallPower.Name = "guiInstallPower";
            this.guiInstallPower.Size = new System.Drawing.Size(223, 24);
            this.guiInstallPower.TabIndex = 85;
            // 
            // cbDiversityPower
            // 
            this.cbDiversityPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDiversityPower.AutoSize = true;
            this.cbDiversityPower.Checked = true;
            this.cbDiversityPower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDiversityPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDiversityPower.Location = new System.Drawing.Point(49, 109);
            this.cbDiversityPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDiversityPower.Name = "cbDiversityPower";
            this.cbDiversityPower.Size = new System.Drawing.Size(71, 18);
            this.cbDiversityPower.TabIndex = 121;
            this.cbDiversityPower.Text = "Diversity:";
            this.cbDiversityPower.UseVisualStyleBackColor = true;
            this.cbDiversityPower.CheckedChanged += new System.EventHandler(this.cbDiversityPower_CheckedChanged);
            this.cbDiversityPower.EnabledChanged += new System.EventHandler(this.cbDiversityPower_CheckedChanged);
            // 
            // cbRCDRatingPower
            // 
            this.cbRCDRatingPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRCDRatingPower.AutoSize = true;
            this.cbRCDRatingPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRCDRatingPower.Location = new System.Drawing.Point(49, 64);
            this.cbRCDRatingPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRCDRatingPower.Name = "cbRCDRatingPower";
            this.cbRCDRatingPower.Size = new System.Drawing.Size(151, 18);
            this.cbRCDRatingPower.TabIndex = 86;
            this.cbRCDRatingPower.Text = "RCD Rating (if applicable):";
            this.cbRCDRatingPower.UseVisualStyleBackColor = true;
            this.cbRCDRatingPower.CheckedChanged += new System.EventHandler(this.cbRCDRatingPower_CheckedChanged);
            this.cbRCDRatingPower.EnabledChanged += new System.EventHandler(this.cbRCDRatingPower_CheckedChanged);
            // 
            // guiTxtRevisionPower
            // 
            this.guiTxtRevisionPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRevisionPower.Enabled = false;
            this.guiTxtRevisionPower.Location = new System.Drawing.Point(207, 130);
            this.guiTxtRevisionPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRevisionPower.Name = "guiTxtRevisionPower";
            this.guiTxtRevisionPower.PromptChar = ' ';
            this.guiTxtRevisionPower.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRevisionPower.TabIndex = 120;
            this.guiTxtRevisionPower.Text = "A";
            this.guiTxtRevisionPower.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbDeratingFactorPower
            // 
            this.cbDeratingFactorPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDeratingFactorPower.AutoSize = true;
            this.cbDeratingFactorPower.Checked = true;
            this.cbDeratingFactorPower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDeratingFactorPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDeratingFactorPower.Location = new System.Drawing.Point(49, 87);
            this.cbDeratingFactorPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDeratingFactorPower.Name = "cbDeratingFactorPower";
            this.cbDeratingFactorPower.Size = new System.Drawing.Size(103, 18);
            this.cbDeratingFactorPower.TabIndex = 86;
            this.cbDeratingFactorPower.Text = "Derating Factor:";
            this.cbDeratingFactorPower.UseVisualStyleBackColor = true;
            this.cbDeratingFactorPower.CheckedChanged += new System.EventHandler(this.cbDeratingFactorPower_CheckedChanged);
            this.cbDeratingFactorPower.EnabledChanged += new System.EventHandler(this.cbDeratingFactorPower_CheckedChanged);
            // 
            // cbInstallMethodPower
            // 
            this.cbInstallMethodPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbInstallMethodPower.AutoSize = true;
            this.cbInstallMethodPower.Checked = true;
            this.cbInstallMethodPower.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbInstallMethodPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbInstallMethodPower.Location = new System.Drawing.Point(49, 154);
            this.cbInstallMethodPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbInstallMethodPower.Name = "cbInstallMethodPower";
            this.cbInstallMethodPower.Size = new System.Drawing.Size(114, 18);
            this.cbInstallMethodPower.TabIndex = 119;
            this.cbInstallMethodPower.Text = "Installation Method";
            this.cbInstallMethodPower.UseVisualStyleBackColor = true;
            this.cbInstallMethodPower.CheckedChanged += new System.EventHandler(this.cbInstallMethodPower_CheckedChanged);
            this.cbInstallMethodPower.EnabledChanged += new System.EventHandler(this.cbInstallMethodPower_CheckedChanged);
            // 
            // label9
            // 
            this.label9.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.Location = new System.Drawing.Point(65, 15);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 14);
            this.label9.TabIndex = 83;
            this.label9.Text = "Rating(A)";
            // 
            // cbCircuitRevisionPower
            // 
            this.cbCircuitRevisionPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCircuitRevisionPower.AutoSize = true;
            this.cbCircuitRevisionPower.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCircuitRevisionPower.Location = new System.Drawing.Point(49, 132);
            this.cbCircuitRevisionPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCircuitRevisionPower.Name = "cbCircuitRevisionPower";
            this.cbCircuitRevisionPower.Size = new System.Drawing.Size(103, 18);
            this.cbCircuitRevisionPower.TabIndex = 119;
            this.cbCircuitRevisionPower.Text = "Circuit Revision:";
            this.cbCircuitRevisionPower.UseVisualStyleBackColor = true;
            this.cbCircuitRevisionPower.CheckedChanged += new System.EventHandler(this.cbCircuitRevisionPower_CheckedChanged_1);
            this.cbCircuitRevisionPower.EnabledChanged += new System.EventHandler(this.cbCircuitRevisionPower_CheckedChanged);
            // 
            // label5
            // 
            this.label5.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(138, 15);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(36, 14);
            this.label5.TabIndex = 87;
            this.label5.Text = "Curve";
            // 
            // label4
            // 
            this.label4.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(207, 15);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(30, 14);
            this.label4.TabIndex = 88;
            this.label4.Text = "Type";
            // 
            // guiTxtRatingPower
            // 
            this.guiTxtRatingPower.AllowDrop = true;
            this.guiTxtRatingPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRatingPower.FormattingEnabled = true;
            this.guiTxtRatingPower.Items.AddRange(new object[] {
            "0",
            "2",
            "3",
            "4",
            "6",
            "10",
            "16",
            "20",
            "25",
            "32",
            "40",
            "50",
            "63",
            "80",
            "100"});
            this.guiTxtRatingPower.Location = new System.Drawing.Point(67, 32);
            this.guiTxtRatingPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRatingPower.Name = "guiTxtRatingPower";
            this.guiTxtRatingPower.Size = new System.Drawing.Size(49, 24);
            this.guiTxtRatingPower.TabIndex = 114;
            this.guiTxtRatingPower.Text = "20";
            // 
            // guiTxtCurvePower
            // 
            this.guiTxtCurvePower.AllowDrop = true;
            this.guiTxtCurvePower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtCurvePower.FormattingEnabled = true;
            this.guiTxtCurvePower.Location = new System.Drawing.Point(137, 32);
            this.guiTxtCurvePower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtCurvePower.Name = "guiTxtCurvePower";
            this.guiTxtCurvePower.Size = new System.Drawing.Size(49, 24);
            this.guiTxtCurvePower.TabIndex = 105;
            this.guiTxtCurvePower.Text = "C";
            // 
            // guiTxtDeratingFactorPower
            // 
            this.guiTxtDeratingFactorPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDeratingFactorPower.Location = new System.Drawing.Point(207, 83);
            this.guiTxtDeratingFactorPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDeratingFactorPower.Mask = "000%";
            this.guiTxtDeratingFactorPower.Name = "guiTxtDeratingFactorPower";
            this.guiTxtDeratingFactorPower.PromptChar = ' ';
            this.guiTxtDeratingFactorPower.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDeratingFactorPower.TabIndex = 111;
            this.guiTxtDeratingFactorPower.Text = "90";
            this.guiTxtDeratingFactorPower.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // guiTxtDevicePower
            // 
            this.guiTxtDevicePower.AllowDrop = true;
            this.guiTxtDevicePower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDevicePower.FormattingEnabled = true;
            this.guiTxtDevicePower.Location = new System.Drawing.Point(207, 32);
            this.guiTxtDevicePower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDevicePower.Name = "guiTxtDevicePower";
            this.guiTxtDevicePower.Size = new System.Drawing.Size(67, 24);
            this.guiTxtDevicePower.TabIndex = 108;
            this.guiTxtDevicePower.Text = "MCB";
            // 
            // guiTxtRCDratingPower
            // 
            this.guiTxtRCDratingPower.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRCDratingPower.Enabled = false;
            this.guiTxtRCDratingPower.Location = new System.Drawing.Point(207, 61);
            this.guiTxtRCDratingPower.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRCDratingPower.Name = "guiTxtRCDratingPower";
            this.guiTxtRCDratingPower.PromptChar = ' ';
            this.guiTxtRCDratingPower.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRCDratingPower.TabIndex = 111;
            this.guiTxtRCDratingPower.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.guiTxtDiversityOther);
            this.tabPage3.Controls.Add(this.rbOnlyUnassignedOther);
            this.tabPage3.Controls.Add(this.cbDeviceOther);
            this.tabPage3.Controls.Add(this.btnWriteOther);
            this.tabPage3.Controls.Add(this.cbCurveOther);
            this.tabPage3.Controls.Add(this.cbRatingOther);
            this.tabPage3.Controls.Add(this.rbOverwriteExistingOther);
            this.tabPage3.Controls.Add(this.cbDiversityOther);
            this.tabPage3.Controls.Add(this.guiInstallOther);
            this.tabPage3.Controls.Add(this.guiTxtRevisionOther);
            this.tabPage3.Controls.Add(this.cbRCDRatingOther);
            this.tabPage3.Controls.Add(this.cbInstallMethodOther);
            this.tabPage3.Controls.Add(this.cbDeratingFactorOther);
            this.tabPage3.Controls.Add(this.cbCircuitRevisionOther);
            this.tabPage3.Controls.Add(this.label13);
            this.tabPage3.Controls.Add(this.label12);
            this.tabPage3.Controls.Add(this.guiTxtRatingOther);
            this.tabPage3.Controls.Add(this.label10);
            this.tabPage3.Controls.Add(this.guiTxtDeratingFactorOther);
            this.tabPage3.Controls.Add(this.guiTxtCurveOther);
            this.tabPage3.Controls.Add(this.guiTxtRCDratingOther);
            this.tabPage3.Controls.Add(this.guiTxtDeviceOther);
            this.tabPage3.Location = new System.Drawing.Point(4, 25);
            this.tabPage3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tabPage3.Size = new System.Drawing.Size(325, 291);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "Other Circuits";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // guiTxtDiversityOther
            // 
            this.guiTxtDiversityOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDiversityOther.Location = new System.Drawing.Point(207, 106);
            this.guiTxtDiversityOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDiversityOther.Mask = "000%";
            this.guiTxtDiversityOther.Name = "guiTxtDiversityOther";
            this.guiTxtDiversityOther.PromptChar = ' ';
            this.guiTxtDiversityOther.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDiversityOther.TabIndex = 122;
            this.guiTxtDiversityOther.Text = "50";
            this.guiTxtDiversityOther.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rbOnlyUnassignedOther
            // 
            this.rbOnlyUnassignedOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOnlyUnassignedOther.AutoSize = true;
            this.rbOnlyUnassignedOther.Checked = true;
            this.rbOnlyUnassignedOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOnlyUnassignedOther.Location = new System.Drawing.Point(49, 206);
            this.rbOnlyUnassignedOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOnlyUnassignedOther.Name = "rbOnlyUnassignedOther";
            this.rbOnlyUnassignedOther.Size = new System.Drawing.Size(151, 18);
            this.rbOnlyUnassignedOther.TabIndex = 194;
            this.rbOnlyUnassignedOther.TabStop = true;
            this.rbOnlyUnassignedOther.Text = "Only Populate Unassigned";
            this.rbOnlyUnassignedOther.UseMnemonic = false;
            this.rbOnlyUnassignedOther.UseVisualStyleBackColor = true;
            // 
            // cbDeviceOther
            // 
            this.cbDeviceOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDeviceOther.AutoSize = true;
            this.cbDeviceOther.Checked = true;
            this.cbDeviceOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDeviceOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDeviceOther.Location = new System.Drawing.Point(189, 37);
            this.cbDeviceOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDeviceOther.Name = "cbDeviceOther";
            this.cbDeviceOther.Size = new System.Drawing.Size(15, 14);
            this.cbDeviceOther.TabIndex = 121;
            this.cbDeviceOther.UseVisualStyleBackColor = true;
            this.cbDeviceOther.CheckedChanged += new System.EventHandler(this.cbDeviceOther_CheckedChanged);
            this.cbDeviceOther.EnabledChanged += new System.EventHandler(this.cbDeviceOther_CheckedChanged);
            // 
            // btnWriteOther
            // 
            this.btnWriteOther.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.btnWriteOther.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btnWriteOther.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.btnWriteOther.FlatAppearance.BorderSize = 0;
            this.btnWriteOther.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnWriteOther.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnWriteOther.ForeColor = System.Drawing.Color.White;
            this.btnWriteOther.Location = new System.Drawing.Point(49, 252);
            this.btnWriteOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnWriteOther.Name = "btnWriteOther";
            this.btnWriteOther.Size = new System.Drawing.Size(223, 26);
            this.btnWriteOther.TabIndex = 197;
            this.btnWriteOther.Text = "Write (Other) To Schedule";
            this.btnWriteOther.UseVisualStyleBackColor = false;
            this.btnWriteOther.Click += new System.EventHandler(this.btnWriteOther_Click);
            // 
            // cbCurveOther
            // 
            this.cbCurveOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCurveOther.AutoSize = true;
            this.cbCurveOther.Checked = true;
            this.cbCurveOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbCurveOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCurveOther.Location = new System.Drawing.Point(121, 37);
            this.cbCurveOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCurveOther.Name = "cbCurveOther";
            this.cbCurveOther.Size = new System.Drawing.Size(15, 14);
            this.cbCurveOther.TabIndex = 121;
            this.cbCurveOther.UseVisualStyleBackColor = true;
            this.cbCurveOther.CheckedChanged += new System.EventHandler(this.cbCurveOther_CheckedChanged);
            this.cbCurveOther.EnabledChanged += new System.EventHandler(this.cbCurveOther_CheckedChanged);
            // 
            // cbRatingOther
            // 
            this.cbRatingOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRatingOther.AutoSize = true;
            this.cbRatingOther.Checked = true;
            this.cbRatingOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbRatingOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRatingOther.Location = new System.Drawing.Point(49, 37);
            this.cbRatingOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRatingOther.Name = "cbRatingOther";
            this.cbRatingOther.Size = new System.Drawing.Size(15, 14);
            this.cbRatingOther.TabIndex = 121;
            this.cbRatingOther.UseVisualStyleBackColor = true;
            this.cbRatingOther.CheckedChanged += new System.EventHandler(this.cbRatingOther_CheckedChanged);
            this.cbRatingOther.EnabledChanged += new System.EventHandler(this.cbRatingOther_CheckedChanged);
            // 
            // rbOverwriteExistingOther
            // 
            this.rbOverwriteExistingOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rbOverwriteExistingOther.AutoSize = true;
            this.rbOverwriteExistingOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rbOverwriteExistingOther.Location = new System.Drawing.Point(49, 225);
            this.rbOverwriteExistingOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.rbOverwriteExistingOther.Name = "rbOverwriteExistingOther";
            this.rbOverwriteExistingOther.Size = new System.Drawing.Size(150, 18);
            this.rbOverwriteExistingOther.TabIndex = 193;
            this.rbOverwriteExistingOther.Text = "Overwrite Existing Values";
            this.rbOverwriteExistingOther.UseVisualStyleBackColor = true;
            // 
            // cbDiversityOther
            // 
            this.cbDiversityOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDiversityOther.AutoSize = true;
            this.cbDiversityOther.Checked = true;
            this.cbDiversityOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDiversityOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDiversityOther.Location = new System.Drawing.Point(49, 109);
            this.cbDiversityOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDiversityOther.Name = "cbDiversityOther";
            this.cbDiversityOther.Size = new System.Drawing.Size(71, 18);
            this.cbDiversityOther.TabIndex = 121;
            this.cbDiversityOther.Text = "Diversity:";
            this.cbDiversityOther.UseVisualStyleBackColor = true;
            this.cbDiversityOther.CheckedChanged += new System.EventHandler(this.cbDiversityOther_CheckedChanged);
            this.cbDiversityOther.EnabledChanged += new System.EventHandler(this.cbDiversityOther_CheckedChanged);
            // 
            // guiInstallOther
            // 
            this.guiInstallOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiInstallOther.FormattingEnabled = true;
            this.guiInstallOther.Location = new System.Drawing.Point(49, 174);
            this.guiInstallOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiInstallOther.Name = "guiInstallOther";
            this.guiInstallOther.Size = new System.Drawing.Size(223, 24);
            this.guiInstallOther.TabIndex = 85;
            // 
            // guiTxtRevisionOther
            // 
            this.guiTxtRevisionOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRevisionOther.Enabled = false;
            this.guiTxtRevisionOther.Location = new System.Drawing.Point(207, 130);
            this.guiTxtRevisionOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRevisionOther.Name = "guiTxtRevisionOther";
            this.guiTxtRevisionOther.PromptChar = ' ';
            this.guiTxtRevisionOther.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRevisionOther.TabIndex = 120;
            this.guiTxtRevisionOther.Text = "A";
            this.guiTxtRevisionOther.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbRCDRatingOther
            // 
            this.cbRCDRatingOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbRCDRatingOther.AutoSize = true;
            this.cbRCDRatingOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbRCDRatingOther.Location = new System.Drawing.Point(49, 64);
            this.cbRCDRatingOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbRCDRatingOther.Name = "cbRCDRatingOther";
            this.cbRCDRatingOther.Size = new System.Drawing.Size(151, 18);
            this.cbRCDRatingOther.TabIndex = 86;
            this.cbRCDRatingOther.Text = "RCD Rating (if applicable):";
            this.cbRCDRatingOther.UseVisualStyleBackColor = true;
            this.cbRCDRatingOther.CheckedChanged += new System.EventHandler(this.cbRCDRatingOther_CheckedChanged);
            this.cbRCDRatingOther.EnabledChanged += new System.EventHandler(this.cbRCDRatingOther_CheckedChanged);
            // 
            // cbInstallMethodOther
            // 
            this.cbInstallMethodOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbInstallMethodOther.AutoSize = true;
            this.cbInstallMethodOther.Checked = true;
            this.cbInstallMethodOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbInstallMethodOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbInstallMethodOther.Location = new System.Drawing.Point(49, 154);
            this.cbInstallMethodOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbInstallMethodOther.Name = "cbInstallMethodOther";
            this.cbInstallMethodOther.Size = new System.Drawing.Size(114, 18);
            this.cbInstallMethodOther.TabIndex = 119;
            this.cbInstallMethodOther.Text = "Installation Method";
            this.cbInstallMethodOther.UseVisualStyleBackColor = true;
            this.cbInstallMethodOther.CheckedChanged += new System.EventHandler(this.cbInstallMethodOther_CheckedChanged);
            this.cbInstallMethodOther.EnabledChanged += new System.EventHandler(this.cbInstallMethodOther_CheckedChanged);
            // 
            // cbDeratingFactorOther
            // 
            this.cbDeratingFactorOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbDeratingFactorOther.AutoSize = true;
            this.cbDeratingFactorOther.Checked = true;
            this.cbDeratingFactorOther.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbDeratingFactorOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbDeratingFactorOther.Location = new System.Drawing.Point(49, 87);
            this.cbDeratingFactorOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbDeratingFactorOther.Name = "cbDeratingFactorOther";
            this.cbDeratingFactorOther.Size = new System.Drawing.Size(103, 18);
            this.cbDeratingFactorOther.TabIndex = 86;
            this.cbDeratingFactorOther.Text = "Derating Factor:";
            this.cbDeratingFactorOther.UseVisualStyleBackColor = true;
            this.cbDeratingFactorOther.CheckedChanged += new System.EventHandler(this.cbDeratingFactorOther_CheckedChanged);
            this.cbDeratingFactorOther.EnabledChanged += new System.EventHandler(this.cbDeratingFactorOther_CheckedChanged);
            // 
            // cbCircuitRevisionOther
            // 
            this.cbCircuitRevisionOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.cbCircuitRevisionOther.AutoSize = true;
            this.cbCircuitRevisionOther.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCircuitRevisionOther.Location = new System.Drawing.Point(49, 132);
            this.cbCircuitRevisionOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.cbCircuitRevisionOther.Name = "cbCircuitRevisionOther";
            this.cbCircuitRevisionOther.Size = new System.Drawing.Size(103, 18);
            this.cbCircuitRevisionOther.TabIndex = 119;
            this.cbCircuitRevisionOther.Text = "Circuit Revision:";
            this.cbCircuitRevisionOther.UseVisualStyleBackColor = true;
            this.cbCircuitRevisionOther.CheckedChanged += new System.EventHandler(this.cbCircuitRevisionOther_CheckedChanged);
            this.cbCircuitRevisionOther.EnabledChanged += new System.EventHandler(this.cbCircuitRevisionOther_CheckedChanged);
            // 
            // label13
            // 
            this.label13.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.Location = new System.Drawing.Point(65, 15);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(53, 14);
            this.label13.TabIndex = 83;
            this.label13.Text = "Rating(A)";
            // 
            // label12
            // 
            this.label12.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(138, 15);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(36, 14);
            this.label12.TabIndex = 87;
            this.label12.Text = "Curve";
            // 
            // guiTxtRatingOther
            // 
            this.guiTxtRatingOther.AllowDrop = true;
            this.guiTxtRatingOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRatingOther.FormattingEnabled = true;
            this.guiTxtRatingOther.Items.AddRange(new object[] {
            "0",
            "2",
            "3",
            "4",
            "6",
            "10",
            "16",
            "20",
            "25",
            "32",
            "40",
            "50",
            "63",
            "80",
            "100"});
            this.guiTxtRatingOther.Location = new System.Drawing.Point(67, 32);
            this.guiTxtRatingOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRatingOther.Name = "guiTxtRatingOther";
            this.guiTxtRatingOther.Size = new System.Drawing.Size(49, 24);
            this.guiTxtRatingOther.TabIndex = 114;
            this.guiTxtRatingOther.Text = "20";
            // 
            // label10
            // 
            this.label10.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.Location = new System.Drawing.Point(207, 15);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(30, 14);
            this.label10.TabIndex = 88;
            this.label10.Text = "Type";
            // 
            // guiTxtDeratingFactorOther
            // 
            this.guiTxtDeratingFactorOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDeratingFactorOther.Location = new System.Drawing.Point(207, 83);
            this.guiTxtDeratingFactorOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDeratingFactorOther.Mask = "000%";
            this.guiTxtDeratingFactorOther.Name = "guiTxtDeratingFactorOther";
            this.guiTxtDeratingFactorOther.PromptChar = ' ';
            this.guiTxtDeratingFactorOther.Size = new System.Drawing.Size(67, 23);
            this.guiTxtDeratingFactorOther.TabIndex = 111;
            this.guiTxtDeratingFactorOther.Text = "90";
            this.guiTxtDeratingFactorOther.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // guiTxtCurveOther
            // 
            this.guiTxtCurveOther.AllowDrop = true;
            this.guiTxtCurveOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtCurveOther.FormattingEnabled = true;
            this.guiTxtCurveOther.Location = new System.Drawing.Point(137, 32);
            this.guiTxtCurveOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtCurveOther.Name = "guiTxtCurveOther";
            this.guiTxtCurveOther.Size = new System.Drawing.Size(49, 24);
            this.guiTxtCurveOther.TabIndex = 105;
            this.guiTxtCurveOther.Text = "C";
            // 
            // guiTxtRCDratingOther
            // 
            this.guiTxtRCDratingOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtRCDratingOther.Enabled = false;
            this.guiTxtRCDratingOther.Location = new System.Drawing.Point(207, 61);
            this.guiTxtRCDratingOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtRCDratingOther.Name = "guiTxtRCDratingOther";
            this.guiTxtRCDratingOther.PromptChar = ' ';
            this.guiTxtRCDratingOther.Size = new System.Drawing.Size(67, 23);
            this.guiTxtRCDratingOther.TabIndex = 111;
            this.guiTxtRCDratingOther.Text = "30mA";
            this.guiTxtRCDratingOther.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // guiTxtDeviceOther
            // 
            this.guiTxtDeviceOther.AllowDrop = true;
            this.guiTxtDeviceOther.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.guiTxtDeviceOther.FormattingEnabled = true;
            this.guiTxtDeviceOther.Location = new System.Drawing.Point(207, 32);
            this.guiTxtDeviceOther.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.guiTxtDeviceOther.Name = "guiTxtDeviceOther";
            this.guiTxtDeviceOther.Size = new System.Drawing.Size(67, 24);
            this.guiTxtDeviceOther.TabIndex = 108;
            this.guiTxtDeviceOther.Text = "RCBO";
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 663F));
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel2, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel4, 1, 0);
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 42);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 1;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(1267, 648);
            this.tableLayoutPanel1.TabIndex = 196;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel2.ColumnCount = 1;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.Controls.Add(this.rqNotice, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.dgvDBSel, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.tableLayoutPanel3, 0, 2);
            this.tableLayoutPanel2.Location = new System.Drawing.Point(3, 2);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel2.MinimumSize = new System.Drawing.Size(285, 200);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 3;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 109F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(598, 644);
            this.tableLayoutPanel2.TabIndex = 0;
            // 
            // tableLayoutPanel3
            // 
            this.tableLayoutPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel3.ColumnCount = 1;
            this.tableLayoutPanel3.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.Controls.Add(this.rqDBedit, 0, 0);
            this.tableLayoutPanel3.Controls.Add(this.btnExport, 0, 1);
            this.tableLayoutPanel3.Location = new System.Drawing.Point(3, 537);
            this.tableLayoutPanel3.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            this.tableLayoutPanel3.RowCount = 2;
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel3.Size = new System.Drawing.Size(592, 105);
            this.tableLayoutPanel3.TabIndex = 195;
            // 
            // tableLayoutPanel4
            // 
            this.tableLayoutPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel4.ColumnCount = 1;
            this.tableLayoutPanel4.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel4.Controls.Add(this.tableLayoutPanel5, 0, 2);
            this.tableLayoutPanel4.Controls.Add(this.rqProjectParam, 0, 0);
            this.tableLayoutPanel4.Controls.Add(this.groupBox1, 0, 1);
            this.tableLayoutPanel4.Location = new System.Drawing.Point(607, 2);
            this.tableLayoutPanel4.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel4.MinimumSize = new System.Drawing.Size(603, 200);
            this.tableLayoutPanel4.Name = "tableLayoutPanel4";
            this.tableLayoutPanel4.RowCount = 3;
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 160F));
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel4.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 130F));
            this.tableLayoutPanel4.Size = new System.Drawing.Size(657, 644);
            this.tableLayoutPanel4.TabIndex = 1;
            // 
            // tableLayoutPanel5
            // 
            this.tableLayoutPanel5.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tableLayoutPanel5.ColumnCount = 2;
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.15244F));
            this.tableLayoutPanel5.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 49.84756F));
            this.tableLayoutPanel5.Controls.Add(this.groupBox3, 0, 0);
            this.tableLayoutPanel5.Controls.Add(this.groupBox6, 1, 0);
            this.tableLayoutPanel5.Location = new System.Drawing.Point(3, 516);
            this.tableLayoutPanel5.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.tableLayoutPanel5.Name = "tableLayoutPanel5";
            this.tableLayoutPanel5.RowCount = 1;
            this.tableLayoutPanel5.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel5.Size = new System.Drawing.Size(651, 126);
            this.tableLayoutPanel5.TabIndex = 197;
            // 
            // groupBox6
            // 
            this.groupBox6.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox6.Controls.Add(this.label2);
            this.groupBox6.Controls.Add(this.btnSave);
            this.groupBox6.Controls.Add(this.btnCancel);
            this.groupBox6.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox6.Location = new System.Drawing.Point(329, 2);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.groupBox6.Size = new System.Drawing.Size(319, 122);
            this.groupBox6.TabIndex = 186;
            this.groupBox6.TabStop = false;
            // 
            // ManualLock
            // 
            this.ManualLock.HeaderText = "Manual Lock";
            this.ManualLock.MinimumWidth = 6;
            this.ManualLock.Name = "ManualLock";
            this.ManualLock.ReadOnly = true;
            this.ManualLock.Width = 70;
            // 
            // dBNameDataGridViewTextBoxColumn
            // 
            this.dBNameDataGridViewTextBoxColumn.DataPropertyName = "DB_Name";
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dBNameDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle2;
            this.dBNameDataGridViewTextBoxColumn.HeaderText = "DB Name";
            this.dBNameDataGridViewTextBoxColumn.MinimumWidth = 6;
            this.dBNameDataGridViewTextBoxColumn.Name = "dBNameDataGridViewTextBoxColumn";
            this.dBNameDataGridViewTextBoxColumn.ReadOnly = true;
            this.dBNameDataGridViewTextBoxColumn.ToolTipText = "The name of the DB in Revit";
            this.dBNameDataGridViewTextBoxColumn.Width = 160;
            // 
            // dBPassCountDataGridViewTextBoxColumn
            // 
            this.dBPassCountDataGridViewTextBoxColumn.DataPropertyName = "DB_PassCount";
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dBPassCountDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle3;
            this.dBPassCountDataGridViewTextBoxColumn.HeaderText = "Pass CCTs";
            this.dBPassCountDataGridViewTextBoxColumn.MinimumWidth = 6;
            this.dBPassCountDataGridViewTextBoxColumn.Name = "dBPassCountDataGridViewTextBoxColumn";
            this.dBPassCountDataGridViewTextBoxColumn.ReadOnly = true;
            this.dBPassCountDataGridViewTextBoxColumn.ToolTipText = "Number of circuits that are currently passing";
            this.dBPassCountDataGridViewTextBoxColumn.Width = 70;
            // 
            // dBWarningCountDataGridViewTextBoxColumn
            // 
            this.dBWarningCountDataGridViewTextBoxColumn.DataPropertyName = "DB_WarningCount";
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dBWarningCountDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle4;
            this.dBWarningCountDataGridViewTextBoxColumn.HeaderText = "Warning CCTs";
            this.dBWarningCountDataGridViewTextBoxColumn.MinimumWidth = 6;
            this.dBWarningCountDataGridViewTextBoxColumn.Name = "dBWarningCountDataGridViewTextBoxColumn";
            this.dBWarningCountDataGridViewTextBoxColumn.ReadOnly = true;
            this.dBWarningCountDataGridViewTextBoxColumn.ToolTipText = "Number of circuits with warnings";
            this.dBWarningCountDataGridViewTextBoxColumn.Width = 70;
            // 
            // dBFailCountDataGridViewTextBoxColumn
            // 
            this.dBFailCountDataGridViewTextBoxColumn.DataPropertyName = "DB_FailCount";
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dBFailCountDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle5;
            this.dBFailCountDataGridViewTextBoxColumn.HeaderText = "Fail CCTs";
            this.dBFailCountDataGridViewTextBoxColumn.MinimumWidth = 6;
            this.dBFailCountDataGridViewTextBoxColumn.Name = "dBFailCountDataGridViewTextBoxColumn";
            this.dBFailCountDataGridViewTextBoxColumn.ReadOnly = true;
            this.dBFailCountDataGridViewTextBoxColumn.ToolTipText = "Number of circuits that are currently failing";
            this.dBFailCountDataGridViewTextBoxColumn.Width = 70;
            // 
            // DB_UserNotes
            // 
            this.DB_UserNotes.DataPropertyName = "DB_UserNotes";
            this.DB_UserNotes.HeaderText = "Notes";
            this.DB_UserNotes.Name = "DB_UserNotes";
            this.DB_UserNotes.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.DB_UserNotes.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            // 
            // dBNotesDataGridViewTextBoxColumn
            // 
            this.dBNotesDataGridViewTextBoxColumn.DataPropertyName = "DB_Notes";
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dBNotesDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle6;
            this.dBNotesDataGridViewTextBoxColumn.HeaderText = "Error/Warning";
            this.dBNotesDataGridViewTextBoxColumn.MinimumWidth = 6;
            this.dBNotesDataGridViewTextBoxColumn.Name = "dBNotesDataGridViewTextBoxColumn";
            this.dBNotesDataGridViewTextBoxColumn.ReadOnly = true;
            this.dBNotesDataGridViewTextBoxColumn.ToolTipText = "applicable notes";
            this.dBNotesDataGridViewTextBoxColumn.Width = 125;
            // 
            // DB_Settings
            // 
            this.DB_Settings.DataPropertyName = "DB_Settings";
            this.DB_Settings.HeaderText = "DB Settings";
            this.DB_Settings.MinimumWidth = 6;
            this.DB_Settings.Name = "DB_Settings";
            this.DB_Settings.ReadOnly = true;
            this.DB_Settings.Width = 50;
            // 
            // DB_UpdateRequired
            // 
            this.DB_UpdateRequired.DataPropertyName = "DB_UpdateRequired";
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            dataGridViewCellStyle7.NullValue = false;
            this.DB_UpdateRequired.DefaultCellStyle = dataGridViewCellStyle7;
            this.DB_UpdateRequired.HeaderText = "U.R";
            this.DB_UpdateRequired.MinimumWidth = 6;
            this.DB_UpdateRequired.Name = "DB_UpdateRequired";
            this.DB_UpdateRequired.ReadOnly = true;
            this.DB_UpdateRequired.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.DB_UpdateRequired.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic;
            this.DB_UpdateRequired.ToolTipText = "Update Required: Please run PowerBIM again.";
            this.DB_UpdateRequired.Width = 40;
            // 
            // frmPowerBIM_Start
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.btnHelp_Visiblity = true;
            this.ClientSize = new System.Drawing.Size(1266, 746);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.MinimumSize = new System.Drawing.Size(992, 777);
            this.Name = "frmPowerBIM_Start";
            this.Text = "PowerBIM | Start";
            this.TitleText = "PowerBIM";
            this.VerisionText = "© 2021   01.05.01  ";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.frmPowerBIM_Start_FormClosed);
            this.Load += new System.EventHandler(this.frm_Load);
            this.Controls.SetChildIndex(this.tableLayoutPanel1, 0);
            this.rqProjectParam.ResumeLayout(false);
            this.rqProjectParam.PerformLayout();
            this.pnl_DiscriminationTest.ResumeLayout(false);
            this.pnl_DiscriminationTest.PerformLayout();
            this.pnl_CableDatabase.ResumeLayout(false);
            this.pnl_CableDatabase.PerformLayout();
            this.pnl_SystemMaxVoltDrop.ResumeLayout(false);
            this.pnl_SystemMaxVoltDrop.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDBSel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryTableBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryDataSetBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBSummaryDataSet)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.tabBox.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel3.ResumeLayout(false);
            this.tableLayoutPanel4.ResumeLayout(false);
            this.tableLayoutPanel5.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Button rqSettings;
        private System.Windows.Forms.RadioButton rbOnlyUnassignedLighting;
        private System.Windows.Forms.RadioButton rbOverwriteExistingLighting;
        private System.Windows.Forms.GroupBox rqProjectParam;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel pnl_DiscriminationTest;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Panel pnl_CableDatabase;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Panel pnl_SystemMaxVoltDrop;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Button rqDBedit;
        private System.Windows.Forms.Label rqNotice;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.RadioButton rbSizerOnlyUnassigned;
        private System.Windows.Forms.RadioButton rbSizerOverwriteExisting;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnRun;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnEnhancedCircuitEdit;
        private System.Windows.Forms.DataGridView dgvDBSel;
        private System.Windows.Forms.BindingSource dBSummaryTableBindingSource;
        private System.Windows.Forms.BindingSource dBSummaryDataSetBindingSource;
        private PowerBIM_5.DataSets.DBSummaryDataSet dBSummaryDataSet;
        private System.Windows.Forms.Button btnWriteLighting;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button btnRunSizer;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel4;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel5;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.TabControl tabBox;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.MaskedTextBox guiTxtDiversityLighting;
        private System.Windows.Forms.CheckBox cbDiversityLighting;
        private System.Windows.Forms.MaskedTextBox guiTxtRevisionLighting;
        private System.Windows.Forms.CheckBox cbCircuitRevisionLighting;
        private System.Windows.Forms.ComboBox guiInstallLighting;
        private System.Windows.Forms.ComboBox guiTxtRatingLighting;
        private System.Windows.Forms.MaskedTextBox guiTxtDeratingFactorLighting;
        private System.Windows.Forms.MaskedTextBox guiTxtRCDratingLighting;
        private System.Windows.Forms.ComboBox guiTxtDeviceLighting;
        private System.Windows.Forms.ComboBox guiTxtCurveLighting;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.CheckBox cbDeratingFactorLighting;
        private System.Windows.Forms.CheckBox cbRCDRatingLighting;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.CheckBox cbDeviceLighting;
        private System.Windows.Forms.CheckBox cbCurveLighting;
        private System.Windows.Forms.CheckBox cbRatingLighting;
        private System.Windows.Forms.CheckBox cbInstallMethodLighting;
        private System.Windows.Forms.Button btnProjectParametersSave;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.RadioButton rbOnlyUnassignedPower;
        private System.Windows.Forms.Button btnWritePower;
        private System.Windows.Forms.MaskedTextBox guiTxtDiversityPower;
        private System.Windows.Forms.CheckBox cbDevicePower;
        private System.Windows.Forms.CheckBox cbCurvePower;
        private System.Windows.Forms.CheckBox cbRatingPower;
        private System.Windows.Forms.CheckBox cbDiversityPower;
        private System.Windows.Forms.MaskedTextBox guiTxtRevisionPower;
        private System.Windows.Forms.CheckBox cbInstallMethodPower;
        private System.Windows.Forms.CheckBox cbCircuitRevisionPower;
        private System.Windows.Forms.ComboBox guiInstallPower;
        private System.Windows.Forms.ComboBox guiTxtRatingPower;
        private System.Windows.Forms.MaskedTextBox guiTxtDeratingFactorPower;
        private System.Windows.Forms.MaskedTextBox guiTxtRCDratingPower;
        private System.Windows.Forms.ComboBox guiTxtDevicePower;
        private System.Windows.Forms.ComboBox guiTxtCurvePower;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.CheckBox cbDeratingFactorPower;
        private System.Windows.Forms.CheckBox cbRCDRatingPower;
        private System.Windows.Forms.RadioButton rbOverwriteExistingPower;
        private System.Windows.Forms.RadioButton rbOnlyUnassignedOther;
        private System.Windows.Forms.Button btnWriteOther;
        private System.Windows.Forms.MaskedTextBox guiTxtDiversityOther;
        private System.Windows.Forms.CheckBox cbDeviceOther;
        private System.Windows.Forms.CheckBox cbCurveOther;
        private System.Windows.Forms.CheckBox cbRatingOther;
        private System.Windows.Forms.CheckBox cbDiversityOther;
        private System.Windows.Forms.MaskedTextBox guiTxtRevisionOther;
        private System.Windows.Forms.CheckBox cbInstallMethodOther;
        private System.Windows.Forms.CheckBox cbCircuitRevisionOther;
        private System.Windows.Forms.ComboBox guiInstallOther;
        private System.Windows.Forms.ComboBox guiTxtRatingOther;
        private System.Windows.Forms.MaskedTextBox guiTxtDeratingFactorOther;
        private System.Windows.Forms.MaskedTextBox guiTxtRCDratingOther;
        private System.Windows.Forms.ComboBox guiTxtDeviceOther;
        private System.Windows.Forms.ComboBox guiTxtCurveOther;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.CheckBox cbDeratingFactorOther;
        private System.Windows.Forms.CheckBox cbRCDRatingOther;
        private System.Windows.Forms.RadioButton rbOverwriteExistingOther;
        public System.Windows.Forms.RadioButton guiAUScableSel;
        public System.Windows.Forms.RadioButton guiNZcableSel;
        public System.Windows.Forms.ComboBox rqCPDrange;
        public System.Windows.Forms.RadioButton guiDiscrim1pt5Times;
        public System.Windows.Forms.RadioButton guiDiscrim2Times;
        public System.Windows.Forms.RadioButton guiSysVD7pc;
        public System.Windows.Forms.RadioButton guiSysVD5pc;
        private DataGridViewImageColumn ManualLock;
        private DataGridViewTextBoxColumn dBNameDataGridViewTextBoxColumn;
        private DataGridViewTextBoxColumn dBPassCountDataGridViewTextBoxColumn;
        private DataGridViewTextBoxColumn dBWarningCountDataGridViewTextBoxColumn;
        private DataGridViewTextBoxColumn dBFailCountDataGridViewTextBoxColumn;
        private DataGridViewButtonColumn DB_UserNotes;
        private DataGridViewTextBoxColumn dBNotesDataGridViewTextBoxColumn;
        private DataGridViewButtonColumn DB_Settings;
        private DataGridViewCheckBoxColumn DB_UpdateRequired;
    }
}