{"project": "Revit API WinForms to WPF MVVM Migration", "context": "This is a Revit API tool written in C# using a modeless WinForms UI. The tool is fully functional with no issues but the codebase is messy and poorly architected. I need to migrate it to a WPF MVVM structure using CommunityToolkit.Mvvm, while fully preserving its current functionality and modeless behavior inside Revit.", "coreObjective": ["Migrate from modeless WinForms to modeless WPF using MVVM CommunityToolkit.", "Preserve all existing functionalities exactly as they currently work.", "Cleanly separate Views, ViewModels, Models, and Services.", "Follow MVVM best practices with CommunityToolkit ([RelayCommand], [ObservableProperty]).", "Ensure full modeless support in Revit using the proven ExternalEvent architecture."], "keyConstraints": ["No changes to existing functionality — behavior must remain identical.", "Always cross-check with 'PB6 WPF Conversion/MEP.PowerBIM_1.5' for behavior parity.", "Use CommunityToolkit source generators for Commands and PropertyChanged implementations.", "Respect modeless window management and ExternalEvent architecture for Revit API safety."], "referenceProject": "PB6 WPF Conversion/MEP.PowerBIM_1.5", "conversionTarget": "PB6 WPF Conversion/MEP.PowerBIM_6", "importantFiles": {"entryPoint": "BecaPowerBIMMain.cs", "priorityForms": ["frmPowerBIM_Start.cs", "frmPowerBIM_DbEdit.cs", "frmPowerBIM_DbEdit_ImportSettings.cs", "frmPowerBIM_AdvancedSettings.cs", "frmPowerBIM_DBSettings.cs", "frmPowerBIM_Export.cs"], "criticalComplexForm": "FrmPowerBIM_CircuitEditEnhanced.cs", "criticalFormHelpers": ["EditCircuitPathClicker.cs", "EditDBPathClicker.cs"]}, "targetFolders": {"revitCommands": "PB6 WPF Conversion/MEP.PowerBIM_6/RevitCommands/PowerBIM_6_Command.cs", "views": "PB6 WPF Conversion/MEP.PowerBIM_6/Views", "viewModels": "PB6 WPF Conversion/MEP.PowerBIM_6/ViewModels", "models": "PB6 WPF Conversion/MEP.PowerBIM_6/Models", "services": "PB6 WPF Conversion/MEP.PowerBIM_6/Services", "converters": "PB6 WPF Conversion/MEP.PowerBIM_6/Converters"}, "xamlResourceGuidelines": {"namespaceDeclaration": "xmlns:converters=\"clr-namespace:MEP.PowerBIM_6.Converters\"", "requiredMergedDictionaries": ["<ResourceDictionary.MergedDictionaries>", "    <ResourceDictionary Source=\"pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml\" />", "</ResourceDictionary.MergedDictionaries>"], "requiredConverters": ["<converters:BooleanToVisibilityConverter x:Key=\"BoolToVisConverter\" />", "<converters:NumericFormatConverter x:Key=\"NumericFormatConverter\" />"], "placementNotes": "The MergedDictionaries and Converters must always be included inside <Window.Resources> in every WPF View that is converted."}, "uiDesignGuidelines": {"theme": "Use MaterialDesignInXaml toolkit with namespace xmlns:materialDesign=\"http://materialdesigninxaml.net/winfx/xaml/themes\" for all WPF Views.", "baseStyling": ["All converted WPF Windows and Pages must have a white background as the primary base color.", "Use the provided BecaMainDictionary.xaml via MergedDictionaries for additional styling and consistency."], "visualConsistency": ["Preserve the original control layout, positions, and flow unless an improved modern design is recommended.", "Ensure button positions, text fields, labels, and control grouping remain familiar to the original users."], "layoutRecommendations": ["Consider modern Material Design navigation patterns such as side navigation drawers, top app bars, or tabbed interfaces if it improves the user flow.", "For simple forms, use centered card-based layouts to modernize the look without overwhelming the user.", "For multi-step workflows, suggest using dialogs, wizards, or tabbed layouts to improve flow.", "Do not hard-code widths or heights unless required for exact alignment."], "controlMappings": ["Map WinForms controls to their WPF equivalents using MaterialDesign styles where applicable (e.g., MaterialDesign:TextBox, MaterialDesign:<PERSON><PERSON>, MaterialDesign:DataGrid).", "Use WPF's built-in layout panels (Grid, StackPanel, DockPanel) for responsive layouts, favoring Grid for form-based input pages."], "stylingNotes": ["Leverage MaterialDesignInXaml's style guides for buttons, textboxes, data grids, and progress indicators.", "Use Material Design's Elevation, Padding, and Typography principles to give the UI a modern, clean appearance.", "Ensure consistent margin and spacing across all views following Material Design spacing units."], "recommendationRequest": "Please recommend the most appropriate modern WPF layout pattern for each form: Single-Window, Tabbed Interface, Navigation Drawer, Dialog-Based, or Multi-Page Navigation. Suggest the most intuitive user experience for each, based on the form's complexity and purpose.", "uiTestingRequirement": "Perform visual comparisons of each converted WPF view against the original WinForms UI to ensure both functional and visual consistency. Suggest improvements only if they align with Material Design and enhance user experience without disrupting existing workflows."}, "modelessArchitecture": {"modelessMainWindowHandler": {"role": "Manages modeless window lifecycle, creates RequestHandler and ExternalEvent, initializes ViewModel with dependencies, enforces singleton pattern."}, "request": {"role": "Defines request messages and communication protocol. Provides thread-safe queuing via Request_PB6_Configure and enumerates all request types."}, "requestHandler": {"role": "Implements IExternalEventHandler. Executes requests in Revit context safely. Switches on request type and routes execution to ModelessMainWindowHandler methods."}, "viewModel": {"role": "Manages UI logic and state. Uses [RelayCommand] to post requests to RequestHandler and raise ExternalEvent. Fully decoupled from Revit API logic."}}, "communicationFlow": ["User interacts with WPF UI → ViewModel Command triggers → handler.Request.Make(RequestId) → externalEvent.Raise() → Revit invokes RequestHandler.Execute() → RequestHandler processes request → Calls ModelessMainWindowHandler → Performs Revit API operation → Updates UI via ViewModel"], "phases": [{"phase": "Codebase Analysis", "tasks": ["Analyze all forms and files in PB6 WPF Conversion/MEP.PowerBIM_1.5.", "Extract and map all Revit API dependencies and event handlers.", "Identify UI logic tightly coupled to WinForms that requires refactoring."]}, {"phase": "Architecture Planning", "tasks": ["Propose WPF MVVM folder and class structure based on existing PB6 WPF Conversion/MEP.PowerBIM_6 layout.", "Confirm modeless window management structure using ModelessMainWindowHandler.", "Design migration steps prioritizing least complex forms first."]}, {"phase": "Incremental Conversion", "tasks": ["Convert startup sequence using PowerBIM_6_Command.cs.", "Migrate frmPowerBIM_Start.cs to WPF Window with ViewModel using CommunityToolkit and MaterialDesignInXaml components.", "Adapt ExternalEvent communication to MVVM structure.", "Test each conversion step against the reference behavior in PB6 WPF Conversion/MEP.PowerBIM_1.5, including visual and layout comparisons."]}, {"phase": "Critical UI Conversion", "tasks": ["Convert FrmPowerBIM_CircuitEditEnhanced.cs to WPF with ViewModel using MaterialDesign components.", "Migrate EditCircuitPathClicker.cs and EditDBPathClicker.cs to WPF-friendly services or ViewModel helpers.", "Validate complex workflows in FrmPowerBIM_CircuitEditEnhanced.cs against the reference project, including UI design and navigation recommendations."]}, {"phase": "Full Validation", "tasks": ["Run complete regression testing comparing WPF and WinForms behavior, both functionally and visually.", "Ensure all Revit API operations, data updates, and UI feedback work identically.", "Provide final review and cleanup of folder structure, naming conventions, XAML styling, and code organization."]}], "additionalRequirements": ["Document migration progress step-by-step.", "Explain design decisions and refactoring choices in comments or documentation files.", "Prioritize safety and stability over aggressive refactoring."]}