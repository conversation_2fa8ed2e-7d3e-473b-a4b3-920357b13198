using System;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a PowerBIM Cable for WPF binding
    /// Wraps PowerBIM_CableData with ObservableObject for MVVM
    /// </summary>
    public partial class CableModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_CableData _originalData;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Cable name
        /// </summary>
        [ObservableProperty]
        private string _cableName = string.Empty;

        /// <summary>
        /// Cable index
        /// </summary>
        [ObservableProperty]
        private int _cableIndex;

        /// <summary>
        /// Indicates if this is cable to first
        /// </summary>
        [ObservableProperty]
        private bool _isCableToFirst;

        /// <summary>
        /// Specific resistance active
        /// </summary>
        [ObservableProperty]
        private double _spActive;

        /// <summary>
        /// Specific resistance earth
        /// </summary>
        [ObservableProperty]
        private double _spEarth;

        /// <summary>
        /// Rated resistance active
        /// </summary>
        [ObservableProperty]
        private double _rRatedActive;

        /// <summary>
        /// Rated resistance earth
        /// </summary>
        [ObservableProperty]
        private double _rRatedEarth;

        /// <summary>
        /// Operating resistance active
        /// </summary>
        [ObservableProperty]
        private double _rOperatingActive;

        /// <summary>
        /// Operating resistance earth
        /// </summary>
        [ObservableProperty]
        private double _rOperatingEarth;

        /// <summary>
        /// Maximum reactance active
        /// </summary>
        [ObservableProperty]
        private double _xMaxActive;

        /// <summary>
        /// Maximum reactance earth
        /// </summary>
        [ObservableProperty]
        private double _xMaxEarth;

        /// <summary>
        /// Operating impedance active
        /// </summary>
        [ObservableProperty]
        private double _zOperatingActive;

        /// <summary>
        /// Operating impedance earth
        /// </summary>
        [ObservableProperty]
        private double _zOperatingEarth;

        /// <summary>
        /// Rated current
        /// </summary>
        [ObservableProperty]
        private double _iRated;

        /// <summary>
        /// Temperature
        /// </summary>
        [ObservableProperty]
        private double _temperature;

        /// <summary>
        /// K value
        /// </summary>
        [ObservableProperty]
        private double _kValue;

        /// <summary>
        /// Maximum I2t value
        /// </summary>
        [ObservableProperty]
        private double _i2tMax;

        /// <summary>
        /// Conductor material
        /// </summary>
        [ObservableProperty]
        private string _conductorMaterial = string.Empty;

        /// <summary>
        /// Insulation material
        /// </summary>
        [ObservableProperty]
        private string _insulationMaterial = string.Empty;

        /// <summary>
        /// Cable temperature limit
        /// </summary>
        [ObservableProperty]
        private double _cableTemperatureLimit;

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        [ObservableProperty]
        private bool _dataGood;

        /// <summary>
        /// Error message
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        /// <summary>
        /// Warning for user defined cable selected
        /// </summary>
        [ObservableProperty]
        private bool _warningUserDefCableSelected;

        /// <summary>
        /// Warning for emergency lighting present
        /// </summary>
        [ObservableProperty]
        private bool _warningEmLightingPresent;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_CableData
        /// </summary>
        public PowerBIM_CableData OriginalData => _originalData;

        /// <summary>
        /// Indicates if the cable data is valid
        /// </summary>
        public bool IsValid => DataGood && !HasErrors;

        /// <summary>
        /// Indicates if this is an invalid cable
        /// </summary>
        public bool IsInvalidCable => CableName == PowerBIM_CableData.InvalidCableName;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the CableModel with PowerBIM_CableData
        /// </summary>
        /// <param name="cableData">Original cable data</param>
        public CableModel(PowerBIM_CableData cableData)
        {
            _originalData = cableData ?? throw new ArgumentNullException(nameof(cableData));
            
            // Initialize properties from original data
            LoadFromOriginalData();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_CableData
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                CableName = _originalData.Cable_Name ?? string.Empty;
                CableIndex = _originalData.Cable_Index;
                IsCableToFirst = _originalData.is_CableToFirst;
                SpActive = _originalData.Sp_Active;
                SpEarth = _originalData.Sp_Earth;
                RRatedActive = _originalData.R_Rated_Active;
                RRatedEarth = _originalData.R_Rated_Earth;
                ROperatingActive = _originalData.R_Operating_Active;
                ROperatingEarth = _originalData.R_Operating_Earth;
                XMaxActive = _originalData.X_Max_Active;
                XMaxEarth = _originalData.X_Max_Earth;
                ZOperatingActive = _originalData.Z_Operating_Active;
                ZOperatingEarth = _originalData.Z_Operating_Earth;
                IRated = _originalData.I_Rated;
                Temperature = _originalData.Temperature;
                KValue = _originalData.K_Value;
                I2tMax = _originalData.I2t_Max;
                ConductorMaterial = _originalData.Conductor_Material ?? string.Empty;
                InsulationMaterial = _originalData.Insulation_Material ?? string.Empty;
                CableTemperatureLimit = _originalData.Cable_Temperature_Limit;
                DataGood = _originalData.Data_Good;
                ErrorMessage = _originalData.Error_Message ?? string.Empty;
                WarningUserDefCableSelected = _originalData.Warning_UserDefCableSelected;
                WarningEmLightingPresent = _originalData.Warning_EmLightingPresent;
                
                // Update validation status
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_CableData
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.Cable_Name = CableName;
                _originalData.Cable_Index = CableIndex;
                _originalData.is_CableToFirst = IsCableToFirst;
                _originalData.Sp_Active = SpActive;
                _originalData.Sp_Earth = SpEarth;
                _originalData.R_Rated_Active = RRatedActive;
                _originalData.R_Rated_Earth = RRatedEarth;
                _originalData.R_Operating_Active = ROperatingActive;
                _originalData.R_Operating_Earth = ROperatingEarth;
                _originalData.X_Max_Active = XMaxActive;
                _originalData.X_Max_Earth = XMaxEarth;
                _originalData.Z_Operating_Active = ZOperatingActive;
                _originalData.Z_Operating_Earth = ZOperatingEarth;
                _originalData.I_Rated = IRated;
                _originalData.Temperature = Temperature;
                _originalData.K_Value = KValue;
                _originalData.I2t_Max = I2tMax;
                _originalData.Conductor_Material = ConductorMaterial;
                _originalData.Insulation_Material = InsulationMaterial;
                _originalData.Cable_Temperature_Limit = CableTemperatureLimit;
                _originalData.Data_Good = DataGood;
                _originalData.Error_Message = ErrorMessage;
                _originalData.Warning_UserDefCableSelected = WarningUserDefCableSelected;
                _originalData.Warning_EmLightingPresent = WarningEmLightingPresent;
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        /// <summary>
        /// Update cable data from original calculations
        /// </summary>
        public void UpdateCableData()
        {
            try
            {
                _originalData?.UpdateCableData();
                LoadFromOriginalData();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error updating cable data: {ex.Message}";
                HasErrors = true;
                DataGood = false;
            }
        }

        /// <summary>
        /// Create null entry for invalid cable
        /// </summary>
        public void CreateNullEntry()
        {
            try
            {
                _originalData?.CreateNullEntry();
                LoadFromOriginalData();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Error creating null entry: {ex.Message}";
                HasErrors = true;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validate the cable data
        /// </summary>
        private void ValidateData()
        {
            try
            {
                HasErrors = !DataGood || !string.IsNullOrEmpty(ErrorMessage) || IsInvalidCable;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle DataGood property change
        /// </summary>
        partial void OnDataGoodChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        /// <summary>
        /// Handle CableName property change
        /// </summary>
        partial void OnCableNameChanged(string value)
        {
            OnPropertyChanged(nameof(IsInvalidCable));
            ValidateData();
        }

        #endregion
    }
}
