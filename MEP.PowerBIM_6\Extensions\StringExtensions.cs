using System;
using System.Text.RegularExpressions;

namespace MEP.PowerBIM_6.Extensions
{
    /// <summary>
    /// Extension methods for string operations
    /// </summary>
    public static class StringExtensions
    {
        /// <summary>
        /// Extract numeric part from a string
        /// Used for circuit number sorting and processing
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Numeric part as string</returns>
        public static string ExtractNumber(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return "0";

            try
            {
                // Use regex to extract the first number found in the string
                Match match = Regex.Match(input, @"\d+");
                if (match.Success)
                {
                    return match.Value;
                }
            }
            catch
            {
                // If regex fails, try manual extraction
            }

            // Fallback: extract digits manually
            var result = string.Empty;
            foreach (char c in input)
            {
                if (char.IsDigit(c))
                {
                    result += c;
                }
                else if (!string.IsNullOrEmpty(result))
                {
                    // Stop at first non-digit after finding digits
                    break;
                }
            }

            return string.IsNullOrEmpty(result) ? "0" : result;
        }

        /// <summary>
        /// Check if string contains another string (case insensitive)
        /// </summary>
        /// <param name="source">Source string</param>
        /// <param name="value">Value to search for</param>
        /// <param name="comparison">String comparison type</param>
        /// <returns>True if contains, false otherwise</returns>
        public static bool Contains(this string source, string value, StringComparison comparison)
        {
            if (source == null || value == null)
                return false;

            return source.IndexOf(value, comparison) >= 0;
        }

        /// <summary>
        /// Safe substring that doesn't throw exceptions
        /// </summary>
        /// <param name="source">Source string</param>
        /// <param name="startIndex">Start index</param>
        /// <param name="length">Length</param>
        /// <returns>Substring or empty string if invalid</returns>
        public static string SafeSubstring(this string source, int startIndex, int length)
        {
            if (string.IsNullOrEmpty(source) || startIndex < 0 || startIndex >= source.Length)
                return string.Empty;

            if (startIndex + length > source.Length)
                length = source.Length - startIndex;

            if (length <= 0)
                return string.Empty;

            return source.Substring(startIndex, length);
        }

        /// <summary>
        /// Convert string to title case
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Title case string</returns>
        public static string ToTitleCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            var words = input.Split(' ');
            for (int i = 0; i < words.Length; i++)
            {
                if (words[i].Length > 0)
                {
                    words[i] = char.ToUpper(words[i][0]) + 
                              (words[i].Length > 1 ? words[i].Substring(1).ToLower() : string.Empty);
                }
            }

            return string.Join(" ", words);
        }

        /// <summary>
        /// Remove extra whitespace and normalize string
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Normalized string</returns>
        public static string NormalizeWhitespace(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // Replace multiple whitespace with single space
            return Regex.Replace(input.Trim(), @"\s+", " ");
        }

        /// <summary>
        /// Check if string is numeric
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>True if numeric, false otherwise</returns>
        public static bool IsNumeric(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            return double.TryParse(input, out _);
        }

        /// <summary>
        /// Convert to double with fallback
        /// </summary>
        /// <param name="input">Input string</param>
        /// <param name="defaultValue">Default value if conversion fails</param>
        /// <returns>Double value</returns>
        public static double ToDoubleOrDefault(this string input, double defaultValue = 0.0)
        {
            if (string.IsNullOrEmpty(input))
                return defaultValue;

            return double.TryParse(input, out double result) ? result : defaultValue;
        }

        /// <summary>
        /// Convert to int with fallback
        /// </summary>
        /// <param name="input">Input string</param>
        /// <param name="defaultValue">Default value if conversion fails</param>
        /// <returns>Integer value</returns>
        public static int ToIntOrDefault(this string input, int defaultValue = 0)
        {
            if (string.IsNullOrEmpty(input))
                return defaultValue;

            return int.TryParse(input, out int result) ? result : defaultValue;
        }
    }
}
