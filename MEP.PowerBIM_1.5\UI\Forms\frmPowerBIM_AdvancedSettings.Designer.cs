﻿namespace MEP.PowerBIM_5.UI.Forms
{
    partial class frmPowerBIM_AdvancedSettings
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmPowerBIM_AdvancedSettings));
            this.rqSettingsCancel = new System.Windows.Forms.Button();
            this.rqSettingsSubmit = new System.Windows.Forms.Button();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.radioButtonNonGPO04 = new System.Windows.Forms.RadioButton();
            this.radioButtonNonGPO5 = new System.Windows.Forms.RadioButton();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.radioButtonLightingClearning04 = new System.Windows.Forms.RadioButton();
            this.radioButtonLightingClearning5 = new System.Windows.Forms.RadioButton();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.label5 = new System.Windows.Forms.Label();
            this.tb_VoltDropBreakerRatingPercent = new System.Windows.Forms.TextBox();
            this.rqGPONotice = new System.Windows.Forms.Label();
            this.radioButtonGPOCalcAssignedLoad = new System.Windows.Forms.RadioButton();
            this.radioButtonGPOCalcBreakerRating = new System.Windows.Forms.RadioButton();
            this.radioButtonGPOCalc1000plus100 = new System.Windows.Forms.RadioButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox10 = new System.Windows.Forms.GroupBox();
            this.rb_OtherLinearDeprecating = new System.Windows.Forms.RadioButton();
            this.rb_OtherLumpLoad = new System.Windows.Forms.RadioButton();
            this.groupBox9 = new System.Windows.Forms.GroupBox();
            this.rb_PowerLinearDeprecating = new System.Windows.Forms.RadioButton();
            this.rb_PowerLumpLoad = new System.Windows.Forms.RadioButton();
            this.groupBox8 = new System.Windows.Forms.GroupBox();
            this.rb_LightingLinearDeprecating = new System.Windows.Forms.RadioButton();
            this.rb_LightingLumpLoad = new System.Windows.Forms.RadioButton();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.rqCableDataFile = new System.Windows.Forms.TextBox();
            this.reEnableNewData = new System.Windows.Forms.Button();
            this.rqEditDatabase = new System.Windows.Forms.Button();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.cbExtraLengthElem = new System.Windows.Forms.CheckBox();
            this.cbExtraLenPerCCT = new System.Windows.Forms.CheckBox();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.tbTxtLenPerCCTOther = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.tbTxtLenPerCCTPower = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.tbTxtLenPerCCTLighting = new System.Windows.Forms.TextBox();
            this.tbTxtLenPerElemOther = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.tbTxtLenPerElemPower = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.tbTxtLenPerElemLighting = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBox7 = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tbTxtRevitPathNodeTollerance = new System.Windows.Forms.MaskedTextBox();
            this.gb_MainSettings = new System.Windows.Forms.GroupBox();
            this.chb_ControlGlobally = new System.Windows.Forms.CheckBox();
            this.enhancedCCTTableBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.enhancedCCTDataSet = new MEP.PowerBIM_5.DataSets.EnhancedCCTDataSet();
            this.groupBox4.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.groupBox10.SuspendLayout();
            this.groupBox9.SuspendLayout();
            this.groupBox8.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.groupBox7.SuspendLayout();
            this.gb_MainSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.enhancedCCTTableBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.enhancedCCTDataSet)).BeginInit();
            this.SuspendLayout();
            // 
            // rqSettingsCancel
            // 
            this.rqSettingsCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.rqSettingsCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqSettingsCancel.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqSettingsCancel.ForeColor = System.Drawing.Color.White;
            this.rqSettingsCancel.Location = new System.Drawing.Point(213, 702);
            this.rqSettingsCancel.Margin = new System.Windows.Forms.Padding(2);
            this.rqSettingsCancel.Name = "rqSettingsCancel";
            this.rqSettingsCancel.Size = new System.Drawing.Size(138, 38);
            this.rqSettingsCancel.TabIndex = 191;
            this.rqSettingsCancel.Text = "Cancel";
            this.rqSettingsCancel.UseVisualStyleBackColor = false;
            this.rqSettingsCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // rqSettingsSubmit
            // 
            this.rqSettingsSubmit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.rqSettingsSubmit.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.rqSettingsSubmit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqSettingsSubmit.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqSettingsSubmit.ForeColor = System.Drawing.Color.Black;
            this.rqSettingsSubmit.Location = new System.Drawing.Point(69, 702);
            this.rqSettingsSubmit.Margin = new System.Windows.Forms.Padding(2);
            this.rqSettingsSubmit.Name = "rqSettingsSubmit";
            this.rqSettingsSubmit.Size = new System.Drawing.Size(138, 38);
            this.rqSettingsSubmit.TabIndex = 190;
            this.rqSettingsSubmit.Text = "OK";
            this.rqSettingsSubmit.UseVisualStyleBackColor = false;
            this.rqSettingsSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.radioButtonNonGPO04);
            this.groupBox4.Controls.Add(this.radioButtonNonGPO5);
            this.groupBox4.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox4.Location = new System.Drawing.Point(7, 315);
            this.groupBox4.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox4.Size = new System.Drawing.Size(392, 54);
            this.groupBox4.TabIndex = 186;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "Non-GPO Clearing Times";
            // 
            // radioButtonNonGPO04
            // 
            this.radioButtonNonGPO04.AutoSize = true;
            this.radioButtonNonGPO04.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonNonGPO04.Location = new System.Drawing.Point(24, 24);
            this.radioButtonNonGPO04.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonNonGPO04.Name = "radioButtonNonGPO04";
            this.radioButtonNonGPO04.Size = new System.Drawing.Size(58, 18);
            this.radioButtonNonGPO04.TabIndex = 120;
            this.radioButtonNonGPO04.Text = "0.4sec";
            this.toolTip1.SetToolTip(this.radioButtonNonGPO04, "Select whether to use 0.4seconds or 5seconds as the required clearing time for al" +
        "l power circuits that are not \"GPOs\"\r\n");
            this.radioButtonNonGPO04.UseVisualStyleBackColor = true;
            // 
            // radioButtonNonGPO5
            // 
            this.radioButtonNonGPO5.AutoSize = true;
            this.radioButtonNonGPO5.Checked = true;
            this.radioButtonNonGPO5.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonNonGPO5.Location = new System.Drawing.Point(176, 24);
            this.radioButtonNonGPO5.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonNonGPO5.Name = "radioButtonNonGPO5";
            this.radioButtonNonGPO5.Size = new System.Drawing.Size(49, 18);
            this.radioButtonNonGPO5.TabIndex = 118;
            this.radioButtonNonGPO5.TabStop = true;
            this.radioButtonNonGPO5.Text = "5sec";
            this.toolTip1.SetToolTip(this.radioButtonNonGPO5, "Select whether to use 0.4seconds or 5seconds as the required clearing time for al" +
        "l power circuits that are not \"GPOs\"\r\n\r\n");
            this.radioButtonNonGPO5.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.radioButtonLightingClearning04);
            this.groupBox3.Controls.Add(this.radioButtonLightingClearning5);
            this.groupBox3.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox3.Location = new System.Drawing.Point(7, 252);
            this.groupBox3.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox3.Size = new System.Drawing.Size(392, 54);
            this.groupBox3.TabIndex = 187;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "Lighting Circuit Clearing Times";
            // 
            // radioButtonLightingClearning04
            // 
            this.radioButtonLightingClearning04.AutoSize = true;
            this.radioButtonLightingClearning04.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonLightingClearning04.Location = new System.Drawing.Point(24, 24);
            this.radioButtonLightingClearning04.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonLightingClearning04.Name = "radioButtonLightingClearning04";
            this.radioButtonLightingClearning04.Size = new System.Drawing.Size(58, 18);
            this.radioButtonLightingClearning04.TabIndex = 120;
            this.radioButtonLightingClearning04.Text = "0.4sec";
            this.toolTip1.SetToolTip(this.radioButtonLightingClearning04, resources.GetString("radioButtonLightingClearning04.ToolTip"));
            this.radioButtonLightingClearning04.UseVisualStyleBackColor = true;
            // 
            // radioButtonLightingClearning5
            // 
            this.radioButtonLightingClearning5.AutoSize = true;
            this.radioButtonLightingClearning5.Checked = true;
            this.radioButtonLightingClearning5.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonLightingClearning5.Location = new System.Drawing.Point(176, 24);
            this.radioButtonLightingClearning5.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonLightingClearning5.Name = "radioButtonLightingClearning5";
            this.radioButtonLightingClearning5.Size = new System.Drawing.Size(49, 18);
            this.radioButtonLightingClearning5.TabIndex = 118;
            this.radioButtonLightingClearning5.TabStop = true;
            this.radioButtonLightingClearning5.Text = "5sec";
            this.toolTip1.SetToolTip(this.radioButtonLightingClearning5, resources.GetString("radioButtonLightingClearning5.ToolTip"));
            this.radioButtonLightingClearning5.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.label5);
            this.groupBox2.Controls.Add(this.tb_VoltDropBreakerRatingPercent);
            this.groupBox2.Controls.Add(this.rqGPONotice);
            this.groupBox2.Controls.Add(this.radioButtonGPOCalcAssignedLoad);
            this.groupBox2.Controls.Add(this.radioButtonGPOCalcBreakerRating);
            this.groupBox2.Controls.Add(this.radioButtonGPOCalc1000plus100);
            this.groupBox2.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox2.Location = new System.Drawing.Point(6, 152);
            this.groupBox2.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox2.Size = new System.Drawing.Size(393, 94);
            this.groupBox2.TabIndex = 188;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "GPOs";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(98, 20);
            this.label5.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(17, 16);
            this.label5.TabIndex = 131;
            this.label5.Text = "%";
            // 
            // tb_VoltDropBreakerRatingPercent
            // 
            this.tb_VoltDropBreakerRatingPercent.Location = new System.Drawing.Point(43, 16);
            this.tb_VoltDropBreakerRatingPercent.Name = "tb_VoltDropBreakerRatingPercent";
            this.tb_VoltDropBreakerRatingPercent.Size = new System.Drawing.Size(54, 22);
            this.tb_VoltDropBreakerRatingPercent.TabIndex = 118;
            this.tb_VoltDropBreakerRatingPercent.Text = "80";
            this.tb_VoltDropBreakerRatingPercent.Validating += new System.ComponentModel.CancelEventHandler(this.tb_VoltDropBreakerRatingPercent_Validating);
            // 
            // rqGPONotice
            // 
            this.rqGPONotice.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rqGPONotice.AutoSize = true;
            this.rqGPONotice.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqGPONotice.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.rqGPONotice.Location = new System.Drawing.Point(40, 60);
            this.rqGPONotice.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rqGPONotice.MaximumSize = new System.Drawing.Size(350, 41);
            this.rqGPONotice.Name = "rqGPONotice";
            this.rqGPONotice.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.rqGPONotice.Size = new System.Drawing.Size(335, 28);
            this.rqGPONotice.TabIndex = 117;
            this.rqGPONotice.Text = "A GPO is defined as having \"GPO\" in the type name, or by having an apparent power" +
    " of 0 VA.";
            this.rqGPONotice.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // radioButtonGPOCalcAssignedLoad
            // 
            this.radioButtonGPOCalcAssignedLoad.AutoSize = true;
            this.radioButtonGPOCalcAssignedLoad.Checked = true;
            this.radioButtonGPOCalcAssignedLoad.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonGPOCalcAssignedLoad.Location = new System.Drawing.Point(24, 40);
            this.radioButtonGPOCalcAssignedLoad.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonGPOCalcAssignedLoad.Name = "radioButtonGPOCalcAssignedLoad";
            this.radioButtonGPOCalcAssignedLoad.Size = new System.Drawing.Size(122, 18);
            this.radioButtonGPOCalcAssignedLoad.TabIndex = 116;
            this.radioButtonGPOCalcAssignedLoad.TabStop = true;
            this.radioButtonGPOCalcAssignedLoad.Text = "Using assigned load";
            this.toolTip1.SetToolTip(this.radioButtonGPOCalcAssignedLoad, "Revit circuit current is used in PowerBIM (Default). \r\n\r\nNote: A GPO must either " +
        "have an apparent power of 0VA, or have \"GPO\" in the type name.\r\n");
            this.radioButtonGPOCalcAssignedLoad.UseVisualStyleBackColor = true;
            this.radioButtonGPOCalcAssignedLoad.CheckedChanged += new System.EventHandler(this.radioButtonGPOCalcAssignedLoad_CheckedChanged);
            // 
            // radioButtonGPOCalcBreakerRating
            // 
            this.radioButtonGPOCalcBreakerRating.AutoSize = true;
            this.radioButtonGPOCalcBreakerRating.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonGPOCalcBreakerRating.Location = new System.Drawing.Point(24, 20);
            this.radioButtonGPOCalcBreakerRating.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonGPOCalcBreakerRating.Name = "radioButtonGPOCalcBreakerRating";
            this.radioButtonGPOCalcBreakerRating.Size = new System.Drawing.Size(14, 13);
            this.radioButtonGPOCalcBreakerRating.TabIndex = 116;
            this.toolTip1.SetToolTip(this.radioButtonGPOCalcBreakerRating, resources.GetString("radioButtonGPOCalcBreakerRating.ToolTip"));
            this.radioButtonGPOCalcBreakerRating.UseVisualStyleBackColor = true;
            this.radioButtonGPOCalcBreakerRating.CheckedChanged += new System.EventHandler(this.radioButtonGPOCalcBreakerRating_CheckedChanged);
            // 
            // radioButtonGPOCalc1000plus100
            // 
            this.radioButtonGPOCalc1000plus100.AutoSize = true;
            this.radioButtonGPOCalc1000plus100.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radioButtonGPOCalc1000plus100.Location = new System.Drawing.Point(176, 20);
            this.radioButtonGPOCalc1000plus100.Margin = new System.Windows.Forms.Padding(2);
            this.radioButtonGPOCalc1000plus100.Name = "radioButtonGPOCalc1000plus100";
            this.radioButtonGPOCalc1000plus100.Size = new System.Drawing.Size(147, 18);
            this.radioButtonGPOCalc1000plus100.TabIndex = 115;
            this.radioButtonGPOCalc1000plus100.Text = "1000W + 100W per outlet";
            this.toolTip1.SetToolTip(this.radioButtonGPOCalc1000plus100, resources.GetString("radioButtonGPOCalc1000plus100.ToolTip"));
            this.radioButtonGPOCalc1000plus100.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.groupBox10);
            this.groupBox1.Controls.Add(this.groupBox9);
            this.groupBox1.Controls.Add(this.groupBox8);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox1.Location = new System.Drawing.Point(6, 10);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox1.Size = new System.Drawing.Size(393, 137);
            this.groupBox1.TabIndex = 189;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Volt Drop Calculation";
            // 
            // groupBox10
            // 
            this.groupBox10.Controls.Add(this.rb_OtherLinearDeprecating);
            this.groupBox10.Controls.Add(this.rb_OtherLumpLoad);
            this.groupBox10.Location = new System.Drawing.Point(72, 97);
            this.groupBox10.Margin = new System.Windows.Forms.Padding(3, 0, 3, 3);
            this.groupBox10.Name = "groupBox10";
            this.groupBox10.Size = new System.Drawing.Size(305, 34);
            this.groupBox10.TabIndex = 136;
            this.groupBox10.TabStop = false;
            // 
            // rb_OtherLinearDeprecating
            // 
            this.rb_OtherLinearDeprecating.AutoSize = true;
            this.rb_OtherLinearDeprecating.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_OtherLinearDeprecating.Location = new System.Drawing.Point(16, 11);
            this.rb_OtherLinearDeprecating.Margin = new System.Windows.Forms.Padding(2);
            this.rb_OtherLinearDeprecating.Name = "rb_OtherLinearDeprecating";
            this.rb_OtherLinearDeprecating.Size = new System.Drawing.Size(136, 18);
            this.rb_OtherLinearDeprecating.TabIndex = 120;
            this.rb_OtherLinearDeprecating.Text = "Linear Depreciating VD";
            this.toolTip1.SetToolTip(this.rb_OtherLinearDeprecating, "Linear Depreciating: \r\nThis method with depreciate the load current from the firs" +
        "t circuit element to the final circuit element. (Reccommened)\r\n");
            this.rb_OtherLinearDeprecating.UseVisualStyleBackColor = true;
            // 
            // rb_OtherLumpLoad
            // 
            this.rb_OtherLumpLoad.AutoSize = true;
            this.rb_OtherLumpLoad.Checked = true;
            this.rb_OtherLumpLoad.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_OtherLumpLoad.Location = new System.Drawing.Point(168, 11);
            this.rb_OtherLumpLoad.Margin = new System.Windows.Forms.Padding(2);
            this.rb_OtherLumpLoad.Name = "rb_OtherLumpLoad";
            this.rb_OtherLumpLoad.Size = new System.Drawing.Size(96, 18);
            this.rb_OtherLumpLoad.TabIndex = 119;
            this.rb_OtherLumpLoad.TabStop = true;
            this.rb_OtherLumpLoad.Text = "Lump Load VD";
            this.toolTip1.SetToolTip(this.rb_OtherLumpLoad, "Lump Load: \r\nThis method with model the entire load current at the end of the cab" +
        "le. (Worst case)");
            this.rb_OtherLumpLoad.UseVisualStyleBackColor = true;
            // 
            // groupBox9
            // 
            this.groupBox9.Controls.Add(this.rb_PowerLinearDeprecating);
            this.groupBox9.Controls.Add(this.rb_PowerLumpLoad);
            this.groupBox9.Location = new System.Drawing.Point(72, 59);
            this.groupBox9.Margin = new System.Windows.Forms.Padding(3, 0, 3, 3);
            this.groupBox9.Name = "groupBox9";
            this.groupBox9.Size = new System.Drawing.Size(305, 34);
            this.groupBox9.TabIndex = 136;
            this.groupBox9.TabStop = false;
            // 
            // rb_PowerLinearDeprecating
            // 
            this.rb_PowerLinearDeprecating.AutoSize = true;
            this.rb_PowerLinearDeprecating.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_PowerLinearDeprecating.Location = new System.Drawing.Point(16, 11);
            this.rb_PowerLinearDeprecating.Margin = new System.Windows.Forms.Padding(2);
            this.rb_PowerLinearDeprecating.Name = "rb_PowerLinearDeprecating";
            this.rb_PowerLinearDeprecating.Size = new System.Drawing.Size(136, 18);
            this.rb_PowerLinearDeprecating.TabIndex = 118;
            this.rb_PowerLinearDeprecating.Text = "Linear Depreciating VD";
            this.toolTip1.SetToolTip(this.rb_PowerLinearDeprecating, "Linear Depreciating: \r\nThis method with depreciate the load current from the firs" +
        "t circuit element to the final circuit element. (Reccommened)\r\n");
            this.rb_PowerLinearDeprecating.UseVisualStyleBackColor = true;
            // 
            // rb_PowerLumpLoad
            // 
            this.rb_PowerLumpLoad.AutoSize = true;
            this.rb_PowerLumpLoad.Checked = true;
            this.rb_PowerLumpLoad.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_PowerLumpLoad.Location = new System.Drawing.Point(168, 11);
            this.rb_PowerLumpLoad.Margin = new System.Windows.Forms.Padding(2);
            this.rb_PowerLumpLoad.Name = "rb_PowerLumpLoad";
            this.rb_PowerLumpLoad.Size = new System.Drawing.Size(96, 18);
            this.rb_PowerLumpLoad.TabIndex = 117;
            this.rb_PowerLumpLoad.TabStop = true;
            this.rb_PowerLumpLoad.Text = "Lump Load VD";
            this.toolTip1.SetToolTip(this.rb_PowerLumpLoad, "Lump Load: \r\nThis method with model the entire load current at the end of the cab" +
        "le. (Worst case)");
            this.rb_PowerLumpLoad.UseVisualStyleBackColor = true;
            // 
            // groupBox8
            // 
            this.groupBox8.Controls.Add(this.rb_LightingLinearDeprecating);
            this.groupBox8.Controls.Add(this.rb_LightingLumpLoad);
            this.groupBox8.Location = new System.Drawing.Point(72, 21);
            this.groupBox8.Margin = new System.Windows.Forms.Padding(3, 0, 3, 3);
            this.groupBox8.Name = "groupBox8";
            this.groupBox8.Size = new System.Drawing.Size(305, 34);
            this.groupBox8.TabIndex = 135;
            this.groupBox8.TabStop = false;
            // 
            // rb_LightingLinearDeprecating
            // 
            this.rb_LightingLinearDeprecating.AutoSize = true;
            this.rb_LightingLinearDeprecating.Checked = true;
            this.rb_LightingLinearDeprecating.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_LightingLinearDeprecating.Location = new System.Drawing.Point(16, 12);
            this.rb_LightingLinearDeprecating.Margin = new System.Windows.Forms.Padding(2);
            this.rb_LightingLinearDeprecating.Name = "rb_LightingLinearDeprecating";
            this.rb_LightingLinearDeprecating.Size = new System.Drawing.Size(136, 18);
            this.rb_LightingLinearDeprecating.TabIndex = 116;
            this.rb_LightingLinearDeprecating.TabStop = true;
            this.rb_LightingLinearDeprecating.Text = "Linear Depreciating VD";
            this.toolTip1.SetToolTip(this.rb_LightingLinearDeprecating, "Linear Depreciating: \r\nThis method with depreciate the load current from the firs" +
        "t circuit element to the final circuit element. (Reccommened)\r\n");
            this.rb_LightingLinearDeprecating.UseVisualStyleBackColor = true;
            // 
            // rb_LightingLumpLoad
            // 
            this.rb_LightingLumpLoad.AutoSize = true;
            this.rb_LightingLumpLoad.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rb_LightingLumpLoad.Location = new System.Drawing.Point(167, 12);
            this.rb_LightingLumpLoad.Margin = new System.Windows.Forms.Padding(2);
            this.rb_LightingLumpLoad.Name = "rb_LightingLumpLoad";
            this.rb_LightingLumpLoad.Size = new System.Drawing.Size(96, 18);
            this.rb_LightingLumpLoad.TabIndex = 115;
            this.rb_LightingLumpLoad.Text = "Lump Load VD";
            this.toolTip1.SetToolTip(this.rb_LightingLumpLoad, "Lump Load: \r\nThis method with model the entire load current at the end of the cab" +
        "le. (Worst case)");
            this.rb_LightingLumpLoad.UseVisualStyleBackColor = true;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.Location = new System.Drawing.Point(8, 108);
            this.label8.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(42, 16);
            this.label8.TabIndex = 134;
            this.label8.Text = "Other";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.Location = new System.Drawing.Point(8, 70);
            this.label7.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(48, 16);
            this.label7.TabIndex = 133;
            this.label7.Text = "Power";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.Location = new System.Drawing.Point(8, 35);
            this.label6.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 16);
            this.label6.TabIndex = 132;
            this.label6.Text = "Lighting";
            // 
            // label9
            // 
            this.label9.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label9.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.label9.Location = new System.Drawing.Point(4, 70);
            this.label9.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(395, 43);
            this.label9.TabIndex = 195;
            this.label9.Text = "Please \'Generate Updated Cable Data\' everytime after the Cable Database is update" +
    "d or path changed. This will generate the new CSV inputs that PowerBIM uses! (It" +
    " may take a couple of minutes)";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rqCableDataFile
            // 
            this.rqCableDataFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqCableDataFile.Location = new System.Drawing.Point(4, 20);
            this.rqCableDataFile.Margin = new System.Windows.Forms.Padding(2);
            this.rqCableDataFile.Name = "rqCableDataFile";
            this.rqCableDataFile.Size = new System.Drawing.Size(396, 19);
            this.rqCableDataFile.TabIndex = 194;
            // 
            // reEnableNewData
            // 
            this.reEnableNewData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.reEnableNewData.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.reEnableNewData.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.reEnableNewData.ForeColor = System.Drawing.Color.White;
            this.reEnableNewData.Location = new System.Drawing.Point(200, 42);
            this.reEnableNewData.Margin = new System.Windows.Forms.Padding(2);
            this.reEnableNewData.Name = "reEnableNewData";
            this.reEnableNewData.Size = new System.Drawing.Size(200, 22);
            this.reEnableNewData.TabIndex = 193;
            this.reEnableNewData.Text = "Generate Updated Cable Data";
            this.reEnableNewData.UseVisualStyleBackColor = false;
            this.reEnableNewData.Click += new System.EventHandler(this.btnEnableNewData_Click);
            // 
            // rqEditDatabase
            // 
            this.rqEditDatabase.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.rqEditDatabase.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqEditDatabase.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqEditDatabase.ForeColor = System.Drawing.Color.White;
            this.rqEditDatabase.Location = new System.Drawing.Point(4, 42);
            this.rqEditDatabase.Margin = new System.Windows.Forms.Padding(2);
            this.rqEditDatabase.Name = "rqEditDatabase";
            this.rqEditDatabase.Size = new System.Drawing.Size(190, 22);
            this.rqEditDatabase.TabIndex = 192;
            this.rqEditDatabase.Text = "Set New Cable Database Location";
            this.rqEditDatabase.UseVisualStyleBackColor = false;
            this.rqEditDatabase.Click += new System.EventHandler(this.btnEditDatabase_Click);
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.label9);
            this.groupBox5.Controls.Add(this.reEnableNewData);
            this.groupBox5.Controls.Add(this.rqCableDataFile);
            this.groupBox5.Controls.Add(this.rqEditDatabase);
            this.groupBox5.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold);
            this.groupBox5.Location = new System.Drawing.Point(8, 583);
            this.groupBox5.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox5.Size = new System.Drawing.Size(404, 119);
            this.groupBox5.TabIndex = 196;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "Cable Database";
            // 
            // cbExtraLengthElem
            // 
            this.cbExtraLengthElem.AutoSize = true;
            this.cbExtraLengthElem.Checked = true;
            this.cbExtraLengthElem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbExtraLengthElem.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbExtraLengthElem.Location = new System.Drawing.Point(7, 41);
            this.cbExtraLengthElem.Margin = new System.Windows.Forms.Padding(2);
            this.cbExtraLengthElem.Name = "cbExtraLengthElem";
            this.cbExtraLengthElem.Size = new System.Drawing.Size(149, 18);
            this.cbExtraLengthElem.TabIndex = 127;
            this.cbExtraLengthElem.Text = "Extra Length Per Element:";
            this.cbExtraLengthElem.UseVisualStyleBackColor = true;
            // 
            // cbExtraLenPerCCT
            // 
            this.cbExtraLenPerCCT.AutoSize = true;
            this.cbExtraLenPerCCT.Checked = true;
            this.cbExtraLenPerCCT.CheckState = System.Windows.Forms.CheckState.Checked;
            this.cbExtraLenPerCCT.Font = new System.Drawing.Font("Arial", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbExtraLenPerCCT.Location = new System.Drawing.Point(7, 63);
            this.cbExtraLenPerCCT.Margin = new System.Windows.Forms.Padding(2);
            this.cbExtraLenPerCCT.Name = "cbExtraLenPerCCT";
            this.cbExtraLenPerCCT.Size = new System.Drawing.Size(142, 18);
            this.cbExtraLenPerCCT.TabIndex = 128;
            this.cbExtraLenPerCCT.Text = "Extra Length Per Circuit:";
            this.cbExtraLenPerCCT.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.tbTxtLenPerCCTOther);
            this.groupBox6.Controls.Add(this.label14);
            this.groupBox6.Controls.Add(this.tbTxtLenPerCCTPower);
            this.groupBox6.Controls.Add(this.label13);
            this.groupBox6.Controls.Add(this.tbTxtLenPerCCTLighting);
            this.groupBox6.Controls.Add(this.tbTxtLenPerElemOther);
            this.groupBox6.Controls.Add(this.label12);
            this.groupBox6.Controls.Add(this.tbTxtLenPerElemPower);
            this.groupBox6.Controls.Add(this.label11);
            this.groupBox6.Controls.Add(this.tbTxtLenPerElemLighting);
            this.groupBox6.Controls.Add(this.label15);
            this.groupBox6.Controls.Add(this.label10);
            this.groupBox6.Controls.Add(this.label4);
            this.groupBox6.Controls.Add(this.label3);
            this.groupBox6.Controls.Add(this.label2);
            this.groupBox6.Controls.Add(this.cbExtraLenPerCCT);
            this.groupBox6.Controls.Add(this.cbExtraLengthElem);
            this.groupBox6.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox6.Location = new System.Drawing.Point(7, 375);
            this.groupBox6.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox6.Size = new System.Drawing.Size(392, 91);
            this.groupBox6.TabIndex = 187;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "Circuit Length Adjustment Factors";
            // 
            // tbTxtLenPerCCTOther
            // 
            this.tbTxtLenPerCCTOther.Location = new System.Drawing.Point(308, 61);
            this.tbTxtLenPerCCTOther.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerCCTOther.Name = "tbTxtLenPerCCTOther";
            this.tbTxtLenPerCCTOther.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerCCTOther.TabIndex = 153;
            this.tbTxtLenPerCCTOther.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerCCTOther_KeyDown);
            this.tbTxtLenPerCCTOther.Leave += new System.EventHandler(this.tbTxtLenPerCCTOther_Leave);
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("Arial", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.Location = new System.Drawing.Point(351, 63);
            this.label14.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 16);
            this.label14.TabIndex = 152;
            this.label14.Text = "%";
            // 
            // tbTxtLenPerCCTPower
            // 
            this.tbTxtLenPerCCTPower.Location = new System.Drawing.Point(233, 61);
            this.tbTxtLenPerCCTPower.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerCCTPower.Name = "tbTxtLenPerCCTPower";
            this.tbTxtLenPerCCTPower.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerCCTPower.TabIndex = 151;
            this.tbTxtLenPerCCTPower.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerCCTPower_KeyDown);
            this.tbTxtLenPerCCTPower.Leave += new System.EventHandler(this.tbTxtLenPerCCTPower_Leave);
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("Arial", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.Location = new System.Drawing.Point(277, 63);
            this.label13.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 16);
            this.label13.TabIndex = 150;
            this.label13.Text = "%";
            // 
            // tbTxtLenPerCCTLighting
            // 
            this.tbTxtLenPerCCTLighting.Location = new System.Drawing.Point(159, 61);
            this.tbTxtLenPerCCTLighting.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerCCTLighting.Name = "tbTxtLenPerCCTLighting";
            this.tbTxtLenPerCCTLighting.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerCCTLighting.TabIndex = 149;
            this.tbTxtLenPerCCTLighting.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerCCTLighting_KeyDown);
            this.tbTxtLenPerCCTLighting.Leave += new System.EventHandler(this.tbTxtLenPerCCTLighting_Leave);
            // 
            // tbTxtLenPerElemOther
            // 
            this.tbTxtLenPerElemOther.Location = new System.Drawing.Point(308, 35);
            this.tbTxtLenPerElemOther.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerElemOther.Name = "tbTxtLenPerElemOther";
            this.tbTxtLenPerElemOther.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerElemOther.TabIndex = 148;
            this.tbTxtLenPerElemOther.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerElemOther_KeyDown);
            this.tbTxtLenPerElemOther.Leave += new System.EventHandler(this.tbTxtLenPerElemOther_Leave);
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("Arial", 10.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.Location = new System.Drawing.Point(351, 37);
            this.label12.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(20, 18);
            this.label12.TabIndex = 147;
            this.label12.Text = "m";
            // 
            // tbTxtLenPerElemPower
            // 
            this.tbTxtLenPerElemPower.Location = new System.Drawing.Point(233, 35);
            this.tbTxtLenPerElemPower.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerElemPower.Name = "tbTxtLenPerElemPower";
            this.tbTxtLenPerElemPower.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerElemPower.TabIndex = 146;
            this.tbTxtLenPerElemPower.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerElemPower_KeyDown);
            this.tbTxtLenPerElemPower.Leave += new System.EventHandler(this.tbTxtLenPerElemPower_Leave);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("Arial", 10.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label11.Location = new System.Drawing.Point(277, 37);
            this.label11.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(20, 18);
            this.label11.TabIndex = 145;
            this.label11.Text = "m";
            // 
            // tbTxtLenPerElemLighting
            // 
            this.tbTxtLenPerElemLighting.Location = new System.Drawing.Point(159, 35);
            this.tbTxtLenPerElemLighting.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtLenPerElemLighting.Name = "tbTxtLenPerElemLighting";
            this.tbTxtLenPerElemLighting.Size = new System.Drawing.Size(43, 22);
            this.tbTxtLenPerElemLighting.TabIndex = 144;
            this.tbTxtLenPerElemLighting.KeyDown += new System.Windows.Forms.KeyEventHandler(this.tbTxtLenPerElemLighting_KeyDown);
            this.tbTxtLenPerElemLighting.Leave += new System.EventHandler(this.tbTxtLenPerElemLighting_Leave);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("Arial", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label15.Location = new System.Drawing.Point(202, 63);
            this.label15.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(17, 16);
            this.label15.TabIndex = 141;
            this.label15.Text = "%";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("Arial", 10.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label10.Location = new System.Drawing.Point(202, 37);
            this.label10.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(20, 18);
            this.label10.TabIndex = 138;
            this.label10.Text = "m";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Arial", 8.5F, System.Drawing.FontStyle.Bold);
            this.label4.Location = new System.Drawing.Point(313, 19);
            this.label4.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(39, 15);
            this.label4.TabIndex = 137;
            this.label4.Text = "Other";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Font = new System.Drawing.Font("Arial", 8.5F, System.Drawing.FontStyle.Bold);
            this.label3.Location = new System.Drawing.Point(238, 19);
            this.label3.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(44, 15);
            this.label3.TabIndex = 134;
            this.label3.Text = "Power";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Arial", 8.5F, System.Drawing.FontStyle.Bold);
            this.label2.Location = new System.Drawing.Point(159, 19);
            this.label2.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(52, 15);
            this.label2.TabIndex = 131;
            this.label2.Text = "Lighting";
            // 
            // groupBox7
            // 
            this.groupBox7.Controls.Add(this.label1);
            this.groupBox7.Controls.Add(this.tbTxtRevitPathNodeTollerance);
            this.groupBox7.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.groupBox7.Location = new System.Drawing.Point(7, 535);
            this.groupBox7.Margin = new System.Windows.Forms.Padding(2);
            this.groupBox7.Name = "groupBox7";
            this.groupBox7.Padding = new System.Windows.Forms.Padding(2);
            this.groupBox7.Size = new System.Drawing.Size(405, 46);
            this.groupBox7.TabIndex = 188;
            this.groupBox7.TabStop = false;
            this.groupBox7.Text = "Revit Path Settings";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Arial", 8.25F);
            this.label1.Location = new System.Drawing.Point(22, 24);
            this.label1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(246, 14);
            this.label1.TabIndex = 130;
            this.label1.Text = "Path node vs. element intersection (XY tolerance)";
            // 
            // tbTxtRevitPathNodeTollerance
            // 
            this.tbTxtRevitPathNodeTollerance.Location = new System.Drawing.Point(304, 20);
            this.tbTxtRevitPathNodeTollerance.Margin = new System.Windows.Forms.Padding(2);
            this.tbTxtRevitPathNodeTollerance.Mask = "0.30m";
            this.tbTxtRevitPathNodeTollerance.Name = "tbTxtRevitPathNodeTollerance";
            this.tbTxtRevitPathNodeTollerance.PromptChar = ' ';
            this.tbTxtRevitPathNodeTollerance.Size = new System.Drawing.Size(66, 22);
            this.tbTxtRevitPathNodeTollerance.TabIndex = 129;
            this.tbTxtRevitPathNodeTollerance.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // gb_MainSettings
            // 
            this.gb_MainSettings.Controls.Add(this.groupBox1);
            this.gb_MainSettings.Controls.Add(this.groupBox2);
            this.gb_MainSettings.Controls.Add(this.groupBox6);
            this.gb_MainSettings.Controls.Add(this.groupBox3);
            this.gb_MainSettings.Controls.Add(this.groupBox4);
            this.gb_MainSettings.Location = new System.Drawing.Point(7, 63);
            this.gb_MainSettings.Name = "gb_MainSettings";
            this.gb_MainSettings.Size = new System.Drawing.Size(405, 473);
            this.gb_MainSettings.TabIndex = 197;
            this.gb_MainSettings.TabStop = false;
            // 
            // chb_ControlGlobally
            // 
            this.chb_ControlGlobally.AutoSize = true;
            this.chb_ControlGlobally.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chb_ControlGlobally.Location = new System.Drawing.Point(8, 46);
            this.chb_ControlGlobally.Name = "chb_ControlGlobally";
            this.chb_ControlGlobally.Size = new System.Drawing.Size(114, 19);
            this.chb_ControlGlobally.TabIndex = 137;
            this.chb_ControlGlobally.Text = "Control Globally";
            this.chb_ControlGlobally.UseVisualStyleBackColor = true;
            this.chb_ControlGlobally.CheckedChanged += new System.EventHandler(this.chb_ControlGlobally_CheckedChanged);
            // 
            // enhancedCCTTableBindingSource
            // 
            this.enhancedCCTTableBindingSource.DataMember = "EnhancedCCTTable";
            this.enhancedCCTTableBindingSource.DataSource = this.enhancedCCTDataSet;
            // 
            // enhancedCCTDataSet
            // 
            this.enhancedCCTDataSet.DataSetName = "EnhancedCCTDataSet";
            this.enhancedCCTDataSet.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // frmPowerBIM_AdvancedSettings
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.btnHelp_Visiblity = true;
            this.ClientSize = new System.Drawing.Size(418, 803);
            this.Controls.Add(this.chb_ControlGlobally);
            this.Controls.Add(this.gb_MainSettings);
            this.Controls.Add(this.groupBox7);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.rqSettingsCancel);
            this.Controls.Add(this.rqSettingsSubmit);
            this.Margin = new System.Windows.Forms.Padding(4);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(434, 842);
            this.MinimumSize = new System.Drawing.Size(434, 842);
            this.Name = "frmPowerBIM_AdvancedSettings";
            this.Text = "PowerBIM | Advanced Settings";
            this.TitleText = "PowerBIM -  SETTINGS";
            this.VerisionText = "© 2021   01.05.01  ";
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.frmPowerBIM_AdvancedSettings_FormClosed);
            this.Load += new System.EventHandler(this.frmPowerBIM_AdvancedSettings_Load);
            this.Controls.SetChildIndex(this.rqSettingsSubmit, 0);
            this.Controls.SetChildIndex(this.rqSettingsCancel, 0);
            this.Controls.SetChildIndex(this.groupBox5, 0);
            this.Controls.SetChildIndex(this.groupBox7, 0);
            this.Controls.SetChildIndex(this.gb_MainSettings, 0);
            this.Controls.SetChildIndex(this.chb_ControlGlobally, 0);
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox10.ResumeLayout(false);
            this.groupBox10.PerformLayout();
            this.groupBox9.ResumeLayout(false);
            this.groupBox9.PerformLayout();
            this.groupBox8.ResumeLayout(false);
            this.groupBox8.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.groupBox6.PerformLayout();
            this.groupBox7.ResumeLayout(false);
            this.groupBox7.PerformLayout();
            this.gb_MainSettings.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.enhancedCCTTableBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.enhancedCCTDataSet)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button rqSettingsCancel;
        private System.Windows.Forms.Button rqSettingsSubmit;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.RadioButton radioButtonNonGPO04;
        private System.Windows.Forms.RadioButton radioButtonNonGPO5;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.RadioButton radioButtonLightingClearning04;
        private System.Windows.Forms.RadioButton radioButtonLightingClearning5;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Label rqGPONotice;
        private System.Windows.Forms.RadioButton radioButtonGPOCalcAssignedLoad;
        private System.Windows.Forms.RadioButton radioButtonGPOCalcBreakerRating;
        private System.Windows.Forms.RadioButton radioButtonGPOCalc1000plus100;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rb_LightingLinearDeprecating;
        private System.Windows.Forms.RadioButton rb_LightingLumpLoad;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox rqCableDataFile;
        private System.Windows.Forms.Button rqEditDatabase;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button reEnableNewData;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.BindingSource enhancedCCTTableBindingSource;
        private PowerBIM_5.DataSets.EnhancedCCTDataSet enhancedCCTDataSet;
        private System.Windows.Forms.CheckBox cbExtraLengthElem;
        private System.Windows.Forms.CheckBox cbExtraLenPerCCT;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox7;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.MaskedTextBox tbTxtRevitPathNodeTollerance;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox tb_VoltDropBreakerRatingPercent;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.RadioButton rb_OtherLinearDeprecating;
        private System.Windows.Forms.RadioButton rb_OtherLumpLoad;
        private System.Windows.Forms.RadioButton rb_PowerLinearDeprecating;
        private System.Windows.Forms.RadioButton rb_PowerLumpLoad;
        private System.Windows.Forms.GroupBox groupBox10;
        private System.Windows.Forms.GroupBox groupBox9;
        private System.Windows.Forms.GroupBox groupBox8;
        private System.Windows.Forms.GroupBox gb_MainSettings;
        private System.Windows.Forms.CheckBox chb_ControlGlobally;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TextBox tbTxtLenPerCCTOther;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox tbTxtLenPerCCTPower;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox tbTxtLenPerCCTLighting;
        private System.Windows.Forms.TextBox tbTxtLenPerElemOther;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox tbTxtLenPerElemPower;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox tbTxtLenPerElemLighting;
    }
}