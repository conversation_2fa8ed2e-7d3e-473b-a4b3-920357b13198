﻿
using Color = System.Drawing.Color;
using Point = System.Drawing.Point;

namespace MEP.PowerBIM_5.UI.Forms
{
    partial class FrmPowerBIM_CircuitEditEnhanced
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle35 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle8 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle9 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle10 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle11 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle12 = new DataGridViewCellStyle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmPowerBIM_CircuitEditEnhanced));
            DataGridViewCellStyle dataGridViewCellStyle13 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle14 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle15 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle16 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle17 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle18 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle19 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle20 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle21 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle22 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle23 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle24 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle25 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle26 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle27 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle28 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle29 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle30 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle31 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle32 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle33 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle34 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle36 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle37 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle38 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle39 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle40 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle41 = new DataGridViewCellStyle();
            tlp_MainContent = new TableLayoutPanel();
            btnRun = new Button();
            btnCancel = new Button();
            btnSave = new Button();
            gb_CircuitData = new GroupBox();
            tlp_CircuitsData = new TableLayoutPanel();
            btn_ActivateEditPathView = new Button();
            AdgvCircuitData = new Common.UI.Controls.ExtendedAdvancedDataGridView();
            circuitNumberDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            deviceRatingDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            deviceCurveTypeDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            protectionDeviceDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            rCDProtectionDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            otherControlsDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            cableToFirstCircuitComponentDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            cableInstallationMethodDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            deratingFactorDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            diversityDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            Manual = new DataGridViewCheckBoxColumn();
            cablePathSelectionDataGridViewTextBoxColumn = new DataGridViewComboBoxColumn();
            cableSetPathDataGridViewButtonColumn = new DataGridViewButtonColumn();
            lengthToFirstDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            lengthToFinalDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            ManualCurrent = new DataGridViewCheckBoxColumn();
            unDiversifiedCurrentDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            numberOfElementsDataGridViewButtonColumn = new DataGridViewButtonColumn();
            circuitDescriptionDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkTripRatingDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            cable1ValidDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            cable2ValidDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkCPDDescriminatesDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkLoadCurrentDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkCable1CurrentDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkCable2CurrentDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkEFLIDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkFinalCCTVDDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkSystemMaxVDDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkCable1SCWithstandDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            checkCable2SCWithstandDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            circuitCheckSummaryDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            circuitCheckResultDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            circuitRevisionDataGridViewTextBoxColumn = new DataGridViewTextBoxColumn();
            isSpareOrSpaceDataGridViewCheckBoxColumn = new DataGridViewCheckBoxColumn();
            enhancedCCTTableBindingSource = new BindingSource(components);
            enhancedCCTDataSet = new DataSets.EnhancedCCTDataSet();
            adgv_SearchBar = new Zuby.ADGV.AdvancedDataGridViewSearchToolBar();
            label1 = new Label();
            tableLayoutPanel1 = new TableLayoutPanel();
            dgv_DiversifiedPhase = new DataGridView();
            dgv_UnDiversifiedPhase = new DataGridView();
            label2 = new Label();
            label3 = new Label();
            PhaseR = new DataGridViewTextBoxColumn();
            PhaseW = new DataGridViewTextBoxColumn();
            PhaseB = new DataGridViewTextBoxColumn();
            RevitPhaseR = new DataGridViewTextBoxColumn();
            RevitPhaseW = new DataGridViewTextBoxColumn();
            RevitPhaseB = new DataGridViewTextBoxColumn();
            tlp_MainContent.SuspendLayout();
            gb_CircuitData.SuspendLayout();
            tlp_CircuitsData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)AdgvCircuitData).BeginInit();
            ((System.ComponentModel.ISupportInitialize)enhancedCCTTableBindingSource).BeginInit();
            ((System.ComponentModel.ISupportInitialize)enhancedCCTDataSet).BeginInit();
            tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_DiversifiedPhase).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dgv_UnDiversifiedPhase).BeginInit();
            SuspendLayout();
            // 
            // tlp_MainContent
            // 
            tlp_MainContent.BackColor = Color.White;
            tlp_MainContent.ColumnCount = 5;
            tlp_MainContent.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlp_MainContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 145F));
            tlp_MainContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 145F));
            tlp_MainContent.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 145F));
            tlp_MainContent.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tlp_MainContent.Controls.Add(btnRun, 2, 1);
            tlp_MainContent.Controls.Add(btnCancel, 3, 1);
            tlp_MainContent.Controls.Add(btnSave, 1, 1);
            tlp_MainContent.Controls.Add(gb_CircuitData, 0, 0);
            tlp_MainContent.Controls.Add(label1, 0, 1);
            tlp_MainContent.Controls.Add(tableLayoutPanel1, 4, 1);
            tlp_MainContent.Dock = DockStyle.Fill;
            tlp_MainContent.Location = new Point(0, 40);
            tlp_MainContent.Name = "tlp_MainContent";
            tlp_MainContent.RowCount = 2;
            tlp_MainContent.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlp_MainContent.RowStyles.Add(new RowStyle(SizeType.Absolute, 97F));
            tlp_MainContent.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tlp_MainContent.Size = new Size(1443, 766);
            tlp_MainContent.TabIndex = 3;
            // 
            // btnRun
            // 
            btnRun.Anchor = AnchorStyles.None;
            btnRun.BackColor = Color.FromArgb(141, 14, 132);
            btnRun.FlatAppearance.BorderColor = Color.FromArgb(141, 14, 132);
            btnRun.FlatStyle = FlatStyle.Popup;
            btnRun.Font = new Font("Arial", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnRun.ForeColor = Color.White;
            btnRun.Location = new Point(652, 698);
            btnRun.Margin = new Padding(2);
            btnRun.Name = "btnRun";
            btnRun.Size = new Size(138, 38);
            btnRun.TabIndex = 201;
            btnRun.Text = "Auto Calc [ON]";
            btnRun.UseVisualStyleBackColor = false;
            btnRun.Click += btnRun_Click;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Left;
            btnCancel.BackColor = Color.FromArgb(18, 168, 178);
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.FlatAppearance.BorderColor = Color.FromArgb(18, 168, 178);
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Arial", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnCancel.ForeColor = Color.White;
            btnCancel.Location = new Point(796, 698);
            btnCancel.Margin = new Padding(2);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(138, 38);
            btnCancel.TabIndex = 199;
            btnCancel.Text = "Close";
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnSave
            // 
            btnSave.Anchor = AnchorStyles.Right;
            btnSave.BackColor = Color.FromArgb(255, 206, 0);
            btnSave.FlatAppearance.BorderColor = Color.FromArgb(255, 206, 0);
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Font = new Font("Arial", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSave.ForeColor = Color.Black;
            btnSave.Location = new Point(509, 698);
            btnSave.Margin = new Padding(2);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(138, 38);
            btnSave.TabIndex = 200;
            btnSave.Text = "Save";
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // gb_CircuitData
            // 
            gb_CircuitData.BackColor = Color.White;
            tlp_MainContent.SetColumnSpan(gb_CircuitData, 5);
            gb_CircuitData.Controls.Add(tlp_CircuitsData);
            gb_CircuitData.Dock = DockStyle.Fill;
            gb_CircuitData.Location = new Point(3, 3);
            gb_CircuitData.Name = "gb_CircuitData";
            gb_CircuitData.Size = new Size(1437, 663);
            gb_CircuitData.TabIndex = 203;
            gb_CircuitData.TabStop = false;
            gb_CircuitData.Text = "Circuit Data ";
            // 
            // tlp_CircuitsData
            // 
            tlp_CircuitsData.BackColor = Color.White;
            tlp_CircuitsData.ColumnCount = 2;
            tlp_CircuitsData.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tlp_CircuitsData.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 211F));
            tlp_CircuitsData.Controls.Add(btn_ActivateEditPathView, 1, 0);
            tlp_CircuitsData.Controls.Add(AdgvCircuitData, 0, 1);
            tlp_CircuitsData.Controls.Add(adgv_SearchBar, 0, 0);
            tlp_CircuitsData.Dock = DockStyle.Fill;
            tlp_CircuitsData.Location = new Point(3, 16);
            tlp_CircuitsData.Name = "tlp_CircuitsData";
            tlp_CircuitsData.RowCount = 2;
            tlp_CircuitsData.RowStyles.Add(new RowStyle(SizeType.Absolute, 31F));
            tlp_CircuitsData.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tlp_CircuitsData.RowStyles.Add(new RowStyle(SizeType.Absolute, 16F));
            tlp_CircuitsData.Size = new Size(1431, 644);
            tlp_CircuitsData.TabIndex = 0;
            // 
            // btn_ActivateEditPathView
            // 
            btn_ActivateEditPathView.Anchor = AnchorStyles.None;
            btn_ActivateEditPathView.BackColor = Color.FromArgb(141, 14, 132);
            btn_ActivateEditPathView.FlatAppearance.BorderColor = Color.FromArgb(141, 14, 132);
            btn_ActivateEditPathView.FlatStyle = FlatStyle.Popup;
            btn_ActivateEditPathView.Font = new Font("Arial", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btn_ActivateEditPathView.ForeColor = Color.White;
            btn_ActivateEditPathView.Location = new Point(1239, 2);
            btn_ActivateEditPathView.Margin = new Padding(2);
            btn_ActivateEditPathView.Name = "btn_ActivateEditPathView";
            btn_ActivateEditPathView.Size = new Size(172, 27);
            btn_ActivateEditPathView.TabIndex = 203;
            btn_ActivateEditPathView.Text = "Activate Edit Path View";
            btn_ActivateEditPathView.UseVisualStyleBackColor = false;
            btn_ActivateEditPathView.Click += btn_ActivateEditPathView_Click;
            // 
            // AdgvCircuitData
            // 
            AdgvCircuitData.AllowDrop = true;
            AdgvCircuitData.AllowUserToAddRows = false;
            AdgvCircuitData.AllowUserToDeleteRows = false;
            AdgvCircuitData.AllowUserToOrderColumns = true;
            AdgvCircuitData.AutoGenerateColumns = false;
            AdgvCircuitData.BackgroundColor = SystemColors.Control;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(18, 168, 178);
            dataGridViewCellStyle1.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle1.ForeColor = Color.White;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            AdgvCircuitData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            AdgvCircuitData.ColumnHeadersHeight = 60;
            AdgvCircuitData.Columns.AddRange(new DataGridViewColumn[] { circuitNumberDataGridViewTextBoxColumn, deviceRatingDataGridViewTextBoxColumn, deviceCurveTypeDataGridViewTextBoxColumn, protectionDeviceDataGridViewTextBoxColumn, rCDProtectionDataGridViewTextBoxColumn, otherControlsDataGridViewTextBoxColumn, cableToFirstCircuitComponentDataGridViewTextBoxColumn, cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn, cableInstallationMethodDataGridViewTextBoxColumn, deratingFactorDataGridViewTextBoxColumn, diversityDataGridViewTextBoxColumn, Manual, cablePathSelectionDataGridViewTextBoxColumn, cableSetPathDataGridViewButtonColumn, lengthToFirstDataGridViewTextBoxColumn, lengthToFinalDataGridViewTextBoxColumn, ManualCurrent, unDiversifiedCurrentDataGridViewTextBoxColumn, numberOfElementsDataGridViewButtonColumn, circuitDescriptionDataGridViewTextBoxColumn, checkTripRatingDataGridViewTextBoxColumn, cable1ValidDataGridViewTextBoxColumn, cable2ValidDataGridViewTextBoxColumn, checkCPDDescriminatesDataGridViewTextBoxColumn, checkLoadCurrentDataGridViewTextBoxColumn, checkCable1CurrentDataGridViewTextBoxColumn, checkCable2CurrentDataGridViewTextBoxColumn, checkEFLIDataGridViewTextBoxColumn, checkFinalCCTVDDataGridViewTextBoxColumn, checkSystemMaxVDDataGridViewTextBoxColumn, checkCable1SCWithstandDataGridViewTextBoxColumn, checkCable2SCWithstandDataGridViewTextBoxColumn, circuitCheckSummaryDataGridViewTextBoxColumn, circuitCheckResultDataGridViewTextBoxColumn, circuitRevisionDataGridViewTextBoxColumn, isSpareOrSpaceDataGridViewCheckBoxColumn });
            tlp_CircuitsData.SetColumnSpan(AdgvCircuitData, 2);
            AdgvCircuitData.DataSource = enhancedCCTTableBindingSource;
            dataGridViewCellStyle35.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle35.BackColor = SystemColors.Window;
            dataGridViewCellStyle35.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle35.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle35.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle35.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle35.WrapMode = DataGridViewTriState.True;
            AdgvCircuitData.DefaultCellStyle = dataGridViewCellStyle35;
            AdgvCircuitData.Dock = DockStyle.Fill;
            AdgvCircuitData.EnableHeadersVisualStyles = false;
            AdgvCircuitData.FilterAndSortEnabled = true;
            AdgvCircuitData.FilterStringChangedInvokeBeforeDatasourceUpdate = true;
            AdgvCircuitData.Location = new Point(3, 34);
            AdgvCircuitData.Name = "AdgvCircuitData";
            AdgvCircuitData.RightToLeft = RightToLeft.No;
            AdgvCircuitData.RowHeadersVisible = false;
            AdgvCircuitData.RowHeadersWidth = 51;
            AdgvCircuitData.SelectionMode = DataGridViewSelectionMode.CellSelect;
            AdgvCircuitData.Size = new Size(1425, 607);
            AdgvCircuitData.SortStringChangedInvokeBeforeDatasourceUpdate = true;
            AdgvCircuitData.TabIndex = 198;
            AdgvCircuitData.CellBeginEdit += AdgvCircuitData_CellBeginEdit;
            AdgvCircuitData.CellContentClick += AdgvCircuitData_CellContentClick;
            AdgvCircuitData.CellEndEdit += AdgvCircuitData_CellEndEdit;
            AdgvCircuitData.CellFormatting += AdgvCircuitData_CellFormatting;
            AdgvCircuitData.CellLeave += AdgvCircuitData_CellLeave;
            AdgvCircuitData.CellValueChanged += AdgvCircuitData_CellValueChanged;
            AdgvCircuitData.CurrentCellDirtyStateChanged += AdgvCircuitData_CurrentCellDirtyStateChanged;
            AdgvCircuitData.DataBindingComplete += AdgvCircuitData_DataBindingComplete;
            AdgvCircuitData.DataError += AdgvCircuitData_DataError;
            AdgvCircuitData.RowsAdded += AdgvCircuitData_RowsAdded;
            // 
            // circuitNumberDataGridViewTextBoxColumn
            // 
            circuitNumberDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            circuitNumberDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Number";
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = Color.LightGray;
            circuitNumberDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle2;
            circuitNumberDataGridViewTextBoxColumn.Frozen = true;
            circuitNumberDataGridViewTextBoxColumn.HeaderText = "Circuit Number";
            circuitNumberDataGridViewTextBoxColumn.MinimumWidth = 22;
            circuitNumberDataGridViewTextBoxColumn.Name = "circuitNumberDataGridViewTextBoxColumn";
            circuitNumberDataGridViewTextBoxColumn.ReadOnly = true;
            circuitNumberDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            circuitNumberDataGridViewTextBoxColumn.Width = 60;
            // 
            // deviceRatingDataGridViewTextBoxColumn
            // 
            deviceRatingDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            deviceRatingDataGridViewTextBoxColumn.DataPropertyName = "Device_Rating";
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = Color.White;
            deviceRatingDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle3;
            deviceRatingDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            deviceRatingDataGridViewTextBoxColumn.HeaderText = "Rating";
            deviceRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            deviceRatingDataGridViewTextBoxColumn.Name = "deviceRatingDataGridViewTextBoxColumn";
            deviceRatingDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            deviceRatingDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            deviceRatingDataGridViewTextBoxColumn.Width = 60;
            // 
            // deviceCurveTypeDataGridViewTextBoxColumn
            // 
            deviceCurveTypeDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            deviceCurveTypeDataGridViewTextBoxColumn.DataPropertyName = "Device_Curve_Type";
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = Color.White;
            deviceCurveTypeDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle4;
            deviceCurveTypeDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            deviceCurveTypeDataGridViewTextBoxColumn.HeaderText = "Curve Type";
            deviceCurveTypeDataGridViewTextBoxColumn.MinimumWidth = 22;
            deviceCurveTypeDataGridViewTextBoxColumn.Name = "deviceCurveTypeDataGridViewTextBoxColumn";
            deviceCurveTypeDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            deviceCurveTypeDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            deviceCurveTypeDataGridViewTextBoxColumn.Width = 60;
            // 
            // protectionDeviceDataGridViewTextBoxColumn
            // 
            protectionDeviceDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            protectionDeviceDataGridViewTextBoxColumn.DataPropertyName = "Protection_Device";
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = Color.White;
            protectionDeviceDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle5;
            protectionDeviceDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            protectionDeviceDataGridViewTextBoxColumn.HeaderText = "Device";
            protectionDeviceDataGridViewTextBoxColumn.MinimumWidth = 22;
            protectionDeviceDataGridViewTextBoxColumn.Name = "protectionDeviceDataGridViewTextBoxColumn";
            protectionDeviceDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            protectionDeviceDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            protectionDeviceDataGridViewTextBoxColumn.Width = 125;
            // 
            // rCDProtectionDataGridViewTextBoxColumn
            // 
            rCDProtectionDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            rCDProtectionDataGridViewTextBoxColumn.DataPropertyName = "RCD_Protection";
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = Color.White;
            rCDProtectionDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle6;
            rCDProtectionDataGridViewTextBoxColumn.HeaderText = "RCD Protection";
            rCDProtectionDataGridViewTextBoxColumn.MinimumWidth = 22;
            rCDProtectionDataGridViewTextBoxColumn.Name = "rCDProtectionDataGridViewTextBoxColumn";
            rCDProtectionDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            rCDProtectionDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            rCDProtectionDataGridViewTextBoxColumn.Width = 125;
            // 
            // otherControlsDataGridViewTextBoxColumn
            // 
            otherControlsDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            otherControlsDataGridViewTextBoxColumn.DataPropertyName = "Other_Controls";
            dataGridViewCellStyle7.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = Color.White;
            otherControlsDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle7;
            otherControlsDataGridViewTextBoxColumn.HeaderText = "Controls/Interfaces";
            otherControlsDataGridViewTextBoxColumn.MinimumWidth = 22;
            otherControlsDataGridViewTextBoxColumn.Name = "otherControlsDataGridViewTextBoxColumn";
            otherControlsDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            otherControlsDataGridViewTextBoxColumn.Width = 125;
            // 
            // cableToFirstCircuitComponentDataGridViewTextBoxColumn
            // 
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.DataPropertyName = "Cable_To_First_Circuit_Component";
            dataGridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = Color.White;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle8;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.HeaderText = "Cable To First";
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.MinimumWidth = 22;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.Name = "cableToFirstCircuitComponentDataGridViewTextBoxColumn";
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.ToolTipText = "Type of cable used between the DB to first element in circuit";
            cableToFirstCircuitComponentDataGridViewTextBoxColumn.Width = 210;
            // 
            // cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn
            // 
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.DataPropertyName = "Cable_To_Remainder_Of_Circuit_Components";
            dataGridViewCellStyle9.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle9.BackColor = Color.White;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle9;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.HeaderText = "Cable To Remainder";
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.MinimumWidth = 22;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.Name = "cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn";
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.ToolTipText = "Type of cable used between the different circuit elements";
            cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn.Width = 210;
            // 
            // cableInstallationMethodDataGridViewTextBoxColumn
            // 
            cableInstallationMethodDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            cableInstallationMethodDataGridViewTextBoxColumn.DataPropertyName = "Cable_Installation_Method";
            dataGridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle10.BackColor = Color.White;
            cableInstallationMethodDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle10;
            cableInstallationMethodDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            cableInstallationMethodDataGridViewTextBoxColumn.HeaderText = "Installation Method";
            cableInstallationMethodDataGridViewTextBoxColumn.MinimumWidth = 22;
            cableInstallationMethodDataGridViewTextBoxColumn.Name = "cableInstallationMethodDataGridViewTextBoxColumn";
            cableInstallationMethodDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            cableInstallationMethodDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cableInstallationMethodDataGridViewTextBoxColumn.Width = 200;
            // 
            // deratingFactorDataGridViewTextBoxColumn
            // 
            deratingFactorDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            deratingFactorDataGridViewTextBoxColumn.DataPropertyName = "Derating_Factor";
            dataGridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle11.BackColor = Color.White;
            deratingFactorDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle11;
            deratingFactorDataGridViewTextBoxColumn.HeaderText = "Derating Factor (%)";
            deratingFactorDataGridViewTextBoxColumn.MinimumWidth = 22;
            deratingFactorDataGridViewTextBoxColumn.Name = "deratingFactorDataGridViewTextBoxColumn";
            deratingFactorDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            deratingFactorDataGridViewTextBoxColumn.Width = 60;
            // 
            // diversityDataGridViewTextBoxColumn
            // 
            diversityDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            diversityDataGridViewTextBoxColumn.DataPropertyName = "Diversity";
            dataGridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle12.BackColor = Color.White;
            diversityDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle12;
            diversityDataGridViewTextBoxColumn.HeaderText = "Diversity (%)";
            diversityDataGridViewTextBoxColumn.MinimumWidth = 22;
            diversityDataGridViewTextBoxColumn.Name = "diversityDataGridViewTextBoxColumn";
            diversityDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            diversityDataGridViewTextBoxColumn.Width = 60;
            // 
            // Manual
            // 
            Manual.DataPropertyName = "Manual";
            Manual.FalseValue = "False";
            Manual.HeaderText = "Manual Length";
            Manual.MinimumWidth = 22;
            Manual.Name = "Manual";
            Manual.SortMode = DataGridViewColumnSortMode.Programmatic;
            Manual.TrueValue = "True";
            Manual.Width = 125;
            // 
            // cablePathSelectionDataGridViewTextBoxColumn
            // 
            cablePathSelectionDataGridViewTextBoxColumn.DataPropertyName = "Path_Mode";
            cablePathSelectionDataGridViewTextBoxColumn.FlatStyle = FlatStyle.Flat;
            cablePathSelectionDataGridViewTextBoxColumn.HeaderText = "Cable Path Mode Selection";
            cablePathSelectionDataGridViewTextBoxColumn.MinimumWidth = 22;
            cablePathSelectionDataGridViewTextBoxColumn.Name = "cablePathSelectionDataGridViewTextBoxColumn";
            cablePathSelectionDataGridViewTextBoxColumn.Resizable = DataGridViewTriState.True;
            cablePathSelectionDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cablePathSelectionDataGridViewTextBoxColumn.ToolTipText = resources.GetString("cablePathSelectionDataGridViewTextBoxColumn.ToolTipText");
            cablePathSelectionDataGridViewTextBoxColumn.Width = 125;
            // 
            // cableSetPathDataGridViewButtonColumn
            // 
            dataGridViewCellStyle13.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle13.BackColor = Color.FromArgb(137, 212, 217);
            dataGridViewCellStyle13.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle13.ForeColor = Color.Black;
            dataGridViewCellStyle13.SelectionBackColor = SystemColors.Highlight;
            cableSetPathDataGridViewButtonColumn.DefaultCellStyle = dataGridViewCellStyle13;
            cableSetPathDataGridViewButtonColumn.FlatStyle = FlatStyle.Popup;
            cableSetPathDataGridViewButtonColumn.HeaderText = "Edit Custom Cable Path";
            cableSetPathDataGridViewButtonColumn.MinimumWidth = 22;
            cableSetPathDataGridViewButtonColumn.Name = "cableSetPathDataGridViewButtonColumn";
            cableSetPathDataGridViewButtonColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cableSetPathDataGridViewButtonColumn.Text = "Set Path";
            cableSetPathDataGridViewButtonColumn.ToolTipText = "Opens circuit path editor in a view that only shows that circuit.";
            cableSetPathDataGridViewButtonColumn.UseColumnTextForButtonValue = true;
            cableSetPathDataGridViewButtonColumn.Width = 60;
            // 
            // lengthToFirstDataGridViewTextBoxColumn
            // 
            lengthToFirstDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            lengthToFirstDataGridViewTextBoxColumn.DataPropertyName = "Length_To_First";
            dataGridViewCellStyle14.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle14.BackColor = Color.White;
            lengthToFirstDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle14;
            lengthToFirstDataGridViewTextBoxColumn.HeaderText = "Length First";
            lengthToFirstDataGridViewTextBoxColumn.MinimumWidth = 22;
            lengthToFirstDataGridViewTextBoxColumn.Name = "lengthToFirstDataGridViewTextBoxColumn";
            lengthToFirstDataGridViewTextBoxColumn.ReadOnly = true;
            lengthToFirstDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            lengthToFirstDataGridViewTextBoxColumn.ToolTipText = "Length of cable to reach first component";
            lengthToFirstDataGridViewTextBoxColumn.Width = 60;
            // 
            // lengthToFinalDataGridViewTextBoxColumn
            // 
            lengthToFinalDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            lengthToFinalDataGridViewTextBoxColumn.DataPropertyName = "Length_To_Final";
            dataGridViewCellStyle15.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle15.BackColor = Color.White;
            lengthToFinalDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle15;
            lengthToFinalDataGridViewTextBoxColumn.HeaderText = "Length Total";
            lengthToFinalDataGridViewTextBoxColumn.MinimumWidth = 22;
            lengthToFinalDataGridViewTextBoxColumn.Name = "lengthToFinalDataGridViewTextBoxColumn";
            lengthToFinalDataGridViewTextBoxColumn.ReadOnly = true;
            lengthToFinalDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            lengthToFinalDataGridViewTextBoxColumn.ToolTipText = "Overall Length of the cable";
            lengthToFinalDataGridViewTextBoxColumn.Width = 60;
            // 
            // ManualCurrent
            // 
            ManualCurrent.DataPropertyName = "ManualCurrent";
            ManualCurrent.HeaderText = "Manual Current";
            ManualCurrent.MinimumWidth = 22;
            ManualCurrent.Name = "ManualCurrent";
            ManualCurrent.SortMode = DataGridViewColumnSortMode.Programmatic;
            ManualCurrent.Width = 60;
            // 
            // unDiversifiedCurrentDataGridViewTextBoxColumn
            // 
            unDiversifiedCurrentDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            dataGridViewCellStyle16.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle16.BackColor = Color.LightGray;
            dataGridViewCellStyle16.WrapMode = DataGridViewTriState.True;
            unDiversifiedCurrentDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle16;
            unDiversifiedCurrentDataGridViewTextBoxColumn.HeaderText = "PowerBim Current";
            unDiversifiedCurrentDataGridViewTextBoxColumn.MinimumWidth = 22;
            unDiversifiedCurrentDataGridViewTextBoxColumn.Name = "unDiversifiedCurrentDataGridViewTextBoxColumn";
            unDiversifiedCurrentDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            unDiversifiedCurrentDataGridViewTextBoxColumn.Width = 40;
            // 
            // numberOfElementsDataGridViewButtonColumn
            // 
            numberOfElementsDataGridViewButtonColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            numberOfElementsDataGridViewButtonColumn.DataPropertyName = "Number_Of_Elements";
            dataGridViewCellStyle17.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle17.BackColor = Color.LightGray;
            dataGridViewCellStyle17.Padding = new Padding(6, 2, 6, 2);
            numberOfElementsDataGridViewButtonColumn.DefaultCellStyle = dataGridViewCellStyle17;
            numberOfElementsDataGridViewButtonColumn.FlatStyle = FlatStyle.Popup;
            numberOfElementsDataGridViewButtonColumn.HeaderText = "Number Of Elements";
            numberOfElementsDataGridViewButtonColumn.MinimumWidth = 22;
            numberOfElementsDataGridViewButtonColumn.Name = "numberOfElementsDataGridViewButtonColumn";
            numberOfElementsDataGridViewButtonColumn.ReadOnly = true;
            numberOfElementsDataGridViewButtonColumn.Resizable = DataGridViewTriState.True;
            numberOfElementsDataGridViewButtonColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            numberOfElementsDataGridViewButtonColumn.Width = 60;
            // 
            // circuitDescriptionDataGridViewTextBoxColumn
            // 
            circuitDescriptionDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            circuitDescriptionDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Description";
            dataGridViewCellStyle18.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle18.BackColor = Color.White;
            circuitDescriptionDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle18;
            circuitDescriptionDataGridViewTextBoxColumn.HeaderText = "Description";
            circuitDescriptionDataGridViewTextBoxColumn.MinimumWidth = 22;
            circuitDescriptionDataGridViewTextBoxColumn.Name = "circuitDescriptionDataGridViewTextBoxColumn";
            circuitDescriptionDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            circuitDescriptionDataGridViewTextBoxColumn.Width = 85;
            // 
            // checkTripRatingDataGridViewTextBoxColumn
            // 
            checkTripRatingDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkTripRatingDataGridViewTextBoxColumn.DataPropertyName = "Check_Trip_Rating";
            dataGridViewCellStyle19.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle19.BackColor = Color.LightGray;
            dataGridViewCellStyle19.ForeColor = Color.FromArgb(141, 14, 132);
            checkTripRatingDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle19;
            checkTripRatingDataGridViewTextBoxColumn.HeaderText = "Check Trip Rating";
            checkTripRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkTripRatingDataGridViewTextBoxColumn.Name = "checkTripRatingDataGridViewTextBoxColumn";
            checkTripRatingDataGridViewTextBoxColumn.ReadOnly = true;
            checkTripRatingDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkTripRatingDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkTripRatingDataGridViewTextBoxColumn.Width = 80;
            // 
            // cable1ValidDataGridViewTextBoxColumn
            // 
            cable1ValidDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            cable1ValidDataGridViewTextBoxColumn.DataPropertyName = "Cable_1_Valid";
            dataGridViewCellStyle20.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle20.BackColor = Color.LightGray;
            dataGridViewCellStyle20.ForeColor = Color.FromArgb(141, 14, 132);
            cable1ValidDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle20;
            cable1ValidDataGridViewTextBoxColumn.HeaderText = "Cable 1 Valid";
            cable1ValidDataGridViewTextBoxColumn.MinimumWidth = 22;
            cable1ValidDataGridViewTextBoxColumn.Name = "cable1ValidDataGridViewTextBoxColumn";
            cable1ValidDataGridViewTextBoxColumn.ReadOnly = true;
            cable1ValidDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cable1ValidDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            cable1ValidDataGridViewTextBoxColumn.Width = 60;
            // 
            // cable2ValidDataGridViewTextBoxColumn
            // 
            cable2ValidDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            cable2ValidDataGridViewTextBoxColumn.DataPropertyName = "Cable_2_Valid";
            dataGridViewCellStyle21.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle21.BackColor = Color.LightGray;
            dataGridViewCellStyle21.ForeColor = Color.FromArgb(141, 14, 132);
            cable2ValidDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle21;
            cable2ValidDataGridViewTextBoxColumn.HeaderText = "Cable 2 Valid";
            cable2ValidDataGridViewTextBoxColumn.MinimumWidth = 22;
            cable2ValidDataGridViewTextBoxColumn.Name = "cable2ValidDataGridViewTextBoxColumn";
            cable2ValidDataGridViewTextBoxColumn.ReadOnly = true;
            cable2ValidDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            cable2ValidDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            cable2ValidDataGridViewTextBoxColumn.Width = 60;
            // 
            // checkCPDDescriminatesDataGridViewTextBoxColumn
            // 
            checkCPDDescriminatesDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkCPDDescriminatesDataGridViewTextBoxColumn.DataPropertyName = "Check_CPD_Descriminates";
            dataGridViewCellStyle22.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle22.BackColor = Color.LightGray;
            dataGridViewCellStyle22.ForeColor = Color.FromArgb(141, 14, 132);
            checkCPDDescriminatesDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle22;
            checkCPDDescriminatesDataGridViewTextBoxColumn.HeaderText = "Check CPD Descriminates";
            checkCPDDescriminatesDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkCPDDescriminatesDataGridViewTextBoxColumn.Name = "checkCPDDescriminatesDataGridViewTextBoxColumn";
            checkCPDDescriminatesDataGridViewTextBoxColumn.ReadOnly = true;
            checkCPDDescriminatesDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkCPDDescriminatesDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkCPDDescriminatesDataGridViewTextBoxColumn.Width = 143;
            // 
            // checkLoadCurrentDataGridViewTextBoxColumn
            // 
            checkLoadCurrentDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkLoadCurrentDataGridViewTextBoxColumn.DataPropertyName = "Check_Load_Current";
            dataGridViewCellStyle23.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle23.BackColor = Color.LightGray;
            dataGridViewCellStyle23.ForeColor = Color.FromArgb(141, 14, 132);
            checkLoadCurrentDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle23;
            checkLoadCurrentDataGridViewTextBoxColumn.HeaderText = "Check Load Current";
            checkLoadCurrentDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkLoadCurrentDataGridViewTextBoxColumn.Name = "checkLoadCurrentDataGridViewTextBoxColumn";
            checkLoadCurrentDataGridViewTextBoxColumn.ReadOnly = true;
            checkLoadCurrentDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkLoadCurrentDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkLoadCurrentDataGridViewTextBoxColumn.Width = 86;
            // 
            // checkCable1CurrentDataGridViewTextBoxColumn
            // 
            checkCable1CurrentDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkCable1CurrentDataGridViewTextBoxColumn.DataPropertyName = "Check_Cable_1_Current";
            dataGridViewCellStyle24.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle24.BackColor = Color.LightGray;
            dataGridViewCellStyle24.ForeColor = Color.FromArgb(141, 14, 132);
            checkCable1CurrentDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle24;
            checkCable1CurrentDataGridViewTextBoxColumn.HeaderText = "Check Cable 1 Current";
            checkCable1CurrentDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkCable1CurrentDataGridViewTextBoxColumn.Name = "checkCable1CurrentDataGridViewTextBoxColumn";
            checkCable1CurrentDataGridViewTextBoxColumn.ReadOnly = true;
            checkCable1CurrentDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkCable1CurrentDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkCable1CurrentDataGridViewTextBoxColumn.Width = 66;
            // 
            // checkCable2CurrentDataGridViewTextBoxColumn
            // 
            checkCable2CurrentDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkCable2CurrentDataGridViewTextBoxColumn.DataPropertyName = "Check_Cable_2_Current";
            dataGridViewCellStyle25.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle25.BackColor = Color.LightGray;
            dataGridViewCellStyle25.ForeColor = Color.FromArgb(141, 14, 132);
            checkCable2CurrentDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle25;
            checkCable2CurrentDataGridViewTextBoxColumn.HeaderText = "Check Cable 2 Current";
            checkCable2CurrentDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkCable2CurrentDataGridViewTextBoxColumn.Name = "checkCable2CurrentDataGridViewTextBoxColumn";
            checkCable2CurrentDataGridViewTextBoxColumn.ReadOnly = true;
            checkCable2CurrentDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkCable2CurrentDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkCable2CurrentDataGridViewTextBoxColumn.Width = 66;
            // 
            // checkEFLIDataGridViewTextBoxColumn
            // 
            checkEFLIDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkEFLIDataGridViewTextBoxColumn.DataPropertyName = "Check_EFLI";
            dataGridViewCellStyle26.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle26.BackColor = Color.LightGray;
            dataGridViewCellStyle26.ForeColor = Color.FromArgb(141, 14, 132);
            checkEFLIDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle26;
            checkEFLIDataGridViewTextBoxColumn.HeaderText = "Check EFLI";
            checkEFLIDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkEFLIDataGridViewTextBoxColumn.Name = "checkEFLIDataGridViewTextBoxColumn";
            checkEFLIDataGridViewTextBoxColumn.ReadOnly = true;
            checkEFLIDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkEFLIDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkEFLIDataGridViewTextBoxColumn.Width = 81;
            // 
            // checkFinalCCTVDDataGridViewTextBoxColumn
            // 
            checkFinalCCTVDDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkFinalCCTVDDataGridViewTextBoxColumn.DataPropertyName = "Check_Final_CCT_VD";
            dataGridViewCellStyle27.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle27.BackColor = Color.LightGray;
            dataGridViewCellStyle27.ForeColor = Color.FromArgb(141, 14, 132);
            checkFinalCCTVDDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle27;
            checkFinalCCTVDDataGridViewTextBoxColumn.HeaderText = "Check Final CCT VD";
            checkFinalCCTVDDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkFinalCCTVDDataGridViewTextBoxColumn.Name = "checkFinalCCTVDDataGridViewTextBoxColumn";
            checkFinalCCTVDDataGridViewTextBoxColumn.ReadOnly = true;
            checkFinalCCTVDDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkFinalCCTVDDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkFinalCCTVDDataGridViewTextBoxColumn.Width = 66;
            // 
            // checkSystemMaxVDDataGridViewTextBoxColumn
            // 
            checkSystemMaxVDDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkSystemMaxVDDataGridViewTextBoxColumn.DataPropertyName = "Check_System_Max_VD";
            dataGridViewCellStyle28.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle28.BackColor = Color.LightGray;
            dataGridViewCellStyle28.ForeColor = Color.FromArgb(141, 14, 132);
            checkSystemMaxVDDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle28;
            checkSystemMaxVDDataGridViewTextBoxColumn.HeaderText = "Check System Max VD";
            checkSystemMaxVDDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkSystemMaxVDDataGridViewTextBoxColumn.Name = "checkSystemMaxVDDataGridViewTextBoxColumn";
            checkSystemMaxVDDataGridViewTextBoxColumn.ReadOnly = true;
            checkSystemMaxVDDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkSystemMaxVDDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkSystemMaxVDDataGridViewTextBoxColumn.Width = 85;
            // 
            // checkCable1SCWithstandDataGridViewTextBoxColumn
            // 
            checkCable1SCWithstandDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkCable1SCWithstandDataGridViewTextBoxColumn.DataPropertyName = "Check_Cable_1_SC_Withstand";
            dataGridViewCellStyle29.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle29.BackColor = Color.LightGray;
            dataGridViewCellStyle29.ForeColor = Color.FromArgb(141, 14, 132);
            checkCable1SCWithstandDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle29;
            checkCable1SCWithstandDataGridViewTextBoxColumn.HeaderText = "Check Cable 1 SC Withstand";
            checkCable1SCWithstandDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkCable1SCWithstandDataGridViewTextBoxColumn.Name = "checkCable1SCWithstandDataGridViewTextBoxColumn";
            checkCable1SCWithstandDataGridViewTextBoxColumn.ReadOnly = true;
            checkCable1SCWithstandDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkCable1SCWithstandDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkCable1SCWithstandDataGridViewTextBoxColumn.Width = 81;
            // 
            // checkCable2SCWithstandDataGridViewTextBoxColumn
            // 
            checkCable2SCWithstandDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            checkCable2SCWithstandDataGridViewTextBoxColumn.DataPropertyName = "Check_Cable_2_SC_Withstand";
            dataGridViewCellStyle30.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle30.BackColor = Color.LightGray;
            dataGridViewCellStyle30.ForeColor = Color.FromArgb(141, 14, 132);
            checkCable2SCWithstandDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle30;
            checkCable2SCWithstandDataGridViewTextBoxColumn.HeaderText = "Check Cable 2 SC Withstand";
            checkCable2SCWithstandDataGridViewTextBoxColumn.MinimumWidth = 22;
            checkCable2SCWithstandDataGridViewTextBoxColumn.Name = "checkCable2SCWithstandDataGridViewTextBoxColumn";
            checkCable2SCWithstandDataGridViewTextBoxColumn.ReadOnly = true;
            checkCable2SCWithstandDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            checkCable2SCWithstandDataGridViewTextBoxColumn.ToolTipText = "[Max Allowed] / [Calculated Value].. Note, if the calculated value is reported in parenthesis then it's a failing result. ";
            checkCable2SCWithstandDataGridViewTextBoxColumn.Width = 81;
            // 
            // circuitCheckSummaryDataGridViewTextBoxColumn
            // 
            circuitCheckSummaryDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            circuitCheckSummaryDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Check_Summary";
            dataGridViewCellStyle31.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle31.BackColor = Color.LightGray;
            dataGridViewCellStyle31.ForeColor = Color.FromArgb(141, 14, 132);
            circuitCheckSummaryDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle31;
            circuitCheckSummaryDataGridViewTextBoxColumn.HeaderText = "Circuit_Check_Summary";
            circuitCheckSummaryDataGridViewTextBoxColumn.MinimumWidth = 22;
            circuitCheckSummaryDataGridViewTextBoxColumn.Name = "circuitCheckSummaryDataGridViewTextBoxColumn";
            circuitCheckSummaryDataGridViewTextBoxColumn.ReadOnly = true;
            circuitCheckSummaryDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            circuitCheckSummaryDataGridViewTextBoxColumn.ToolTipText = "Verbose report of circuit check calculation";
            circuitCheckSummaryDataGridViewTextBoxColumn.Width = 147;
            // 
            // circuitCheckResultDataGridViewTextBoxColumn
            // 
            circuitCheckResultDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            circuitCheckResultDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Check_Result";
            dataGridViewCellStyle32.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle32.BackColor = Color.LightGray;
            dataGridViewCellStyle32.ForeColor = Color.FromArgb(141, 14, 132);
            circuitCheckResultDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle32;
            circuitCheckResultDataGridViewTextBoxColumn.HeaderText = "Circuit Check Result";
            circuitCheckResultDataGridViewTextBoxColumn.MinimumWidth = 22;
            circuitCheckResultDataGridViewTextBoxColumn.Name = "circuitCheckResultDataGridViewTextBoxColumn";
            circuitCheckResultDataGridViewTextBoxColumn.ReadOnly = true;
            circuitCheckResultDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            circuitCheckResultDataGridViewTextBoxColumn.Width = 90;
            // 
            // circuitRevisionDataGridViewTextBoxColumn
            // 
            circuitRevisionDataGridViewTextBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            circuitRevisionDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Revision";
            dataGridViewCellStyle33.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle33.BackColor = Color.White;
            circuitRevisionDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle33;
            circuitRevisionDataGridViewTextBoxColumn.HeaderText = "Circuit Revision";
            circuitRevisionDataGridViewTextBoxColumn.MinimumWidth = 22;
            circuitRevisionDataGridViewTextBoxColumn.Name = "circuitRevisionDataGridViewTextBoxColumn";
            circuitRevisionDataGridViewTextBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            circuitRevisionDataGridViewTextBoxColumn.Width = 97;
            // 
            // isSpareOrSpaceDataGridViewCheckBoxColumn
            // 
            isSpareOrSpaceDataGridViewCheckBoxColumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.None;
            isSpareOrSpaceDataGridViewCheckBoxColumn.DataPropertyName = "isSpareOrSpace";
            dataGridViewCellStyle34.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle34.BackColor = Color.LightGray;
            dataGridViewCellStyle34.NullValue = false;
            isSpareOrSpaceDataGridViewCheckBoxColumn.DefaultCellStyle = dataGridViewCellStyle34;
            isSpareOrSpaceDataGridViewCheckBoxColumn.HeaderText = "Is Spare Or Space";
            isSpareOrSpaceDataGridViewCheckBoxColumn.MinimumWidth = 22;
            isSpareOrSpaceDataGridViewCheckBoxColumn.Name = "isSpareOrSpaceDataGridViewCheckBoxColumn";
            isSpareOrSpaceDataGridViewCheckBoxColumn.ReadOnly = true;
            isSpareOrSpaceDataGridViewCheckBoxColumn.SortMode = DataGridViewColumnSortMode.Programmatic;
            isSpareOrSpaceDataGridViewCheckBoxColumn.Visible = false;
            isSpareOrSpaceDataGridViewCheckBoxColumn.Width = 60;
            // 
            // enhancedCCTTableBindingSource
            // 
            enhancedCCTTableBindingSource.DataMember = "EnhancedCCTTable";
            enhancedCCTTableBindingSource.DataSource = enhancedCCTDataSet;
            // 
            // enhancedCCTDataSet
            // 
            enhancedCCTDataSet.DataSetName = "EnhancedCCTDataSet";
            enhancedCCTDataSet.Namespace = "http://tempuri.org/EnhancedCCTDataSet.xsd";
            enhancedCCTDataSet.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // adgv_SearchBar
            // 
            adgv_SearchBar.AllowMerge = false;
            adgv_SearchBar.GripStyle = ToolStripGripStyle.Hidden;
            adgv_SearchBar.ImageScalingSize = new Size(20, 20);
            adgv_SearchBar.Location = new Point(0, 0);
            adgv_SearchBar.MaximumSize = new Size(0, 27);
            adgv_SearchBar.MinimumSize = new Size(0, 27);
            adgv_SearchBar.Name = "adgv_SearchBar";
            adgv_SearchBar.RenderMode = ToolStripRenderMode.Professional;
            adgv_SearchBar.Size = new Size(1220, 27);
            adgv_SearchBar.TabIndex = 202;
            adgv_SearchBar.Text = "advancedDataGridViewSearchToolBar1";
            adgv_SearchBar.Search += adgv_SearchBar_Search;
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label1.Font = new Font("Arial", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            label1.ForeColor = Color.FromArgb(141, 14, 132);
            label1.Location = new Point(2, 669);
            label1.Margin = new Padding(2, 0, 2, 0);
            label1.Name = "label1";
            label1.Size = new Size(500, 97);
            label1.TabIndex = 204;
            label1.Text = "Tips: \r\n- Left-click on a cell or row of cells to select, then right-click and drag to copy data just like in Excel!\r\n- Any unsaved Changes will be lost when you close this window.";
            label1.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 3;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 13F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel1.Controls.Add(dgv_DiversifiedPhase, 0, 1);
            tableLayoutPanel1.Controls.Add(dgv_UnDiversifiedPhase, 2, 1);
            tableLayoutPanel1.Controls.Add(label2, 0, 0);
            tableLayoutPanel1.Controls.Add(label3, 2, 0);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(942, 672);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 2;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 28F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Size = new Size(498, 91);
            tableLayoutPanel1.TabIndex = 205;
            // 
            // dgv_DiversifiedPhase
            // 
            dgv_DiversifiedPhase.AllowUserToAddRows = false;
            dgv_DiversifiedPhase.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgv_DiversifiedPhase.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_DiversifiedPhase.Columns.AddRange(new DataGridViewColumn[] { PhaseR, PhaseW, PhaseB });
            dgv_DiversifiedPhase.Dock = DockStyle.Fill;
            dgv_DiversifiedPhase.Location = new Point(3, 31);
            dgv_DiversifiedPhase.Name = "dgv_DiversifiedPhase";
            dgv_DiversifiedPhase.RowHeadersVisible = false;
            dgv_DiversifiedPhase.Size = new Size(236, 57);
            dgv_DiversifiedPhase.TabIndex = 0;
            // 
            // dgv_UnDiversifiedPhase
            // 
            dgv_UnDiversifiedPhase.AllowUserToAddRows = false;
            dgv_UnDiversifiedPhase.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgv_UnDiversifiedPhase.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgv_UnDiversifiedPhase.Columns.AddRange(new DataGridViewColumn[] { RevitPhaseR, RevitPhaseW, RevitPhaseB });
            dgv_UnDiversifiedPhase.Dock = DockStyle.Fill;
            dgv_UnDiversifiedPhase.Location = new Point(258, 31);
            dgv_UnDiversifiedPhase.Name = "dgv_UnDiversifiedPhase";
            dgv_UnDiversifiedPhase.RowHeadersVisible = false;
            dgv_UnDiversifiedPhase.Size = new Size(237, 57);
            dgv_UnDiversifiedPhase.TabIndex = 1;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.BackColor = Color.FromArgb(141, 14, 132);
            label2.Dock = DockStyle.Fill;
            label2.ForeColor = Color.White;
            label2.Location = new Point(3, 0);
            label2.Name = "label2";
            label2.Size = new Size(236, 28);
            label2.TabIndex = 2;
            label2.Text = "Diversified Phase Loading (Run Power BIM)";
            label2.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.BackColor = Color.FromArgb(141, 14, 132);
            label3.Dock = DockStyle.Fill;
            label3.ForeColor = Color.White;
            label3.Location = new Point(258, 0);
            label3.Name = "label3";
            label3.Size = new Size(237, 28);
            label3.TabIndex = 3;
            label3.Text = "Undiversified Phase Loading";
            label3.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // PhaseR
            // 
            dataGridViewCellStyle36.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle36.ForeColor = Color.FromArgb(141, 14, 132);
            PhaseR.DefaultCellStyle = dataGridViewCellStyle36;
            PhaseR.HeaderText = "Phase R";
            PhaseR.Name = "PhaseR";
            PhaseR.ReadOnly = true;
            // 
            // PhaseW
            // 
            dataGridViewCellStyle37.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle37.ForeColor = Color.FromArgb(141, 14, 132);
            PhaseW.DefaultCellStyle = dataGridViewCellStyle37;
            PhaseW.HeaderText = "Phase W";
            PhaseW.Name = "PhaseW";
            PhaseW.ReadOnly = true;
            // 
            // PhaseB
            // 
            dataGridViewCellStyle38.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle38.ForeColor = Color.FromArgb(141, 14, 132);
            PhaseB.DefaultCellStyle = dataGridViewCellStyle38;
            PhaseB.HeaderText = "Phase B";
            PhaseB.Name = "PhaseB";
            PhaseB.ReadOnly = true;
            // 
            // RevitPhaseR
            // 
            dataGridViewCellStyle39.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle39.ForeColor = Color.FromArgb(141, 14, 132);
            RevitPhaseR.DefaultCellStyle = dataGridViewCellStyle39;
            RevitPhaseR.HeaderText = "Phase R";
            RevitPhaseR.Name = "RevitPhaseR";
            RevitPhaseR.ReadOnly = true;
            // 
            // RevitPhaseW
            // 
            dataGridViewCellStyle40.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle40.ForeColor = Color.FromArgb(141, 14, 132);
            RevitPhaseW.DefaultCellStyle = dataGridViewCellStyle40;
            RevitPhaseW.HeaderText = "PhaseW";
            RevitPhaseW.Name = "RevitPhaseW";
            RevitPhaseW.ReadOnly = true;
            // 
            // RevitPhaseB
            // 
            dataGridViewCellStyle41.Font = new Font("Microsoft Sans Serif", 8.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            dataGridViewCellStyle41.ForeColor = Color.FromArgb(141, 14, 132);
            RevitPhaseB.DefaultCellStyle = dataGridViewCellStyle41;
            RevitPhaseB.HeaderText = "Phase B";
            RevitPhaseB.Name = "RevitPhaseB";
            RevitPhaseB.ReadOnly = true;
            // 
            // FrmPowerBIM_CircuitEditEnhanced
            // 
            AutoScaleDimensions = new SizeF(6F, 13F);
            AutoScaleMode = AutoScaleMode.Font;
            btnHelp_Visiblity = true;
            ClientSize = new Size(1443, 857);
            Controls.Add(tlp_MainContent);
            Margin = new Padding(4, 4, 4, 4);
            MinimumSize = new Size(720, 480);
            Name = "FrmPowerBIM_CircuitEditEnhanced";
            Text = "PowerBIM | Edit Circuits";
            TitleText = "Edit Circuit Properties";
            VerisionText = "© 2021   01.05.01  ";
            FormClosing += FrmClosing;
            Load += EnhancedFrmPowerBIMcircuitEdit_Load;
            Controls.SetChildIndex(tlp_MainContent, 0);
            tlp_MainContent.ResumeLayout(false);
            gb_CircuitData.ResumeLayout(false);
            tlp_CircuitsData.ResumeLayout(false);
            tlp_CircuitsData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)AdgvCircuitData).EndInit();
            ((System.ComponentModel.ISupportInitialize)enhancedCCTTableBindingSource).EndInit();
            ((System.ComponentModel.ISupportInitialize)enhancedCCTDataSet).EndInit();
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgv_DiversifiedPhase).EndInit();
            ((System.ComponentModel.ISupportInitialize)dgv_UnDiversifiedPhase).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tlp_MainContent;
        private Common.UI.Controls.ExtendedAdvancedDataGridView AdgvCircuitData;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnRun;
        private Zuby.ADGV.AdvancedDataGridViewSearchToolBar adgv_SearchBar;
        private System.Windows.Forms.GroupBox gb_CircuitData;
        private System.Windows.Forms.TableLayoutPanel tlp_CircuitsData;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btn_ActivateEditPathView;
        private System.Windows.Forms.BindingSource enhancedCCTTableBindingSource;
        private PowerBIM_5.DataSets.EnhancedCCTDataSet enhancedCCTDataSet;
        private System.Windows.Forms.DataGridViewTextBoxColumn circuitNumberDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn deviceRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn deviceCurveTypeDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn protectionDeviceDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn rCDProtectionDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn otherControlsDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn cableToFirstCircuitComponentDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn cableToRemainderOfCircuitComponentsDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn cableInstallationMethodDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn deratingFactorDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn diversityDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewCheckBoxColumn Manual;
        private System.Windows.Forms.DataGridViewComboBoxColumn cablePathSelectionDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewButtonColumn cableSetPathDataGridViewButtonColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn lengthToFirstDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn lengthToFinalDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewCheckBoxColumn ManualCurrent;
        private System.Windows.Forms.DataGridViewTextBoxColumn unDiversifiedCurrentDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewButtonColumn numberOfElementsDataGridViewButtonColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn circuitDescriptionDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkTripRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn cable1ValidDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn cable2ValidDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkCPDDescriminatesDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkLoadCurrentDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkCable1CurrentDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkCable2CurrentDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkEFLIDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkFinalCCTVDDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkSystemMaxVDDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkCable1SCWithstandDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn checkCable2SCWithstandDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn circuitCheckSummaryDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn circuitCheckResultDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn circuitRevisionDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewCheckBoxColumn isSpareOrSpaceDataGridViewCheckBoxColumn;
        private TableLayoutPanel tableLayoutPanel1;
        private DataGridView dgv_DiversifiedPhase;
        private DataGridView dgv_UnDiversifiedPhase;
        private Label label2;
        private Label label3;
        private DataGridViewTextBoxColumn PhaseR;
        private DataGridViewTextBoxColumn PhaseW;
        private DataGridViewTextBoxColumn PhaseB;
        private DataGridViewTextBoxColumn RevitPhaseR;
        private DataGridViewTextBoxColumn RevitPhaseW;
        private DataGridViewTextBoxColumn RevitPhaseB;
    }
}