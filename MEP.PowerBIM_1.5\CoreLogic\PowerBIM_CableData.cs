﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_CableData
    {
        public static readonly string InvalidCableName = "Cannot Size Cable";

        private PowerBIM_ProjectInfo Project_Info { get; set; }
        private PowerBIM_CircuitData Circuit { get; set; }

        public string Cable_Name { get; set; }
        public int Cable_Index { get; set; }
        public bool is_CableToFirst { get; set; }

        public double Sp_Active { get; set; }
        public double Sp_Earth { get; set; }


        public double R_Rated_Active { get; set; }
        public double R_Rated_Earth { get; set; }
        public double R_Operating_Active { get; set; }
        public double R_Operating_Earth { get; set; }
        public double X_Max_Active { get; set; }
        public double X_Max_Earth { get; set; }
        public double Z_Operating_Active { get; set; }
        public double Z_Operating_Earth { get; set; }


        public double I_Rated { get; set; }
        public double Temperature { get; set; }
        public double K_Value { get; set; }
        public double I2t_Max { get; set; }
        public string Conductor_Material { get; set; }
        public string Insulation_Material { get; set; }
        public double Cable_Temperature_Limit { get; set; }


        public bool Data_Good { get; set; }
        public string Error_Message { get; set; }
        public bool Warning_UserDefCableSelected { get; set; }
        public bool Warning_EmLightingPresent { get; set; }


        public PowerBIM_CableData(PowerBIM_ProjectInfo pi, PowerBIM_CircuitData cct, bool isCableToFirst)
        {
            //
            // PowerBIM_CableData
            //
            // Constructor populating data from the passed DB
            //

            // Set Cable name to name of string passed.
            Circuit = cct;
            Project_Info = pi;
            is_CableToFirst = isCableToFirst;
        }

        public void UpdateCableData()
        {
            Get_CableFromNameOrNumber(Circuit);
            int elements = Circuit.Number_Of_Elements;

            // if 0 we couldnt find a match. mark data as bad
            if (Cable_Index > 0)
            {
                FindCableProp_General(Cable_Index);
                I_Rated = FindCableProp_RatedCurrent(Cable_Index, Circuit.Install_Method_Index);
                Temperature = FindCableProp_Temperature(Cable_Index);
                FindCableProp_RXZ_Rated(Cable_Index);
                FindCableProp_R_Operating(Cable_Index);
                K_Value = FindCableProp_KValue();
                I2t_Max = Calc_CableSCWithstand();
                Check_CableWarnings();

                // Mark cable data as good
                Data_Good = true;
            }
            else
            {
                Data_Good = false;

            }
        }

        private void Get_CableFromNameOrNumber(PowerBIM_CircuitData ctt)
        {
            //
            // This function could be passed either a cable ID number or a cable name.
            //      we need to check what was recieved, and then figue out what cable it's refering to. 
            //

            string scheduleCableName = is_CableToFirst ? ctt.Schedule_Cable_To_First : ctt.Schedule_Cable_To_Final;

            try
            {
                // if it's left blank, that's cool, we'll roll with that
                if (scheduleCableName == "" || scheduleCableName == null)
                {
                    Cable_Name = "";
                    Cable_Index = 0;
                }

                if (scheduleCableName != null && !is_CableToFirst && scheduleCableName == "-")
                {
                    Cable_Name = "-"; //DO WE NEED REFER TO SCHEDULE LOGIC?
                    Cable_Index = Find_CableDatabaseIndexFromName(ctt.Schedule_Cable_To_First);
                    //Cable_Index = (Project_Info.Standard_CableNames.Count + 1);
                }

                // If it is only 3 charaters, then it's probably a cable ID number
                else if (scheduleCableName != null && scheduleCableName.Length <= 3) //
                {
                    Cable_Name = Find_CableDatabaseNameFromIndex(scheduleCableName);

                    // if we found a match, save name and index
                    if (Cable_Name != "Cable match not found")
                    {
                        int intCableId = 0;
                        int.TryParse(scheduleCableName, out intCableId);
                        Cable_Index = intCableId;
                    }

                }
                // else, it's probably a name.. Check for a match
                else if (scheduleCableName != null)
                {
                    Cable_Index = Find_CableDatabaseIndexFromName(scheduleCableName);

                    // if we found a match, save name and index
                    if (Cable_Index != -1)
                        Cable_Name = scheduleCableName;
                }
            }
            catch
            {
                // Mark cable data as bad
                Data_Good = false;
            }
        }

        public void CreateNullEntry()
        {
            Cable_Name = InvalidCableName;
            Cable_Index = 0;
            Sp_Active = 0;
            Sp_Earth = 0;

            R_Rated_Active = 0;
            R_Rated_Earth = 0;
            R_Operating_Active = 0;
            R_Operating_Earth = 0;
            X_Max_Active = 0;
            X_Max_Earth = 0;
            Z_Operating_Active = 0;
            Z_Operating_Earth = 0;

            I_Rated = 0;
            Temperature = 0;
            K_Value = 0;
            I2t_Max = 0;
            Conductor_Material = "";
            Insulation_Material = "";
            Cable_Temperature_Limit = 0;

            Data_Good = true;
            Error_Message = "Invalid Cable Selected";
            Warning_UserDefCableSelected = false;
            Warning_EmLightingPresent = false;
        }

        #region public_methods


        private int Find_CableDatabaseIndexFromName(string scheduleCableName)
        {
            //
            // If Cable Remainder is name, convert to index vlaue
            //
            if (scheduleCableName == "-")
            {
                //TODO IF THIS HAPPENS WE MAY HAVE PROBLEMS!
            }


            for (int I = 0; I < PowerBIM_Constants.file_CableRowsMax; I++)
            {
                // ** 2.06b Check Cable 1st Valid: found in database, and it is for correct phase

                if (scheduleCableName == Project_Info.Cable_Database[I, 12] && Circuit.Number_Of_Poles.ToString() == Project_Info.Cable_Database[I, 5])  //Find exact match
                {
                    return I;
                }
            }

            // returns -1 for no match
            return -1;
        }

        private string Find_CableDatabaseNameFromIndex(string scheduleCableName)
        {
            //
            //If Cable Remainder is an ID, convert to name
            //


            int intCableId = 0;
            int.TryParse(scheduleCableName, out intCableId);

            // lets try find the cable in our database
            if (intCableId <= 190 && intCableId >= 1) // TODO update constants
            {
                return Project_Info.Cable_Database[intCableId, 12];
            }
            else
            {
                return "Cable match not found";
            }
        }

        private void Check_CableWarnings()
        {
            //
            // This method checks some parameters of the cable selected, and flags warnings if apporpirate
            //

            if (Circuit.Number_Of_Elements == 1 && Circuit.Cable_To_Final.Cable_Name == "-") //DO WE NEED REFER TO SCHEDULE LOGIC?
            { 
                Warning_UserDefCableSelected = true;
            }

            // add warning if user defined cable selected 
            if (Cable_Index > 127)  // TODO 127 shouldnt be hard coded
            {
                Warning_UserDefCableSelected = true;
            }

            // add warning if 3c cable selected on single phase
            if ((Circuit.Number_Of_Poles == 1) && (Cable_Name.Contains("x3C")))
            {
                Warning_EmLightingPresent = true;
            }
        }

        private bool FindCableProp_General(int I)
        {
            Conductor_Material = Project_Info.Cable_Database[I, 8]; //added for SC withstand calc
            Insulation_Material = Project_Info.Cable_Database[I, 6]; //added for SC withstand calc
            Sp_Active = double.Parse(Project_Info.Cable_Database[I, 3]); //added for SC withstand calc
            Sp_Earth = double.Parse(Project_Info.Cable_Database[I, 4]); //added for SC withstand calc

            return true; // TODO
        }

        private double FindCableProp_RatedCurrent(int cableIndex, int installMethodIndex)
        {
            double dblRatedCurrent = 0;

            // Get rated current for cable based on install method selected
            if (installMethodIndex >= 1 && installMethodIndex <= 10)
            {
                double.TryParse(Project_Info.Cable_Database[cableIndex, installMethodIndex + 31], out dblRatedCurrent);
            }

            // Return the rated cable current from cable database
            return dblRatedCurrent;
        }

        private double FindCableProp_Temperature(int I)
        {

            // ** 2.081 Get Rated Temperature Tr
            double dblTrCbl = double.Parse(Project_Info.Cable_Database[I, 7]); //revised for rev3 cabledata

            // Apply derating factor
            double dblDeratedIrCbl = Circuit.Schedule_Derating_Factor * I_Rated;

            //Calc Operating Temperature T1
            double dblCableTemp = ((dblTrCbl - Project_Info.Ambient_Temp) * ((Circuit.GetCurrent() / dblDeratedIrCbl) * (Circuit.GetCurrent() / dblDeratedIrCbl))) + Project_Info.Ambient_Temp;

            return dblCableTemp;
        }

        private bool FindCableProp_RXZ_Rated(int I)
        {
            // Set to worst case (Rmax and Xmax)
            R_Rated_Active = double.Parse(Project_Info.Cable_Database[I, 15]); //revised for vector calc
            R_Rated_Earth = double.Parse(Project_Info.Cable_Database[I, 23]); //revised for vector calc
            X_Max_Active = double.Parse(Project_Info.Cable_Database[I, 21]); //revised for vector calc
            X_Max_Earth = double.Parse(Project_Info.Cable_Database[I, 29]); //revised for vector calc

            return true; // TODO
        }

        private bool FindCableProp_R_Operating(int I)
        {
            // ** 2.082 Find ACTIVE Cable Rr from cable database
            if (Temperature <= 45)
                R_Operating_Active = double.Parse(Project_Info.Cable_Database[I, 16]);
            else if (Temperature <= 60)
                R_Operating_Active = double.Parse(Project_Info.Cable_Database[I, 17]);
            else if (Temperature <= 75)
                R_Operating_Active = double.Parse(Project_Info.Cable_Database[I, 18]);
            else if (Temperature <= 90)
                R_Operating_Active = double.Parse(Project_Info.Cable_Database[I, 19]);
            else
                R_Operating_Active = double.Parse(Project_Info.Cable_Database[I, 20]);

            // ** 2.083 Find EARTH Cable Rr from cable database
            if (Temperature <= 45)
                R_Operating_Earth = double.Parse(Project_Info.Cable_Database[I, 24]);
            else if (Temperature <= 60)
                R_Operating_Earth = double.Parse(Project_Info.Cable_Database[I, 25]);
            else if (Temperature <= 75)
                R_Operating_Earth = double.Parse(Project_Info.Cable_Database[I, 26]);
            else if (Temperature <= 90)
                R_Operating_Earth = double.Parse(Project_Info.Cable_Database[I, 27]);
            else
                R_Operating_Earth = double.Parse(Project_Info.Cable_Database[I, 28]);

            // ** 2.084 Calc Z
            Z_Operating_Active = Math.Sqrt((R_Operating_Active * R_Operating_Active) + (X_Max_Active * X_Max_Active));
            Z_Operating_Earth = Math.Sqrt((R_Operating_Earth * R_Operating_Earth) + (X_Max_Earth * X_Max_Earth));

            return true; // TODO
        }

        private double FindCableProp_KValue()
        {
            //
            // 2.9 Find K Value for cable from tables
            //

            double dblKValue = 204; // start with worst case max k value
            int intCbleTempLimitCol = 0;
            int intCbleCondMaterialCol = 0;

            /*
            Thermoplastic - up to 300mm         = 160
            Thermoplastic - greater than 300mm  = 140
            Elastomer                           = 250
            XLPE                                = 250
            High temp                           = 350
            */

            // Find Temp limit for cable
            switch (Insulation_Material.ToUpper())
            {
                case "PVC":
                    if (Sp_Active > 300)
                    {
                        Cable_Temperature_Limit = 140;
                        intCbleTempLimitCol = 0;
                    }
                    else
                    {
                        Cable_Temperature_Limit = 160;
                        intCbleTempLimitCol = 1;
                    }
                    break;
                case "XLPE":
                    Cable_Temperature_Limit = 250;
                    intCbleTempLimitCol = 2;
                    break;
                case "ELASTOMER":
                    Cable_Temperature_Limit = 350;
                    intCbleTempLimitCol = 3;
                    break;
                default:
                    dblKValue = 333;
                    break;
            }

            // Find cable material
            switch (Conductor_Material.ToUpper())
            {
                case "COPPER":
                    intCbleCondMaterialCol = 1;
                    break;
                case "ALUMINIUM":
                    intCbleCondMaterialCol = 5;
                    break;
                default:
                    dblKValue = 444;
                    break;
            }

            // Round cable temp to table values
            int intRoundedCblTemp = 25;
            if ((Temperature <= 90) && (Temperature > 25))
            {
                intRoundedCblTemp = 5 * (int)Math.Ceiling(Temperature / 5.0);
            }
            else if (Temperature <= 110)
            {
                intRoundedCblTemp = 110;
            }
            else if (Temperature <= 125)
            {
                intRoundedCblTemp = 125;
            }
            else
            {
                intRoundedCblTemp = 130;
            }

            // Find K value of cable from table
            for (int I = 0; I <= PowerBIM_Constants.file_KValueRowsMax - 1; I++)
            {
                if (intRoundedCblTemp == Int32.Parse(Project_Info.K_Value_Database[I, 0]))
                {
                    dblKValue = Double.Parse(Project_Info.K_Value_Database[I, (1 + intCbleCondMaterialCol + intCbleTempLimitCol)]);
                }
            }
            return dblKValue;
        }

        private double Calc_CableSCWithstand()
        {
            //
            // SC withstand calc
            //
            double K2S2 = (K_Value * K_Value) * (Sp_Active * Sp_Active);
            return K2S2;
        }

        #endregion
    }
}
