﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaTransactionsNamesManager;
using MEP.PowerBIM_1._5.UI.UiData;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using Common.EnhancedADGV.EnhancementClasses;
using MEP.PowerBIM_5.CoreLogic;
using System.Reflection;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_DbEdit : BecaBaseForm
    {
        #region Fields
        // For Modeless
        RequestHandler _handler;
        ExternalEvent _externalEvent;

        //
        private PowerBIM_ProjectInfo projInfo;
        private Autodesk.Revit.DB.Document rqSubDoc = null;
        public IList<PowerBIM_DBData> subDBEditSel = null;

        private List<PowerBIM_DBData> DBs = new List<PowerBIM_DBData>();

        bool _isloading;
        bool _isValuesUnsaved;
        bool _requestToCloseForm;
        int _pathModeCellNum = 17;

        #endregion

        #region Constructor
        public frmPowerBIM_DbEdit(ExternalEvent exEvent, RequestHandler handler, PowerBIM_ProjectInfo pi, IList<PowerBIM_DBData> subDBEdit)
        {
            _isloading = true;
            _isValuesUnsaved = false;
            _requestToCloseForm = false;

            InitializeComponent();

            // For Modeless
            _handler = handler;
            _externalEvent = exEvent;

            projInfo = pi;
            subDBEditSel = subDBEdit;
            rqSubDoc = pi.Document;

        }

        #endregion

        #region Methods

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.rqEditDBClose.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        #region UI

        #region Helpers

        private void DGV_ColourRows()
        {
            for (int i = 0; i < dgvDBData.Rows.Count; i++)
            {
                var row = dgvDBData.Rows[i];
                var rowBoundedObject = subDBEditSel[i];
                if (rowBoundedObject.Data_Good == true)
                {
                    row.Cells[0].Style.BackColor = System.Drawing.Color.LightGreen;
                    row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkGreen;
                }
                else
                {
                    row.Cells[0].Style.BackColor = System.Drawing.Color.PaleVioletRed;
                    row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkRed;
                }
            }

            #region Coloring Headers

            dgvDBData.Columns[1].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[2].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[9].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[10].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[11].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[12].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);
            dgvDBData.Columns[13].HeaderCell.Style.BackColor = System.Drawing.Color.FromArgb(141, 14, 132);

            #endregion

            dgvDBData.Refresh();
        }

        private void SetCellsWhenIsManual(DataGridViewRow row)
        {
            // Disable and greyed out path mode 
            row.Cells[_pathModeCellNum].ReadOnly = true;
            row.Cells[_pathModeCellNum].Style.ForeColor = System.Drawing.Color.White;
            row.Cells[_pathModeCellNum].Style.BackColor = System.Drawing.Color.LightGray;
            // Disable and greyed out set button 
            row.Cells[_pathModeCellNum + 1].ReadOnly = true;
            // Enable user input total length
            row.Cells[_pathModeCellNum + 2].ReadOnly = false;
            row.Cells[_pathModeCellNum + 2].Style.BackColor = System.Drawing.Color.White;
        }

        private void SetCellsWhenIsNotManual(DataGridViewRow row)
        {
            // Enable path mode
            row.Cells[_pathModeCellNum].ReadOnly = false;
            row.Cells[_pathModeCellNum].Style.ForeColor = System.Drawing.Color.Green;
            row.Cells[_pathModeCellNum].Style.BackColor = System.Drawing.Color.White;
            // Enable path mode
            row.Cells[_pathModeCellNum + 1].ReadOnly = false;
            // Disable user input and greyed out  total length
            row.Cells[_pathModeCellNum + 2].ReadOnly = true;
            row.Cells[_pathModeCellNum + 2].Style.BackColor = System.Drawing.Color.LightGray;
        }

        #endregion

        private void frmPowerBIM_DbEdit_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_isValuesUnsaved)
            {
                DialogResult result = MessageBox.Show("You have unsaved changes. Would you like to save those changes?", "Warning", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    SaveDataChanges();
                    _requestToCloseForm = true;
                    e.Cancel = true;
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                }
            }
        }

        private void frmPowerBIM_DbEdit_FormClosed(object sender, FormClosedEventArgs e)
        {

            ModelessPowerBIM_StartFormHandler.BringFormsTofront();
        }

        private void frmPowerBIM_DbEdit_Load_1(object sender, EventArgs e)
        {
            dgvDBData.SetDoubleBuffered();

            //Populate new DB data class for Datagrid viewer
            for (int i = 0; i < subDBEditSel.Count; i++)
            {
                AddDBInfo(subDBEditSel[i]);
                UpdatePathModeDatasource(i);
            }

            SetCircuitLengthManualInForm();

            //var enums = Enum.GetValues(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)).Cast<Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode>();
            //circuitPathModeDataGridViewTextBoxColumn.DataSource = enums;
            DGV_ColourRows();
            _isloading = false;

        }

        private void UpdatePathModeDatasource(int rowIndex)
        {
            var dropdownPathMode = dgvDBData.Rows[rowIndex].Cells[_pathModeCellNum] as DataGridViewComboBoxCell;
            dropdownPathMode.DataSource = SetPathModeEnumRows(subDBEditSel.ElementAt(rowIndex));
        }

        private List<string> SetPathModeEnumRows(PowerBIM_DBData dbData)
        {
            var ecpList = new List<string>();
            var enums = Enum.GetValues(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)).Cast<Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode>();
            if (!dbData.HasCustomCircuitPath)
                enums.Where(x => x.ToString() != "Custom").ToList().ForEach(y => ecpList.Add(y.ToString()));
            else
                enums.ToList().ForEach(y => ecpList.Add(y.ToString()));
            return ecpList;
        }

        private void SetCircuitLengthManualInForm()
        {
            for (int i = 0; i < subDBEditSel.Count; i++)
            {
                var checkBoxCell = dgvDBData.Rows[i].Cells[_pathModeCellNum - 1] as DataGridViewCheckBoxCell;
                var paramManual = subDBEditSel.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual;
                if (paramManual == null)
                    return;

                if (!paramManual.HasValue)
                    SetCellsWhenIsNotManual(dgvDBData.Rows[i]);

                if (subDBEditSel.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 1)
                {
                    checkBoxCell.Value = checkBoxCell.TrueValue;
                    SetCellsWhenIsManual(dgvDBData.Rows[i]);
                }
                else if (subDBEditSel.ElementAt(i).paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 0)
                {
                    checkBoxCell.Value = checkBoxCell.FalseValue;
                    SetCellsWhenIsNotManual(dgvDBData.Rows[i]);
                }
            }
        }

        void AddDBInfo(PowerBIM_DBData DB)
        {
            DataRow dr = dBInfo.Tables[0].NewRow();
            dr.ItemArray = new object[]
            {
                DB.Schedule_DB_Name                                     ,
                DB.Schedule_Main_Switch_Rating                          ,
                DB.Schedule_Feeder_Cable                                ,
                DB.Schedule_Location                                    ,
                DB.Schedule_Seismic_Category                            ,
                DB.Schedule_Form_Rating                                 ,
                DB.Schedule_Bus_Fault_Level                             ,
                DB.Schedule_Surge_Protection                            ,
                DB.Schedule_Metering                                    ,
                DB.Upstream_Device_Rating                               ,
                Math.Round( DB.EFLI_R,8)                                               ,
                Math.Round(DB.EFLI_X ,8  )                                             ,
                Math.Round(DB.DBVD *100  ,4  )                                             ,
                Math.Round(DB.PSCC     ,8     )                                        ,
                DB.Schedule_Device_Fault_Rating                         ,
                Math.Round(DB.Schedule_Final_Circuit_MaxVD *100,4 )                        ,
                DB.DB_IsManual()                                        ,
                DB.LengthClass.Circuit_Path_Mode                        ,
                Math.Round(DB.LengthClass.Length_Total / 1000, 1)
            };
            dBInfo.Tables[0].Rows.Add(dr);

        }

        #endregion

        #endregion

        #region Button Clicks

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            if (Environment.UserDomainName == "BECAMAIL")
            {
                using (frmPowerBIM_Help rqFrmHelp = new frmPowerBIM_Help())
                {
                    rqFrmHelp.ShowDialog();
                }
            }
            else
            {
                using (var bedarHelpFrm = new frmBedarPowerBIM_Help())
                {
                    bedarHelpFrm.ShowDialog();
                }
            }
        }

        private void frmEditDBSave_Click(object sender, EventArgs e)
        {
            //DBEdit_SubmitData();
            SaveDataChanges();

            //this.Close();
        }

        void SaveDataChanges()
        {
            MakeRequest(RequestId.DBEdit_SubmitData);
            _isValuesUnsaved = false;
        }

        private void frmEditDBClose_Click(object sender, EventArgs e)
        {
            this.Close();

        }

        private void rqOpenPowerCADcsv_Click(object sender, EventArgs e)
        {
            List<dgvDBImportData> dbData = subDBEditSel.Select(subDBSel => new dgvDBImportData(subDBSel)).ToList();

            frmPowerBIM_DbEdit_ImportSettings frmPCImport = new frmPowerBIM_DbEdit_ImportSettings(dbData, projInfo, subDBEditSel);
            frmPCImport.ShowDialog();
            List<dgvDBImportDataStatus> importResult = new List<dgvDBImportDataStatus>();
            if (frmPCImport.DialogResult == DialogResult.OK)
            {
                for (int i = 0; i < dgvDBData.Rows.Count; i++)
                {
                    if (frmPCImport.MatchRowIndex.TryGetValue(dgvDBData.Rows[i].Cells[0].Value.ToString(), out int csvRow))
                    {
                        importResult.Add(frmPCImport.WriteBackToDGV(i, csvRow));
                    }
                }
            }

            #region Mark  import data

            foreach (var r in importResult)
            {
                // FEEDER CABLE
                dgvDBData.Rows[r.dgvRowNumber].Cells[2].Value = dbData[r.dgvRowNumber].Feeder_Cable;
                if (!r.Main_Switch_Rating)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[2].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[2].Style.BackColor = System.Drawing.Color.LightGreen;
                }

                // MAIN SWITCH 
                dgvDBData.Rows[r.dgvRowNumber].Cells[1].Value = dbData[r.dgvRowNumber].Main_Switch_Rating;
                if (!r.Main_Switch_Rating)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[1].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[1].Style.BackColor = System.Drawing.Color.LightGreen;
                }

                // UPSTREAM DEVICE
                dgvDBData.Rows[r.dgvRowNumber].Cells[9].Value = dbData[r.dgvRowNumber].Upstream_Device_Rating;
                if (!r.Upstream_Device_Rating)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[9].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[9].Style.BackColor = System.Drawing.Color.LightGreen;
                }

                // EFLI_R
                dgvDBData.Rows[r.dgvRowNumber].Cells[10].Value = dbData[r.dgvRowNumber].EFLI_R;
                if (!r.EFLI_R)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[10].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[10].Style.BackColor = System.Drawing.Color.LightGreen;

                }

                // EFLI_X
                dgvDBData.Rows[r.dgvRowNumber].Cells[11].Value = dbData[r.dgvRowNumber].EFLI_X;
                if (!r.EFLI_X)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[11].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[11].Style.BackColor = System.Drawing.Color.LightGreen;

                }

                // DB VD
                dgvDBData.Rows[r.dgvRowNumber].Cells[12].Value = dbData[r.dgvRowNumber].DBVD * 100;
                if (!r.DBVD)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[12].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[12].Style.BackColor = System.Drawing.Color.LightGreen;

                }

                // PSCC
                dgvDBData.Rows[r.dgvRowNumber].Cells[13].Value = dbData[r.dgvRowNumber].PSCC;
                if (!r.PSCC)
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[13].Style.BackColor = System.Drawing.Color.Red;
                }
                else
                {
                    dgvDBData.Rows[r.dgvRowNumber].Cells[13].Style.BackColor = System.Drawing.Color.LightGreen;

                }


            }

            #endregion
        }

        private void btn_ActivateEditPathView_Click(object sender, EventArgs e)
        {
            MakeRequest(RequestId.ActivateEdithPathView);
        }

        #endregion

        #region DGV

        private void dgvDBData_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == _pathModeCellNum + 1)
            {
                if (e.RowIndex >= 0)
                {
                    if (dgvDBData.Rows[e.RowIndex].Cells[_pathModeCellNum - 1].Value.ToString() == "True")
                        return;
                    MakeRequest(RequestId.OpenPathCustomisingViewForDB);
                }
            }





        }

        private void dgvDBData_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            if (e.Exception.Message == "DataGridViewComboBoxCell value is not valid.")
            {
                var dgvFrmCircuitData = sender as DataGridView;
                object value = dgvFrmCircuitData[e.ColumnIndex, e.RowIndex].Value;
                if (!dgvFrmCircuitData.Columns[e.ColumnIndex].Name.Contains(value.ToString()))
                {
                    dgvFrmCircuitData.Columns[e.ColumnIndex].Name = value.ToString();
                    e.ThrowException = false;
                }
            }
        }

        #endregion

        #region unused methods
        //private void DBEdit_SubmitData()
        //{
        //    if (blFouldata == false)
        //    {
        //        int i = 0;
        //        foreach (PowerBIM_DBData DB in subDBEditSel)
        //        {
        //            // Push back the updated DB data into the DB class

        //            DB.Schedule_DB_Name = dbData[i].DB_Name;
        //            DB.Schedule_Main_Switch_Rating = dbData[i].Main_Switch_Rating;
        //            DB.Schedule_Feeder_Cable = dbData[i].Feeder_Cable;
        //            DB.Schedule_Location = dbData[i].Location;
        //            DB.Schedule_Seismic_Category = dbData[i].Seismic_Category;
        //            DB.Schedule_Form_Rating = dbData[i].Form_Rating;
        //            DB.Schedule_Bus_Fault_Level = dbData[i].Bus_Fault_Level;
        //            DB.Schedule_Surge_Protection = dbData[i].Surge_Protection;
        //            DB.Schedule_Metering = dbData[i].Metering;
        //            DB.Upstream_Device_Rating = dbData[i].Upstream_Device_Rating;
        //            DB.EFLI_R = dbData[i].EFLI_R;
        //            DB.EFLI_X = dbData[i].EFLI_X;
        //            DB.DBVD = dbData[i].DBVD;
        //            DB.PSCC = dbData[i].PSCC;
        //            DB.Schedule_Device_Fault_Rating = dbData[i].Device_Fault_Rating;
        //            DB.Schedule_Final_Circuit_MaxVD = dbData[i].Final_Circuit_MaxVD;

        //            DB.Commit_DBHeaderData();
        //            i++;


        //        }
        //        TaskDialog.Show("Success", (i) + "DBs successfully updated");
        //    }
        //    else
        //    {
        //        TaskDialog.Show("Fail", "Data bad, unable to save");
        //    }

        //}
        //private void Format_NewDataRows(int intGridRow)
        //{
        //    dgvDBData.Rows[intGridRow].Cells[1].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[2].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[9].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[10].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[11].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[12].Style.ForeColor = System.Drawing.Color.Blue;
        //    dgvDBData.Rows[intGridRow].Cells[13].Style.ForeColor = System.Drawing.Color.Blue;
        //}
        //private void dgvDBData_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        //{
        //    int i = e.RowIndex;
        //    if (i >= 0) // valid index sleected
        //    {
        //        PowerBIM_DBData DB = subDBEditSel[i];

        //        // write back new values
        //        DB.Schedule_DB_Name = dbData[i].DB_Name;
        //        DB.Schedule_Main_Switch_Rating = dbData[i].Main_Switch_Rating;
        //        DB.Schedule_Feeder_Cable = dbData[i].Feeder_Cable;
        //        DB.Schedule_Location = dbData[i].Location;
        //        DB.Schedule_Seismic_Category = dbData[i].Seismic_Category;
        //        DB.Schedule_Form_Rating = dbData[i].Form_Rating;
        //        DB.Schedule_Bus_Fault_Level = dbData[i].Bus_Fault_Level;
        //        DB.Schedule_Surge_Protection = dbData[i].Surge_Protection;
        //        DB.Schedule_Metering = dbData[i].Metering;
        //        DB.Upstream_Device_Rating = dbData[i].Upstream_Device_Rating;
        //        DB.EFLI_R = dbData[i].EFLI_R;
        //        DB.EFLI_X = dbData[i].EFLI_X;
        //        DB.DBVD = dbData[i].DBVD;
        //        DB.PSCC = dbData[i].PSCC;
        //        DB.Schedule_Device_Fault_Rating = dbData[i].Device_Fault_Rating;
        //        DB.Schedule_Final_Circuit_MaxVD = dbData[i].Final_Circuit_MaxVD;

        //        // Check new DB data
        //        DB.Check_DB();

        //        //Update Data Good param
        //        dbData[i].Data_Good = DB.Data_Good;

        //        // Colour rows
        //        DGV_ColourRows();
        //    }
        //}

        //private void frmPowerBIM_DbEdit_Load(object sender, EventArgs e)
        //{
        //    DGV_ColourRows();
        //}

        #endregion

        internal bool OpenPathCustomisingView()
        {
            var selOBJ = subDBEditSel[dgvDBData.SelectedCells[0].OwningRow.Index];
            return selOBJ.OpenPathCustomisingView();

        }

        internal void EnableEditDBPath(UIDocument uiDoc)
        {
            uiDoc.Selection.SetElementIds(new List<ElementId>(0));//clear any previous selection
            uiDoc.Selection.SetElementIds(new List<ElementId>(1) { subDBEditSel[dgvDBData.SelectedCells[0].OwningRow.Index].DB_Element.Id });//select element
        }

        internal void DBEdit_SubmitData()
        {
            string errors = string.Empty;

            for (int i = 0; i < subDBEditSel.Count; i++)
            {
                var row = dgvDBData.Rows[i];
                var DB = subDBEditSel[i];

                // Push back the updated DB data into the DB class
                DB.Schedule_DB_Name = row.Cells[0].Value.ToString();
                DB.Schedule_Main_Switch_Rating = double.Parse(row.Cells[1].Value.ToString());
                DB.Schedule_Feeder_Cable = row.Cells[2].Value.ToString();
                DB.Schedule_Location = row.Cells[3].Value.ToString();
                DB.Schedule_Seismic_Category = row.Cells[4].Value.ToString();
                DB.Schedule_Form_Rating = row.Cells[5].Value.ToString();
                DB.Schedule_Bus_Fault_Level = row.Cells[6].Value.ToString();
                DB.Schedule_Surge_Protection = row.Cells[7].Value.ToString();
                DB.Schedule_Metering = row.Cells[8].Value.ToString();
                DB.Upstream_Device_Rating = double.Parse(row.Cells[9].Value.ToString());
                DB.EFLI_R = double.Parse(row.Cells[10].Value.ToString());
                DB.EFLI_X = double.Parse(row.Cells[11].Value.ToString());
                DB.DBVD = double.Parse(row.Cells[12].Value.ToString()) / 100;
                DB.PSCC = double.Parse(row.Cells[13].Value.ToString());
                DB.Schedule_Device_Fault_Rating = row.Cells[14].Value.ToString();
                DB.Schedule_Final_Circuit_MaxVD = double.Parse(row.Cells[15].Value.ToString()) / 100;

                DB.Commit_ManualValue(bool.Parse(row.Cells[16].Value.ToString()));

                DB.Commit_DBHeaderData();

                DB.Check_DB();

                StringBuilder sb;
                using (Transaction tx = new Transaction(projInfo.Document, "pb Submit data"))
                {
                    tx.Start();
                    DB.Commit_CircuitData(out sb);
                    if (sb.Length > 0)
                        errors += sb;
                    DB.Run_DBCheckAllCircuits(out string eerorsb);
                    tx.Commit();
                }
            }

            UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Success", (subDBEditSel.Count) + "DBs successfully updated");

            if (!string.IsNullOrEmpty(errors))
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "These circuits cannot be commited:\n\n" + errors);

            if (_requestToCloseForm)
            {
                this.Close();
            }
        }

        internal PowerBIM_DBData DBEdit_UpdateDataTemporary(DataGridViewRow row, PowerBIM_DBData DB)
        {
            // Create a new instance of the DB object using reflection
            PowerBIM_DBData DB_Cloned = (PowerBIM_DBData)Activator.CreateInstance(DB.GetType(), projInfo, DB.DB_Element);

            // Get all the properties of the DB object and copy them to DB_Copy
            foreach (PropertyInfo property in DB.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                if (property.CanWrite)  // Only copy properties that can be written to
                {
                    property.SetValue(DB_Cloned, property.GetValue(DB, null), null);
                }
            }

            // Copy fields (if there are any public/protected fields) using reflection
            foreach (FieldInfo field in DB.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance))
            {
                field.SetValue(DB_Cloned, field.GetValue(DB));
            }

            // Push back the updated DB data into the DB class
            DB_Cloned.Schedule_DB_Name = row.Cells[0].Value.ToString();
            DB_Cloned.Schedule_Main_Switch_Rating = double.Parse(row.Cells[1].Value.ToString());
            DB_Cloned.Schedule_Feeder_Cable = row.Cells[2].Value.ToString();
            DB_Cloned.Schedule_Location = row.Cells[3].Value.ToString();
            DB_Cloned.Schedule_Seismic_Category = row.Cells[4].Value.ToString();
            DB_Cloned.Schedule_Form_Rating = row.Cells[5].Value.ToString();
            DB_Cloned.Schedule_Bus_Fault_Level = row.Cells[6].Value.ToString();
            DB_Cloned.Schedule_Surge_Protection = row.Cells[7].Value.ToString();
            DB_Cloned.Schedule_Metering = row.Cells[8].Value.ToString();
            DB_Cloned.Upstream_Device_Rating = double.Parse(row.Cells[9].Value.ToString());
            DB_Cloned.EFLI_R = double.Parse(row.Cells[10].Value.ToString());
            DB_Cloned.EFLI_X = double.Parse(row.Cells[11].Value.ToString());
            DB_Cloned.DBVD = double.Parse(row.Cells[12].Value.ToString()) / 100;
            DB_Cloned.PSCC = double.Parse(row.Cells[13].Value.ToString());
            DB_Cloned.Schedule_Device_Fault_Rating = row.Cells[14].Value.ToString();
            DB_Cloned.Schedule_Final_Circuit_MaxVD = double.Parse(row.Cells[15].Value.ToString()) / 100;

            DB_Cloned.Check_DB();

            return DB_Cloned;
        }

        internal void RecalcAndRefreshLengthToForm()
        {
            using (var trans = new Transaction(projInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans.Start();
                var corspondeingRow = (dgvDBData.SelectedCells[0].OwningRow.DataBoundItem as DataRowView).Row;
                RecalcAndRefreshLengthToFormClick(subDBEditSel[dgvDBData.SelectedCells[0].OwningRow.Index], corspondeingRow);
                trans.Commit();
            }
        }

        public void RecalcAndRefreshLengthToFormClick(PowerBIM_DBData CCT, DataRow row)
        {
            UpdatePathModeDatasource(dgvDBData.CurrentRow.Index);
            //Get latest path mode selection from revit and update GUI
            CCT.readCircuitPathMode();
            row.SetField<string>("Circuit_Path_Mode", CCT.LengthClass.Circuit_Path_Mode);

            //recalculate all the numbers
            // Flag CCT that path mode has changed from the form
            //CCT.PathmodeChangedInEditCircuit = true;
            //CCT.Refresh_DerrivedCircuitProperties();

            //run checks
            //RunPowerBIMCheck(CCT);

            //finally put values from circuit into table
            row.SetField<string>("Length_Total", Math.Round(CCT.LengthClass.Length_Total / 1000, 1).ToString());
        }

        private void dgvDBData_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (!_isloading)
            {
                var row = dgvDBData.Rows[e.RowIndex];

                if (e.ColumnIndex == dgvDBData.Columns["manualDataGridViewCheckBoxColumn"].Index)
                {
                    // If Manual checkbox changes
                    if (e.ColumnIndex == _pathModeCellNum - 1 && dgvDBData.Columns[e.ColumnIndex] is DataGridViewCheckBoxColumn && e.RowIndex >= 0)
                    {
                        MakeRequest(RequestId.SetDbLengthManual);
                    }

                    // Manual checkbox
                    if (dgvDBData.Columns[e.ColumnIndex] is DataGridViewCheckBoxColumn && e.RowIndex >= 0)
                    {
                        if (Convert.IsDBNull(row.Cells[_pathModeCellNum - 1].Value))
                            return;

                        if (Convert.ToBoolean(row.Cells[_pathModeCellNum - 1].Value))
                            SetCellsWhenIsManual(row);
                        else
                            SetCellsWhenIsNotManual(row);

                    }
                }

                _isValuesUnsaved = true;

                // Check DB, update coloring if needed
                var db = subDBEditSel.ToList().Find(d => d.DB_Element.Name == dgvDBData[0, e.RowIndex].Value.ToString());
                db?.Check_DB();

                var db_Cloned = DBEdit_UpdateDataTemporary(row, db);

                if (db_Cloned?.Data_Good == true)
                {
                    row.Cells[0].Style.BackColor = System.Drawing.Color.LightGreen;
                    row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkGreen;
                }
                else
                {
                    row.Cells[0].Style.BackColor = System.Drawing.Color.PaleVioletRed;
                    row.DefaultCellStyle.ForeColor = System.Drawing.Color.DarkRed;
                }
            }

        }

        /// <summary>
        /// This is used to trigger manual check box
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgvDBData_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            if (dgvDBData.IsCurrentCellDirty && dgvDBData.CurrentCell.ColumnIndex == dgvDBData.Columns["manualDataGridViewCheckBoxColumn"].Index)
            {
                dgvDBData.EndEdit();
            }
        }

        internal void SetCircuitLengthManual()
        {
            var corrspondingObj = subDBEditSel[dgvDBData.SelectedCells[0].RowIndex];
            SetCircuitLengthManualLogic(corrspondingObj, dgvDBData.SelectedCells[0].OwningRow.Cells[_pathModeCellNum - 1]);
        }

        void SetCircuitLengthManualLogic(PowerBIM_DBData CCT, DataGridViewCell cell)
        {
            if (Convert.ToBoolean(cell.Value))
            {
                using (var trans = new Transaction(projInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
                {
                    trans.Start();
                    CCT.paramPBLength_Beca_Circuit_Length_Manual.Set(1);

                    trans.Commit();
                }
            }
            else
            {
                using (var trans = new Transaction(projInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
                {
                    trans.Start();
                    CCT.paramPBLength_Beca_Circuit_Length_Manual.Set(0);
                    trans.Commit();
                }
                RecalcAndRefreshLengthToForm();
            }

        }

        internal void RequestRecalcAndRefreshLengthToForm()
        {
            MakeRequest(RequestId.RecalcAndRefreshLengthToDbForm);
        }


    }

    #region Modeless Form Handler 

    public class ModelessPowerBIM_DbEditFormHandler
    {
        #region Fields

        public static frmPowerBIM_DbEdit DbEditForm;

        #endregion

        #region Properties


        #endregion

        #region Methods
        public static void DBEdit_SubmitData()
        {
            DbEditForm.DBEdit_SubmitData();
            DbEditForm.Activate();
        }


        public static bool OpenPathCustomisingView()
        {
            return DbEditForm.OpenPathCustomisingView();

        }

        public static void EnableEditDBPath(UIDocument uiDoc)
        {
            DbEditForm.EnableEditDBPath(uiDoc);
        }

        public static void WakeFormUpDbEdit()
        {
            if (DbEditForm != null)
            {
                DbEditForm.WakeUp();
            }
        }

        public static void BringFormsTofront()
        {
            if (DbEditForm != null)
            {
                DbEditForm.TopLevel = true;
                DbEditForm.TopMost = true;

            }
        }
        public static void BringFormsToBack()
        {
            if (DbEditForm != null)
            {
                DbEditForm.TopLevel = false;
            }
        }

        internal static void RequestRecalcAndRefreshLengthToForm()
        {
            DbEditForm.RequestRecalcAndRefreshLengthToForm();
        }
        internal static void RecalcAndRefreshLengthToDbForm()
        {
            DbEditForm.RecalcAndRefreshLengthToForm();
        }




        internal static void SetCircuitLengthManual()
        {
            DbEditForm.SetCircuitLengthManual();
        }


        #endregion

    }

    #endregion
}
