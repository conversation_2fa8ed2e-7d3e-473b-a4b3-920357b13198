﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB.Electrical;
using BecaTransactionsNamesManager;
using BecaRevitUtilities;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_CircuitData
    {
        public PowerBIM_ProjectInfo projInfo { get; set; }
        public PowerBIM_DBData DB { get; set; }
        public Element CCT_Element { get; set; }
        public ElectricalSystem CCT_Electrical_System { get; set; }

        public bool CircuitLengthIsManual { get; set; }

        // 2 Cables, 1 Length and 1 Breaker per Circuit
        public PowerBIM_CableData Cable_To_First { get; set; }
        public PowerBIM_CableData Cable_To_Final { get; set; }
        public PowerBIM_BreakerData Breaker { get; set; }
        public PowerBIM_CircuitRoute LengthClass { get; set; }


        public string CCT_Number { get; set; }
        public string Schedule_Description { get; set; }
        public double Revit_Current { private get; set; }
        public bool ManualCurrent { get; set; }
        public double Manual_PowerBim_User_Current { get; set; }
        public double CCT_PowerBIM_Current { get; set; }
        public int Number_Of_Poles { get; set; } // either 1 (single phase ) or 3 (3 phase)
        public int Number_Of_Elements { get; set; }

        public string Schedule_Cable_To_First { get; set; }
        public string Schedule_Cable_To_Final { get; set; }


        public string Schedule_RCD { get; set; }
        public string Schedule_Other_Controls { get; set; }
        public string Schedule_Install_Method { get; set; }
        public int Install_Method_Index { get; set; }
        public double Schedule_Derating_Factor { get; set; }
        public double CCT_Diversity { get; set; }
        public string Schedule_Revision { get; set; }
        public double CCT_Current_Phase_A { get; set; }
        public double CCT_Current_Phase_B { get; set; }
        public double CCT_Current_Phase_C { get; set; }
        public double CCT_Diversified_Current_Phase_A { get; set; }
        public double CCT_Diversified_Current_Phase_B { get; set; }
        public double CCT_Diversified_Current_Phase_C { get; set; } 

        public double CCT_Undiversified_Current_Phase_A { get; set; }
        public double CCT_Undiversified_Current_Phase_B { get; set; }
        public double CCT_Undiversified_Current_Phase_C { get; set; }


        public bool CCT_Is_Spare_Or_Space { get; set; }
        public bool CCT_Is_Power { get; set; }
        public bool CCT_Is_Lighting { get; set; }
        public bool CCT_GPO_Present { get; set; }
        public bool CCT_RCD_ElementIsPresent { get; set; }
        public string CCT_RCD_Name { get; set; }
        public int GPO_Count { get; set; }
        public double CCT_Clearing_Time { get; set; }
        public bool Data_Good { get; set; }
        public string Error_Message { get; set; }
        public bool Parameters_Good { get; set; }
        public bool Values_Missing { get; set; }

        // Calculation Results
        public double CalcRes_Final_Circuit_VD { get; set; }
        public double CalcRes_Final_Circuit_VD_Percentage { get; set; }
        public double CalcRes_Total_EFLi { get; set; }


        //add warnings
        public bool blWarn_UserDefCableSelected { get; set; }
        public bool blWarn_EmLightingPresent { get; set; }
        public bool blWarn_DeratingAbove1 { get; set; }
        public bool blWarn_AlternatekARatingSelected { get; set; }
        public bool blWarn_IsRCD { get; set; }
        public bool blWarn_DashedFinalCableSelection { get; set; }

        // Locked circuit
        public bool IsLocked { get; set; }
        public string LockedOwnerName { get; set; }
        public bool PathmodeChangedInEditCircuit { get; set; }
        public bool IsManuallyLocked { get; set; }


        //Initialise circuit check result parameters
        public string Schedule_CCTCheck_1_Data { get; set; }
        public string Schedule_CCTCheck_2_CableToFirst { get; set; }
        public string Schedule_CCTCheck_3_CableToFinal { get; set; }
        public string Schedule_CCTCheck_4_Discrimination { get; set; }
        public string Schedule_CCTCheck_5_BreakerCurrent { get; set; }
        public string Schedule_CCTCheck_6_CableToFirstCurrent { get; set; }
        public string Schedule_CCTCheck_7_CableToFinalCurrent { get; set; }
        public string Schedule_CCTCheck_8_EFLI { get; set; }
        public string Schedule_CCTCheck_9_FinalCircuitVD { get; set; }
        public string Schedule_CCTCheck_10_SystemVD { get; set; }
        public string Schedule_CCTCheck_11_CableToFirstSC { get; set; }
        public string Schedule_CCTCheck_12_CableToFinalSC { get; set; }
        public string Schedule_CCTCheck_OK { get; set; }
        public string Schedule_CCTCheck_Summary { get; set; }

        public List<string> BadCircuits;

        // Internal handlers
        public bool Check_Pass { get; set; }
        public string Check_ErrorMessage { get; set; }
        public int Warning_Count { get; set; }

        // Check Custom Path
        public bool HasCustomCircuitPath { get { return CCT_Electrical_System.HasCustomCircuitPath; } }

        #region Parameters
        //Get Circuit Parameters
        Parameter paramCircuitNumber;
        Parameter paramRevitCircuitCurrent;
        Parameter paramRevitCircuitManualCurrent;
        Parameter paramRevitCircuitManualCurrentValue;

        Parameter paramNumOfPoles;
        Parameter paramNumOfElements;
        Parameter paramOtherControls;
        Parameter paramRCDprotection;
        Parameter paramCbl_1stElem;
        Parameter paramCbl_RemainderElem;
        Parameter paramLen_1stElem;
        Parameter paramLen_Total;
        Parameter paramInstallMethod;
        Parameter paramDeratingFactor;
        Parameter paramDiversity;
        Parameter paramCircuitRevision;
        Parameter paramChkCCT_1_Data;
        Parameter paramChkCCT_2_CBL1;
        Parameter paramChkCCT_3_CBLRM;
        Parameter paramChkCCT_4_Discrimination;
        Parameter paramChkCCT_5_Current2;
        Parameter paramChkCCT_6_Current3;
        Parameter paramChkCCT_7_Current4;
        Parameter paramChkCCT_8_EFLI;
        Parameter paramChkCCT_9_VD1;
        Parameter paramChkCCT_10_VD2;
        Parameter paramChkCCT_11_SC1;
        Parameter paramChkCCT_12_SC2;
        Parameter paramChkCCT_ResultDescription;
        Parameter paramChkCCT_ResultOK;
        Parameter paramRevitCircuitCurrentPhaseA;
        Parameter paramRevitCircuitCurrentPhaseB;
        Parameter paramRevitCircuitCurrentPhaseC;
        Parameter paramLoadDescription;

        public Parameter paramPBLength_Beca_Circuit_Length_Manual;
        #endregion

        public PowerBIM_CircuitData(PowerBIM_ProjectInfo pi, PowerBIM_DBData db, ElectricalSystem elecSys)
        {
            //
            // PowerBIM_CCTData
            //
            // Constructor populating data from the passed DB
            //

            //Get DB Parameter Values: 9 parameters
            CCT_Electrical_System = elecSys;

            // Pass project info into circuit instance 
            projInfo = pi;

            // pass DB into circuit instnace
            DB = db;

            //
            // EVERY CIRCUIT HAS 2 CABLES, AND 1 BREAKER 
            //
            try
            {
                Data_Good = true;

                // Create first cable
                Cable_To_First = new PowerBIM_CableData(projInfo, this, true);

                // Create 2nd cable
                Cable_To_Final = new PowerBIM_CableData(projInfo, this, false);

                // Add Breaker Data
                Breaker = new PowerBIM_BreakerData(projInfo, elecSys, db);

                //create length class
                LengthClass = new PowerBIM_CircuitRoute(projInfo, this);
            }
            catch
            {
                Data_Good = false;
            }

            IsLocked = false;
            PathmodeChangedInEditCircuit = false;
            IsManuallyLocked = elecSys.LookupParameter("Beca_PB_IsLocked")?.AsInteger() == 1;

        }


        public double GetCurrent()
        {
            if (ManualCurrent)
            {
                return Manual_PowerBim_User_Current;
            }
            else
            {
                return CCT_PowerBIM_Current;
            }
        }
        internal void SetCurrent()
        {
            if (paramRevitCircuitManualCurrent != null)
            {
                paramRevitCircuitManualCurrent.Set(ManualCurrent ? 1 : 0);

            }

            if (paramRevitCircuitManualCurrentValue != null)
            {
                paramRevitCircuitManualCurrentValue.Set(Manual_PowerBim_User_Current);

            }
        }

        public bool SetCablesToReferToSchedule()
        {
            //Cable to First Reset
            this.Cable_To_First.Cable_Name = "REFER TO SCHEDULE";
            this.Cable_To_First.Sp_Active = 0;
            this.Cable_To_First.Sp_Earth = 0;
            this.Cable_To_First.R_Rated_Active = 0;
            this.Cable_To_First.R_Rated_Earth = 0;
            this.Cable_To_First.R_Operating_Active = 0;
            this.Cable_To_First.R_Operating_Earth = 0;
            this.Cable_To_First.X_Max_Active = 0;
            this.Cable_To_First.X_Max_Earth = 0;
            this.Cable_To_First.Z_Operating_Active = 0;
            this.Cable_To_First.Z_Operating_Earth = 0;
            this.Cable_To_First.I_Rated = 0;
            this.Cable_To_First.Temperature = 0;
            this.Cable_To_First.K_Value = 0;
            this.Cable_To_First.I2t_Max = 0;
            this.Cable_To_First.Conductor_Material = string.Empty;
            this.Cable_To_First.Insulation_Material = string.Empty;
            this.Cable_To_First.Cable_Temperature_Limit = 0;


            //Cable to Final Reset
            this.Cable_To_Final.Cable_Name = "-";
            this.Cable_To_Final.Sp_Active = 0;
            this.Cable_To_Final.Sp_Earth = 0;
            this.Cable_To_Final.R_Rated_Active = 0;
            this.Cable_To_Final.R_Rated_Earth = 0;
            this.Cable_To_Final.R_Operating_Active = 0;
            this.Cable_To_Final.R_Operating_Earth = 0;
            this.Cable_To_Final.X_Max_Active = 0;
            this.Cable_To_Final.X_Max_Earth = 0;
            this.Cable_To_Final.Z_Operating_Active = 0;
            this.Cable_To_Final.Z_Operating_Earth = 0;
            this.Cable_To_Final.I_Rated = 0;
            this.Cable_To_Final.Temperature = 0;
            this.Cable_To_Final.K_Value = 0;
            this.Cable_To_Final.I2t_Max = 0;
            this.Cable_To_Final.Conductor_Material = string.Empty;
            this.Cable_To_Final.Insulation_Material = string.Empty;
            this.Cable_To_Final.Cable_Temperature_Limit = 0;


            this.Schedule_CCTCheck_Summary = "This Circuit has Skipped Calculating due to 'REFER TO SCHEDULE'";



            return true;
        }

        public void Populate_CableParameters()
        {
            //
            // CABLE TO FIRST
            //

            // Start by telling the cable to update itself
            Cable_To_First.UpdateCableData();

            // Next, check that the cable data is good.
            if (!Cable_To_First.Data_Good)
            {
                Schedule_CCTCheck_Summary += Cable_To_First.Error_Message;
                //TODO log breaker error message
            }

            // Write back cable name from cable class to circuit class
            Schedule_Cable_To_First = Cable_To_First.Cable_Name;


            //
            // CABLE TO FIRST
            //

            // Start by telling the cable to update itself
            Cable_To_Final.UpdateCableData();

            // Next, check that the cable data is good.
            if (!Cable_To_Final.Data_Good)
            {
                Schedule_CCTCheck_Summary += Cable_To_Final.Error_Message;
                //TODO log breaker error message
            }

            // Write back cable name from cable class to circuit class
            Schedule_Cable_To_Final = Cable_To_Final.Cable_Name;
        }

        public void Populate_BreakerParameters()
        {
            //
            // BREAKER
            //

            // Start by telling the cable to update itself
            Breaker.UpdateBreakerData();

            // Next, check that the cable data is good.
            if (!Breaker.Data_Good)
            {
                Schedule_CCTCheck_Summary += Breaker.Error_Message;
                //TODO log breaker error message
            }
        }

        public void Get_CircuitParameters()
        {
            // Get Circuit System Parameters
            // - These are fixed values and wont change while powerBIM is running
            // - They are also system parameters and wont be unavailable when spare/space

            // set flag to good
            Parameters_Good = true;
            Values_Missing = false;

            try
            {
                // Circuit Number
                paramCircuitNumber = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_NUMBER);
                CCT_Number = paramCircuitNumber.AsString();

                // Circuit description
                paramLoadDescription = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_NAME);
                Schedule_Description = paramLoadDescription.AsString();

                // Revit current
                paramRevitCircuitCurrent = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_CURRENT_PARAM);
                Revit_Current = paramRevitCircuitCurrent.AsDouble();

                paramRevitCircuitManualCurrent = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramRevitCircuitManualCurrent);
                ManualCurrent = paramRevitCircuitManualCurrent?.AsInteger() == 1;

                paramRevitCircuitManualCurrentValue = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramRevitCircuitManualCurrentValue);
                if (paramRevitCircuitManualCurrentValue == null)
                {
                    Manual_PowerBim_User_Current = 0;

                }
                else
                {
                    Manual_PowerBim_User_Current = paramRevitCircuitManualCurrentValue.AsDouble();

                }

                // Number of poles
                paramNumOfPoles = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_WIRE_NUM_HOTS_PARAM);
                Number_Of_Poles = paramNumOfPoles.AsInteger();

                // Number of elements
                paramNumOfElements = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_SYSTEM_NUM_ELEMENTS_PARAM);
                Number_Of_Elements = paramNumOfElements.AsInteger();

                readCircuitPathMode();

            }
            catch
            {
                // We've couldn't extract data from all required parameters, mark as BAD
                Parameters_Good = false;
            }

            // Now Get Circuit Shared Paramters
            //
            //
            //
            // read parameters within try/catch loop
            //

            paramChkCCT_5_Current2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current2);
            paramChkCCT_6_Current3 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current3);
            paramChkCCT_7_Current4 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current4);
            paramChkCCT_8_EFLI = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_EFLI);
            paramChkCCT_9_VD1 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_VD1);
            paramChkCCT_10_VD2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_VD2);
            paramChkCCT_11_SC1 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_SC1);
            paramChkCCT_12_SC2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_SC2);

            paramPBLength_Beca_Circuit_Length_Manual = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidPBLength_Circuit_Length_Manual);
            if (paramPBLength_Beca_Circuit_Length_Manual != null)
            {

                CircuitLengthIsManual = paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 1;
            }
            try
            {
                // Assign Cable To First
                paramCbl_1stElem = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCbl_1stElem);

                // Check 
                if (paramCbl_1stElem == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Cable To First Circuit Element" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramCbl_1stElem.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Cable To First Circuit Element" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Cable_To_First = paramCbl_1stElem.AsString();

                // Assign Cable To Final
                paramCbl_RemainderElem = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCbl_RemainderElem);
                if (paramCbl_RemainderElem == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Cable To First Circuit Element" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramCbl_RemainderElem.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Cable To Final Circuit Element" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Cable_To_Final = paramCbl_RemainderElem.AsString();

                // Assign Length to first
                paramLen_1stElem = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidLen_1stElem);
                if (paramLen_1stElem == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Length To First Circuit Element" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramLen_1stElem.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Length to first circuit element" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                {
                    LengthClass.Length_To_First = RevitUnitConvertor.InternalToMm(paramLen_1stElem.AsDouble());
                }

                // Assign Cable To Final
                paramLen_Total = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidLen_Total);
                if (paramLen_Total == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Length To First Circuit Element" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramLen_Total.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Total circuit length" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                {

                    LengthClass.Length_Total = RevitUnitConvertor.InternalToMm(paramLen_Total.AsDouble());
                }

                // Assign RCD Protection
                paramRCDprotection = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidRCDProtection);
                if (paramRCDprotection == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "RCD Protection" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramRCDprotection.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "RCD Protection" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_RCD = paramRCDprotection.AsString();

                // Assign Other Controls Interfaces
                paramOtherControls = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidOtherControls);
                if (paramOtherControls == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Other Controls And Interfaces" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramOtherControls.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Other Controls and Interfaces" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Other_Controls = paramOtherControls.AsString();

                // Assign Installation Method
                paramInstallMethod = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCabInstall);
                if (paramInstallMethod == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Installtion Method" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramInstallMethod.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Install Method" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Install_Method = paramInstallMethod.AsString();

                // Assign Derating Factor
                paramDeratingFactor = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidDeratingFactor);
                if (paramDeratingFactor == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Derating Factor" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramDeratingFactor.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Derating Factor" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Derating_Factor = paramDeratingFactor.AsDouble();

                // Assign diversity Factor
                paramDiversity = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidDiversity);
                if (paramDiversity == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Diversity" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramDiversity.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Diversity" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    CCT_Diversity = paramDiversity.AsDouble();


                //
                // Verifcation
                //

                // Assign CCT Check 1 
                paramChkCCT_1_Data = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Data);
                if (paramChkCCT_1_Data == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 1 - Data" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_1_Data.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 1 - Data" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_1_Data = paramChkCCT_1_Data.AsString();

                // Assign CCT Check 2
                paramChkCCT_2_CBL1 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_CBL1);
                if (paramChkCCT_2_CBL1 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 2 - Cable To First" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_2_CBL1.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 2 - Cable To First" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_2_CableToFirst = paramChkCCT_2_CBL1.AsString();

                // Assign CCT Check 3
                paramChkCCT_3_CBLRM = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_CBRM);
                if (paramChkCCT_3_CBLRM == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 3 - Cable To Final" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_3_CBLRM.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 3 - Cable To Final" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_3_CableToFinal = paramChkCCT_3_CBLRM.AsString();

                // Assign CCT Check 4
                paramChkCCT_4_Discrimination = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current1);
                if (paramChkCCT_4_Discrimination == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 4 - Discrimination" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_4_Discrimination.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 4 - Discrimination" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_4_Discrimination = paramChkCCT_4_Discrimination.AsString();


                // Assign CCT Check 5
                paramChkCCT_5_Current2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current2);
                if (paramChkCCT_5_Current2 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 5 - Breaker Current" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_5_Current2.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 5 - Breaker Current" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_5_BreakerCurrent = paramChkCCT_5_Current2.AsString();

                // Assign CCT Check 6
                paramChkCCT_6_Current3 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current3);
                if (paramChkCCT_6_Current3 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 6 - Cable To First Current" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_6_Current3.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 6 - Cable To First Current" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_6_CableToFirstCurrent = paramChkCCT_6_Current3.AsString();

                // Assign CCT Check 7
                paramChkCCT_7_Current4 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_Current4);
                if (paramChkCCT_7_Current4 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 7 - Cable To Final Current" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_7_Current4.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 7 - Cable To Final Current" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_7_CableToFinalCurrent = paramChkCCT_7_Current4.AsString();

                // Assign CCT Check 8
                paramChkCCT_8_EFLI = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_EFLI);
                if (paramChkCCT_8_EFLI == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 8 - EFLI" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_8_EFLI.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 8 - EFLI" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_8_EFLI = paramChkCCT_8_EFLI.AsString();

                // Assign CCT Check 9
                paramChkCCT_9_VD1 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_VD1);
                if (paramChkCCT_9_VD1 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 9 - Final Circuit VD" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_9_VD1.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 9 - Final Circuit VD" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_9_FinalCircuitVD = paramChkCCT_9_VD1.AsString();

                // Assign CCT Check 10
                paramChkCCT_10_VD2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_VD2);
                if (paramChkCCT_10_VD2 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 10 - SystemVD" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_10_VD2.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 10 - SystemVD" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_10_SystemVD = paramChkCCT_10_VD2.AsString();

                // Assign CCT Check 11
                paramChkCCT_11_SC1 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_SC1);
                if (paramChkCCT_11_SC1 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 11 - Cable To First SC" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_11_SC1.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 11 - Cable To First SC" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_11_CableToFirstSC = paramChkCCT_11_SC1.AsString();

                // Assign CCT Check 12
                paramChkCCT_12_SC2 = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_SC2);
                if (paramChkCCT_12_SC2 == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "CCT Check 12 - Cable To Final SC" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_12_SC2.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "CCT Check 12 - Cable To Final SC" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_12_CableToFinalSC = paramChkCCT_12_SC2.AsString();


                // Assign Check result
                paramChkCCT_ResultOK = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCctChkResult);
                if (paramChkCCT_ResultOK == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Circuit Check Result" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_ResultOK.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Circuit Check Result" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_OK = paramChkCCT_ResultOK.AsString();


                // Assign Check Message
                paramChkCCT_ResultDescription = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidChkCCT_ResultAPI);
                if (paramChkCCT_ResultDescription == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Circuit Check Message" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramChkCCT_ResultDescription.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Circuit Check Message" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_CCTCheck_Summary = paramChkCCT_ResultDescription.AsString();


                // Assign Circuit Revision
                paramCircuitRevision = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidCircuitRevision);
                if (paramCircuitRevision == null)
                {
                    Schedule_CCTCheck_Summary += "Revit project is missing the parameter '" + "Circuit Revision" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramCircuitRevision.HasValue == false)
                {
                    Schedule_CCTCheck_Summary += "The parameter '" + "Beca Circuit Revision" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                    Schedule_Revision = paramCircuitRevision.AsString();

                // Diversity added
                paramRevitCircuitCurrentPhaseA = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_CURRENT_PHASEA_PARAM);
                paramRevitCircuitCurrentPhaseB = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_CURRENT_PHASEB_PARAM);
                paramRevitCircuitCurrentPhaseC = CCT_Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_APPARENT_CURRENT_PHASEC_PARAM);
                if (paramRevitCircuitCurrentPhaseB != null)
                    CCT_Current_Phase_A = paramRevitCircuitCurrentPhaseA.AsDouble();
                if (paramRevitCircuitCurrentPhaseB != null)
                    CCT_Current_Phase_B = paramRevitCircuitCurrentPhaseB.AsDouble();
                if (paramRevitCircuitCurrentPhaseB != null)
                    CCT_Current_Phase_C = paramRevitCircuitCurrentPhaseC.AsDouble();

                //// Manual Circuit Length
                //if (paramPBLength_Beca_Circuit_Length_Manual != null)
                //{
                //    if (paramPBLength_Beca_Circuit_Length_Manual.HasValue)
                //    {
                //        if (paramPBLength_Beca_Circuit_Length_Manual.AsInteger() == 1)
                //            CircuitLengthIsManual = true;
                //        else
                //            CircuitLengthIsManual = false;
                //    }
                //    else
                //    {
                //        paramPBLength_Beca_Circuit_Length_Manual.Set(0);
                //    }
                //}

            }
            catch
            {
                Parameters_Good = false;
            }
        }

        public void readCircuitPathMode()
        {
            //circuit path generation mode
            LengthClass.Circuit_Path_Mode = CCT_Electrical_System.CircuitPathMode.ToString();
        }

        public void Get_CircuitCheckResultFromRevit()
        {
            if (Schedule_CCTCheck_OK != null)
            {
                if (Schedule_CCTCheck_OK == "OK")
                {
                    Check_Pass = true;
                }
                else
                {
                    Check_Pass = false;
                }
            }

            if (Schedule_CCTCheck_Summary != null)
            {
                if (Schedule_CCTCheck_Summary.Contains("Warning"))
                {
                    Warning_Count++;
                }
            }
        }


        public void Refresh_DerrivedCircuitProperties()
        {
            //
            // This method is public and can be called at any time to refesh the cable information. 
            // 



            //Check if circuit is a spare way or space
            CCT_Is_Spare_Or_Space = IsSpareOrSpace();

            // Override breaker data for spare/space
            if (CCT_Is_Spare_Or_Space)
            {
                Breaker.CreateSpareSpaceEntry();
                ClearErrors();
            }

            // Confirm all paramteres come in good
            if (Parameters_Good)
            {
                if (MatchUserEnteredInstallMethod() == false) // If we don't get an install method match
                {
                    // TODO: Install method is BAD
                }

                //Check if GPO present
                CCT_GPO_Present = IsGPOPresent();

                //Check if RCD Outlet present
                CCT_RCD_ElementIsPresent = IsRCDPresent();

                //Check if circuit is power or lighting or other
                CCT_Is_Power = Is_PowerCircuit();
                CCT_Is_Lighting = Is_LightingCircuit();

                //Determine whether to use 0.4 or 5sec clearing time for circuit
                CCT_Clearing_Time = DetermineClearingTime();

                //Work out powerBIM current to use for calcs
                CCT_PowerBIM_Current = CalculatePowerBIMCurrent();

                // Update cable data
                Cable_To_First.UpdateCableData();

                // Update cable data
                Cable_To_Final.UpdateCableData();

                // Update breaker data
                Breaker.UpdateBreakerData();

                //length
                var lockedOwnerName = string.Empty;
                LengthClass.UpdateRevitPathMode(PathmodeChangedInEditCircuit, out lockedOwnerName);

                // Flag CCT to revert after transaction has committed
                PathmodeChangedInEditCircuit = false;

                if (!this.CircuitLengthIsManual)
                {

                    LengthClass.CCT_CalculateCircuitLength();
                }

                // Check if it's locked 
                if (lockedOwnerName != string.Empty)
                {
                    LockedOwnerName = lockedOwnerName;
                    IsLocked = true;
                }

            }
        }


        public bool MatchUserEnteredInstallMethod()
        {
            // ** 2.0x 
            // Convert Install Method to String
            // 
            //If Install Method is a number (INT), convert to name (STR)     
            int intInstallMeth;

            try
            {
                // all  good if we just want to leave it blank
                if (Schedule_Install_Method == "")
                {
                    Install_Method_Index = 0;
                    Schedule_Install_Method = "";
                    return true;
                }
                else if (Schedule_Install_Method != null && Schedule_Install_Method.Length <= 2)  //It is possibly a number not a text install method
                {
                    int.TryParse(Schedule_Install_Method, out intInstallMeth);

                    // If we can convert what's been entered into an Int, then try match that Int to corresponding install method. 
                    if (intInstallMeth <= 10 && intInstallMeth >= 1)
                    {
                        Schedule_Install_Method = PowerBIM_Constants.strInstallMethodsList[intInstallMeth];
                        Install_Method_Index = intInstallMeth;

                        // success
                        return true;
                    }

                }
                else if (Schedule_Install_Method != null)
                {
                    // Try Check if string enetered matches any installation method availbile
                    for (int I = 0; I <= 9; I++)
                    {
                        if (Schedule_Install_Method == PowerBIM_Constants.strInstallMethodsList[I])
                        {
                            // Value entered is exact match to string.
                            Install_Method_Index = I;
                            return true;
                        }
                    }
                }
                else
                {
                    // If neither methods return a match
                    Schedule_Install_Method = "Invalid";
                    Install_Method_Index = -1;
                }
            }
            catch
            {
                return false;
            }

            return false;
        }

        #region private_methods


        private bool IsGPOPresent()
        {
            // Go through each element on the circuit

            ElementSet CCTElems = CCT_Electrical_System.Elements;
            bool blIsGPO = false;
            int intGPOCount = 0;

            foreach (Element CTTelem in CCTElems)
            {
                //Only seach through Lighting fixture or Electrical Fixtures
                FamilyInstance CCTFamIns = CTTelem as FamilyInstance;
                if ((CCTFamIns.Category.Name == "Lighting Fixtures") || (CCTFamIns.Category.Name == "Electrical Fixtures"))
                {
                    // Find type name of all elements on circuit.
                    ElementId eid = CTTelem.GetTypeId();
                    ElementType et = projInfo.Document.GetElement(eid) as ElementType;
                    Parameter apparentPower = et.LookupParameter("Beca Apparent Power");
                    Parameter apparentPowerTotal = et.LookupParameter("Beca Apparent Power Total");

                    //Check if type name contains GPO
                    if ((et.Name.ToUpper().Contains("GPO")) || (apparentPower?.AsDouble() == 0) || (apparentPowerTotal?.AsDouble() == 0)) //was removed for enforcing JUST GPO.
                    {
                        // we have a GPO circuit!
                        blIsGPO = true;
                        intGPOCount++;

                        // TODO HIGH account for single and double socket outlets
                    }
                }
            }
            // Writeback total number of GPOs found 
            GPO_Count = intGPOCount;

            return blIsGPO;
        }

        private bool IsRCDPresent()
        {
            // Go through each element on the circuit

            ElementSet CCTElems = CCT_Electrical_System.Elements;
            bool blIsRCD = false;
            string RCDElementName = "";

            foreach (Element CTTelem in CCTElems)
            {
                //Only seach through Lighting fixture or Electrical Fixtures
                FamilyInstance CCTFamIns = CTTelem as FamilyInstance;
                if (CCTFamIns.Category.Name == "Electrical Fixtures")
                {
                    // Find type name of all elements on circuit.
                    ElementId eid = CTTelem.GetTypeId();
                    ElementType et = projInfo.Document.GetElement(eid) as ElementType;

                    //Check if type name contains GPO
                    if (et.Name.ToUpper().Contains("RCD")) 
                    {
                        // we have a RCD on the circuit!
                        blIsRCD = true;
                        RCDElementName += et.FamilyName + " : " + et.Name + " ";
                    }
                }
            }
            // Writeback total number of GPOs found 
            CCT_RCD_Name = RCDElementName;

            return blIsRCD;
        }

        private bool IsSpareOrSpace()
        {
            // Check if circuit is a spare or sapce way and flag 
            if (CCT_Electrical_System.CircuitType == CircuitType.Spare || CCT_Electrical_System.CircuitType == CircuitType.Space)
                return true;
            else
                return false;
        }


        private bool Is_LightingCircuit()
        {
            // If lighting circuit...
            if (Schedule_Description.Length >= 8 && Schedule_Description.Substring(0, 8).ToUpper() == "LIGHTING")
                return true;

            else
                return false;
        }


        private bool Is_PowerCircuit()
        {
            // If power circuit...
            if (Schedule_Description.Length >= 5 && Schedule_Description.Substring(0, 5).ToUpper() == "POWER")
                return true;
            return
                false;
        }


        public double DetermineClearingTime()
        {
            // determine if load is fixed eqipment GPO or Lighting, otherwise use 0.4sec clearing time
            double dblClearingTime;

            // If lighting circuit...
            if (CCT_Is_Lighting)
            {
                dblClearingTime = projInfo.Clearing_Time_Lighting;
            }

            // If power circuit...
            else if (CCT_Is_Power)
            {
                if (CCT_GPO_Present)
                    dblClearingTime = 400; // For GPO circuits, clearning time must be 400ms
                else
                    dblClearingTime = projInfo.Clearing_Time_Power;

            }

            // If neither lighting or power circuit...
            else
            {
                dblClearingTime = 400;
            }

            return dblClearingTime; //TODO
        }


        private double CalculatePowerBIMCurrent()
        {
            double dblCalculatedPowerBIMCurrent = Revit_Current;
            // if GGPO is present
            if (CCT_GPO_Present == true)
            {
                // update load current based on global settings menu slection
                if (projInfo.GPO_Calc_Integer >= 0 && projInfo.GPO_Calc_Integer <= 100) // Use 80% of breaker rating
                {
                    dblCalculatedPowerBIMCurrent = Breaker.Schedule_Trip_Rating * projInfo.GPO_Calc_Integer / 100;
                    if (CCT_Number.Contains("R")) { CCT_Current_Phase_A = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc
                    if (CCT_Number.Contains("W")) { CCT_Current_Phase_B = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc
                    if (CCT_Number.Contains("B")) { CCT_Current_Phase_C = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc

                }

                else if (projInfo.GPO_Calc_Integer == -1) // Use 1000W + 100 rule
                {
                    dblCalculatedPowerBIMCurrent = 4.3478 + ((GPO_Count - 1) * 2 * 0.43478);
                    if (CCT_Number.Contains("R")) { CCT_Current_Phase_A = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc
                    if (CCT_Number.Contains("W")) { CCT_Current_Phase_B = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc
                    if (CCT_Number.Contains("B")) { CCT_Current_Phase_C = dblCalculatedPowerBIMCurrent; } //update durrent for diveristy calc
                }
                else if (projInfo.GPO_Calc_Integer == -2) // Use Acutal load
                {
                    dblCalculatedPowerBIMCurrent = Revit_Current;
                }
                else // Use assigned load
                {
                    dblCalculatedPowerBIMCurrent = Revit_Current;
                }
            }
            return dblCalculatedPowerBIMCurrent;
        }


        #endregion


        public bool CalculateDiversifiedCircuitLoad()
        {
            CCT_Diversified_Current_Phase_A = 0;
            CCT_Diversified_Current_Phase_B = 0;
            CCT_Diversified_Current_Phase_C = 0;
            CCT_Undiversified_Current_Phase_A = 0;
            CCT_Undiversified_Current_Phase_B = 0;
            CCT_Undiversified_Current_Phase_C = 0;
             
            //single phase situatioon
            if ( Number_Of_Poles == 1)
            {
                if (CCT_Current_Phase_A > 0)
                {
                    CCT_Diversified_Current_Phase_A = (CCT_PowerBIM_Current * CCT_Diversity);
                    CCT_Undiversified_Current_Phase_A = CCT_PowerBIM_Current;
                }
                if (CCT_Current_Phase_B > 0)
                {
                    CCT_Diversified_Current_Phase_B = (CCT_PowerBIM_Current * CCT_Diversity);
                    CCT_Undiversified_Current_Phase_B = CCT_PowerBIM_Current;
                }
                    
                if (CCT_Current_Phase_C > 0)
                {
                    CCT_Diversified_Current_Phase_C = (CCT_PowerBIM_Current * CCT_Diversity);
                    CCT_Undiversified_Current_Phase_C = CCT_PowerBIM_Current;
                }
                    
            }

            // three phase situation
            if (Number_Of_Poles == 3)
            {
                CCT_Diversified_Current_Phase_A = (CCT_PowerBIM_Current * CCT_Diversity);
                CCT_Diversified_Current_Phase_B = (CCT_PowerBIM_Current * CCT_Diversity);
                CCT_Diversified_Current_Phase_C = (CCT_PowerBIM_Current * CCT_Diversity);
                CCT_Undiversified_Current_Phase_A = CCT_PowerBIM_Current;
                CCT_Undiversified_Current_Phase_B = CCT_PowerBIM_Current;
                CCT_Undiversified_Current_Phase_C = CCT_PowerBIM_Current;
            }

            return true;
        }



        public bool Auto_SelectCable(bool isOneElement)
        {
            // initialise counters
            int intTryStart = 1;
            int intTryFinish = 0;

            // Check if it's a lighing circuit, start with a 1.5mm and go up to a 4mm
            if (CCT_Is_Lighting)
            {
                intTryStart = PowerBIM_Constants.CableTryStart_Lighting;
                intTryFinish = PowerBIM_Constants.CableTryFinish_Lighting;
            }
            // Check if it's a power circuit, start with a 2.5mm and go up to a 6mm
            else if (CCT_Is_Power)
            {
                // single phase cable
                if (Number_Of_Poles == 1)
                {
                    intTryStart = PowerBIM_Constants.CableTryStart_Power1Phase;
                    intTryFinish = PowerBIM_Constants.CableTryFinish_Power1Phase;
                }
                // 3 phase cable
                else if (Number_Of_Poles == 3)
                {
                    intTryStart = PowerBIM_Constants.CableTryStart_Power3Phase;
                    intTryFinish = PowerBIM_Constants.CableTryFinish_Power3Phase;
                }
            }

            // figure out when to stop running
            int intRunMax = 2 * (intTryFinish - intTryStart);

            //
            // ** check through the cable canidates
            //

            //initial try row 1st and Rem the same
            int intTry1st = intTryStart;
            int intTryRem = intTryStart;

            for (int intRun = 0; intRun <= intRunMax; intRun++)
            {
                //
                //** 2.052 Check Cable Ir vs Circuit Trip Rating of both cables
                //

                // add the new cable test integ
                Schedule_Cable_To_First = intTry1st.ToString();
                Schedule_Cable_To_Final = intTryRem.ToString();

                Cable_To_First.UpdateCableData();
                Schedule_Cable_To_First = Cable_To_First.Cable_Name;

                Cable_To_Final.UpdateCableData();
                Schedule_Cable_To_Final = Cable_To_Final.Cable_Name;

                // Re-run cable calculations
                PowerBIM_CalculationEngine();

                // Run cable check method, if passed we can keep this cable selection
                if (PowerBIM_Calculations.PowerBIMCircuitCheckEngine(this))
                {
                    // End the search for a cable - we have found one.
                    return true;
                }
                else
                {
                    if (isOneElement)
                    {
                        if (intRun % 2 == 0)
                        {
                            intTryRem += 1;
                            intTry1st += 1;
                        }
                    }
                    else
                    {
                        //increase a 1st cable after even run, increase rem cable after odd run
                        if (intRun % 2 == 0) { intTry1st += 1; }
                        else { intTryRem += 1; }
                    }
                }
            }
            // If we get to this point it means we havn't been able to finds a cable.
            Cable_To_First.CreateNullEntry();
            Cable_To_Final.CreateNullEntry();
            Schedule_Cable_To_First = Cable_To_First.Cable_Name;
            Schedule_Cable_To_Final = Cable_To_Final.Cable_Name;

            return false;
        }


        public bool CircuitCheck1_Data()
        {
            // Report back if breraker data is good an parameters are good.
            if (Breaker.Data_Good == true)
            {
                // PASS
                Schedule_CCTCheck_1_Data = "Pass";
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_1_Data = "Fail: " + Breaker.Error_Message;
                Check_ErrorMessage += " (Breaker settings not valid)";
                return false;
            }

        }


        public bool CircuitCheck2_CableToFirst()
        {
            // Check that cable to first is valid

            if (Cable_To_First.Data_Good == true)
            {
                // PASS
                Schedule_CCTCheck_2_CableToFirst = "Valid";
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_2_CableToFirst = Cable_To_First.Error_Message;
                Check_ErrorMessage += " (Cable to first Invalid)";
                return false;
            }
        }


        public bool CircuitCheck3_CableToFinal()
        {
            // Check that cable to final is valid

            if (Cable_To_Final.Data_Good == true)
            {
                // PASS
                Schedule_CCTCheck_3_CableToFinal = "Valid";
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_3_CableToFinal = Cable_To_Final.Error_Message;
                Check_ErrorMessage += " (Cable-to-final Invalid)";
                return false;
            }
        }


        public bool CircuitCheck4_Discrimination()
        {
            // Check discrimination with upstream breaker rating
            double newTripRating = Breaker.Schedule_Trip_Rating * projInfo.Discrimination_Test_Multiplier; // Added x1.5 or x2 selection

            if (newTripRating <= DB.Upstream_Device_Rating)
            {
                // PASS
                Schedule_CCTCheck_4_Discrimination = Breaker.Schedule_Trip_Rating.ToString() + " * " + projInfo.Discrimination_Test_Multiplier.ToString() + " / " + DB.Upstream_Device_Rating.ToString();
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_4_Discrimination = Breaker.Schedule_Trip_Rating.ToString() + " * " + projInfo.Discrimination_Test_Multiplier.ToString() + " (" + DB.Upstream_Device_Rating.ToString() + ")";
                Check_ErrorMessage += " (CPD not discriminating with upstream device)";
                return false;
            }
        }


        public bool CircuitCheck5_OverloadCurrent()
        {
            if (Breaker.Schedule_Trip_Rating > GetCurrent())
            {
                // PASS
                Schedule_CCTCheck_5_BreakerCurrent = Breaker.Schedule_Trip_Rating.ToString() + " / " + Math.Round(GetCurrent(), 2).ToString();
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_5_BreakerCurrent = Breaker.Schedule_Trip_Rating.ToString() + " (" + Math.Round(GetCurrent(), 2).ToString() + ")";
                Check_ErrorMessage += " (Load current too high for breaker)";
                return false;
            }
        }


        public bool CircuitCheck6_RatedCurrent_CableToFirst()
        {
            double derating_Factor = Schedule_Derating_Factor;

            #region Clause ******* on Pg.24 of AS/NZS 3008-1-2 2017 => issue number 376

            if (GetCurrent() < .35 * Cable_To_First.I_Rated)
            {
                derating_Factor = 1;
            }

            #endregion

            // ** 2.06a Check Cable 1st Rating Current
            if (PowerBIM_Calculations.Bool_CableCurrentRatingCheck(Cable_To_First.I_Rated, Breaker.Schedule_Trip_Rating, derating_Factor))
            {
                // PASS
                Schedule_CCTCheck_6_CableToFirstCurrent = Breaker.Schedule_Trip_Rating.ToString() + " / " + Cable_To_First.I_Rated.ToString() + " * " + derating_Factor;
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_6_CableToFirstCurrent = Breaker.Schedule_Trip_Rating.ToString() + " (" + Cable_To_First.I_Rated.ToString() + " * " + derating_Factor + ")";
                Check_ErrorMessage += " (Breaker rating too high to protect cable-to-first overload)";
                return false;
            }
        }


        public bool CircuitCheck7_RatedCurrent_CableToFinal()
        {
            // ** 2.06a Check Cable 1st Rating Current
            if (PowerBIM_Calculations.Bool_CableCurrentRatingCheck(Cable_To_Final.I_Rated, Breaker.Schedule_Trip_Rating, Schedule_Derating_Factor))
            {
                // PASS
                Schedule_CCTCheck_7_CableToFinalCurrent = Breaker.Schedule_Trip_Rating.ToString() + " / " + Cable_To_Final.I_Rated.ToString() + " * " + Schedule_Derating_Factor;
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_7_CableToFinalCurrent = Breaker.Schedule_Trip_Rating.ToString() + " (" + Cable_To_Final.I_Rated.ToString() + " * " + Schedule_Derating_Factor + ")";
                Check_ErrorMessage += " (Breaker rating too high to protect cable-to-final overload)";
                return false;
            }
        }


        public bool CircuitCheck8_CheckEFLI()
        {
            if (PowerBIM_Calculations.Bool_EFLiCheck(this))
            {
                // PASS
                Schedule_CCTCheck_8_EFLI = Math.Round(Breaker.EFLI_Max, 2).ToString() + " / " + Math.Round(CalcRes_Total_EFLi, 2);

                // Check that the PowerBIM hasn't had to select a different breaker KA rating to find a product that actually exists!
                if (Breaker.kA_Rating_Used != Breaker.Min_kA_Rating)
                {
                    blWarn_AlternatekARatingSelected = true; // TODO add information aroun what CPD was selected
                }

                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_8_EFLI = Math.Round(Breaker.EFLI_Max, 2).ToString() + " (" + Math.Round(CalcRes_Total_EFLi, 2).ToString() + ")";
                Check_ErrorMessage += " (EFLI too High)";
                return false;
            }
        }


        public bool CircuitCheck9_CircuitVoltDrop()
        {
            double maxVD = Math.Round(DB.Schedule_Final_Circuit_MaxVD * 100, 2);
            double calcVD = Math.Round(CalcRes_Final_Circuit_VD_Percentage * 100, 2);

            if (PowerBIM_Calculations.Bool_CableVoltDropCheck(CalcRes_Final_Circuit_VD_Percentage, DB.Schedule_Final_Circuit_MaxVD))
            {
                // PASS
                Schedule_CCTCheck_9_FinalCircuitVD = maxVD.ToString() + " / " + calcVD.ToString();
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_9_FinalCircuitVD = maxVD.ToString() + " (" + calcVD.ToString() + ")";
                Check_ErrorMessage += " (Final circuit max VD exceed)";
                return false;
            }
        }


        public bool CircuitCheck10_SystemVoltDrop()
        {
            double maxVD = Math.Round(projInfo.System_VD_Max_Perc * 100, 2);
            double calcVD = Math.Round(CalcRes_Final_Circuit_VD_Percentage * 100, 2);
            double dbVD = Math.Round(DB.DBVD * 100, 2);

            if (PowerBIM_Calculations.Bool_SystemVoltDropCheck(CalcRes_Final_Circuit_VD_Percentage, DB.DBVD, projInfo.System_VD_Max_Perc))
            {
                // PASS
                Schedule_CCTCheck_10_SystemVD = maxVD.ToString() + " / " + calcVD.ToString() + " + " + dbVD.ToString();
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_10_SystemVD = maxVD.ToString() + " (" + calcVD.ToString() + " + " + dbVD.ToString() + ")";
                Check_ErrorMessage += " (System max VD exceed)";
                return false;
            }
        }


        public bool CircuitCheck11_ShortCircuitCable1()
        {
            // FOR FIRST CALBE
            double K2S2_first = Math.Round(Cable_To_First.I2t_Max / 1000, 1);
            double MaxSCWithstand_first = Math.Round(Breaker.I2t_Phase / 1000, 1);

            if (PowerBIM_Calculations.Bool_ShortCircuitCheck(Breaker.I2t_Phase, Cable_To_First.I2t_Max))
            {
                // PASS
                Schedule_CCTCheck_11_CableToFirstSC = K2S2_first.ToString() + "kJ / " + MaxSCWithstand_first.ToString() + "kJ";
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_11_CableToFirstSC = K2S2_first.ToString() + "kJ /(" + MaxSCWithstand_first.ToString() + "kJ )";
                Check_ErrorMessage += " (Cable-to-first let-through energy too high)";
                return false;
            }
        }


        public bool CircuitCheck12_ShortCircuitCable2()
        {
            // FOR SECOND CABLE
            double K2S2_final = Math.Round(Cable_To_Final.I2t_Max / 1000, 1);
            double MaxSCWithstand_final = Math.Round(Breaker.I2t_Phase / 1000, 1);

            // ** 2.097
            if (PowerBIM_Calculations.Bool_ShortCircuitCheck(Breaker.I2t_Phase, Cable_To_Final.I2t_Max))
            {
                // PASS
                Schedule_CCTCheck_12_CableToFinalSC = K2S2_final.ToString() + "kJ / " + MaxSCWithstand_final.ToString() + "kJ";
                return true;
            }
            else
            {
                // FAIL
                Schedule_CCTCheck_12_CableToFinalSC = K2S2_final.ToString() + "kJ /(" + MaxSCWithstand_final.ToString() + "kJ )";
                Check_ErrorMessage += " (Cable-to-final let-through energy too high)";
                return false;
            }
        }


        public bool PowerBIM_CalculationEngine()
        {
            // Determine what clearing time to use for circuit (0.4sec or 5sec)
            DetermineClearingTime();

            //Calculate the diversified circuit load to use for DB totals
            CalculateDiversifiedCircuitLoad();

            if (CCT_Is_Lighting)
            {
                CalcRes_Final_Circuit_VD = PowerBIM_Calculations.Calc_FinalCircuitVoltDrop(this, projInfo.LightingVDCalculation);
            }
            else if (CCT_Is_Power)
            {
                CalcRes_Final_Circuit_VD = PowerBIM_Calculations.Calc_FinalCircuitVoltDrop(this, projInfo.PowerVDCalculation);
            }
            else
            {
                CalcRes_Final_Circuit_VD = PowerBIM_Calculations.Calc_FinalCircuitVoltDrop(this, projInfo.OtherVDCalculation);
            }

            CalcRes_Final_Circuit_VD_Percentage = PowerBIM_Calculations.Calc_FinalCircuitVoltDropPercentage(CalcRes_Final_Circuit_VD, Number_Of_Poles);

            return true; // TODO
        }


        public bool AddPassMessage()
        {
            // Mark Circuit check summar as PASS
            Schedule_CCTCheck_OK = "OK";

            // Mark circuit check message on panel schedule as PASS
            Schedule_CCTCheck_Summary = "PASS";

            return true; //TODO
        }


        public bool AddErrorMessages()
        {
            if (!CCT_Is_Spare_Or_Space)
            {
                // Mark Circuit check summar as ERROR
                Schedule_CCTCheck_OK = "Error";

                // Add error messages to circuit check message on panel schedule. 
                Schedule_CCTCheck_Summary = "Error: " + Check_ErrorMessage;


            }
            else
                ClearErrors();

            return true; //TODO
        }

        public void CheckDefinedCableWarning(string cableToFirstValue)
        {
            //Inital Cable Data Path
            var cableNumber = projInfo.Standard_CableNames.FirstOrDefault(x => x.Value == cableToFirstValue).Key;
            if (cableNumber >= 128 && cableNumber <= 157)
            {
                blWarn_UserDefCableSelected = true;
                Warning_Count++;
            }
            else
            {
                blWarn_UserDefCableSelected = false;
            }

        }

        public void CheckDashCableWarning(PowerBIM_CircuitData ctt)
        {
            bool isSingleElementWithValidCable = (ctt.Number_Of_Elements == 1 && ctt.Cable_To_Final.Cable_Name != "-"); //DO WE NEED REFER TO SCHEDULE LOGIC?
            bool isFirstAndFinalCableSame = (ctt.Cable_To_First.Cable_Name == ctt.Cable_To_Final.Cable_Name);
            if (isSingleElementWithValidCable && isFirstAndFinalCableSame) //AND?? or OR?
            {
                blWarn_DashedFinalCableSelection = true;
                Warning_Count++;
            }
        }

        public void CheckEmergencyLightingExistsWarning()
        {
            foreach (Element el in CCT_Electrical_System.Elements)
            {
                if (el.LookupParameter("Emergency Luminaire")?.AsValueString() == "Yes" || projInfo.Document.GetElement(el.GetTypeId()).LookupParameter("Beca Type Use")?.AsString() == "IsEmergency")
                {
                    blWarn_EmLightingPresent = true;
                    Warning_Count++;
                    break;
                }
            }
        }

        public void CheckDeratingFactorIsAboveOneWarning(double deratingFactor)
        {
            if (deratingFactor > 1.0)
            {
                Warning_Count++;
                blWarn_DeratingAbove1 = true;
            }
        }

        public void CheckRCDElementIsPresentWarning(bool blIsRCD)
        {
            if (blIsRCD)
            {
                Warning_Count++;
                blWarn_IsRCD = true;
            }
        }


        public void AddWarningMessages()
        {
            // add warning preface
            string warningsMessage = " || Warning: ";
            //Schedule_CCTCheck_Summary += " || Warning: ";

            // concatenate warnings....
            if (blWarn_UserDefCableSelected == true)
                warningsMessage += "Non standard cable selected. ";

            if (blWarn_EmLightingPresent == true)
                warningsMessage += "Circuit contains EM Lighting Device(s). ";

            if (blWarn_DeratingAbove1 == true)
                warningsMessage += "Derating Factor greater than 100%. ";

            if (blWarn_AlternatekARatingSelected == true)
                warningsMessage += "CPD with design kA rating not available - " + Breaker.kA_Rating_Used + "kA breaker selected. ";

            if (blWarn_IsRCD == true)
                warningsMessage += "Circuit contains element(s) marked as RCD: " + CCT_RCD_Name + ". ";

            if (blWarn_DashedFinalCableSelection == true)
            {
                warningsMessage += "Two Cables selected for Circuit with only 1 Element, use '-' for remainder instead.";
            }

            if (warningsMessage != " || Warning: ")
                Schedule_CCTCheck_Summary += warningsMessage;

            return;
        }


        public bool SetSchedule_CheckParamteters()
        {
            if ((Parameters_Good == true) && (CCT_Is_Spare_Or_Space == false))
            {
                try
                {
                    // Wirte back all the 12 check results to the verififaction schedule.
                    paramChkCCT_1_Data.Set(Schedule_CCTCheck_1_Data);
                    paramChkCCT_2_CBL1.Set(Schedule_CCTCheck_2_CableToFirst);
                    paramChkCCT_3_CBLRM.Set(Schedule_CCTCheck_3_CableToFinal);
                    paramChkCCT_4_Discrimination.Set(Schedule_CCTCheck_4_Discrimination);
                    paramChkCCT_5_Current2.Set(Schedule_CCTCheck_5_BreakerCurrent);
                    paramChkCCT_6_Current3.Set(Schedule_CCTCheck_6_CableToFirstCurrent);
                    paramChkCCT_7_Current4.Set(Schedule_CCTCheck_7_CableToFinalCurrent);
                    paramChkCCT_8_EFLI.Set(Schedule_CCTCheck_8_EFLI);
                    paramChkCCT_9_VD1.Set(Schedule_CCTCheck_9_FinalCircuitVD);
                    paramChkCCT_10_VD2.Set(Schedule_CCTCheck_10_SystemVD);
                    paramChkCCT_11_SC1.Set(Schedule_CCTCheck_11_CableToFirstSC);
                    paramChkCCT_12_SC2.Set(Schedule_CCTCheck_12_CableToFinalSC);
                    paramChkCCT_ResultDescription.Set(Schedule_CCTCheck_Summary);
                    paramChkCCT_ResultOK.Set(Schedule_CCTCheck_OK);


                    // Return true if we were able to set all params successfuly 
                    return true;
                }
                catch
                {
                    // Return false if exception
                    return false;
                }
            }
            else
                // If bad params or way is designated 'spare' or 'space' return false 
                return false;
        }


        public bool SetSchedule_CableName()
        {
            try
            {
                if ((Parameters_Good == true) && (CCT_Is_Spare_Or_Space == false))
                {
                    paramCbl_1stElem.Set(Schedule_Cable_To_First);
                    paramCbl_RemainderElem.Set(Schedule_Cable_To_Final);

                    return true;
                }
                else
                    // If bad params or way is designated 'spare' or 'space' return false 
                    return false;
            }
            catch
            {
                // Return false if exception
                return false;
            }
        }


        public bool SetSchedule_RCDDeratingDiversityAndRevision()
        {
            if ((Parameters_Good == true) && (CCT_Is_Spare_Or_Space == false))
            {
                try
                {
                    paramDeratingFactor.Set(Schedule_Derating_Factor);
                    paramDiversity.Set(CCT_Diversity);
                    paramRCDprotection.Set(Schedule_RCD);
                    paramCircuitRevision.Set(Schedule_Revision);

                    // Return true if we were able to set all params successfuly 
                    return true;
                }
                catch
                {
                    // Return false if exception
                    return false;
                }
            }
            else
                // If bad params or way is designated 'spare' or 'space' return false 
                return false;
        }


        public bool SetSchedule_InstallMethod()
        {
            if ((Parameters_Good == true) && (CCT_Is_Spare_Or_Space == false))
            {
                try
                {
                    paramInstallMethod.Set(Schedule_Install_Method);

                    // Return true if we were able to set all params successfuly 
                    return true;
                }
                catch
                {
                    // Return false if exception
                    return false;
                }
            }
            else
                // If bad params or way is designated 'spare' or 'space' return false 
                return false;
        }


        public bool SetSchedule_BreakerData()
        {
            if ((Parameters_Good == true))
            {
                try
                {
                    // If circuit is normal we can write all parameters
                    if (!CCT_Is_Spare_Or_Space)
                        Breaker.Commit_AllBreakerDataToRevit();

                    // else if circuit is spare or space we can only set the trip rating.. lol revit
                    else
                        Breaker.Commit_BreakerRatingOnlyToRevit();

                    // Return true if we were able to set all params successfuly 
                    return true;
                }
                catch
                {
                    // Return false if exception
                    return false;
                }
            }
            else
                // If bad params or way is designated 'spare' or 'space' return false 
                return false;
        }

        public bool SetSchedule_CableLengths()
        {
            if ((Parameters_Good == true) && (CCT_Is_Spare_Or_Space == false))
            {
                try
                {
                    // Compute and write back lengths to corresponding parameters

                    //CCT_Electrical_System.CircuitPathMode = (Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode)Enum.Parse(typeof(Autodesk.Revit.DB.Electrical.ElectricalCircuitPathMode), Circuit_Path_Mode, true); //converts string into revit's enum to set revit path mode
                    //CCT_CalculateCircuitLength_RevitPaths(DB.DB_Location);

                    paramLen_1stElem.Set(RevitUnitConvertor.MmToInternal(LengthClass.Length_To_First));
                    paramLen_Total.Set(RevitUnitConvertor.MmToInternal(LengthClass.Length_Total));


                    // Return true if we were able to set all params successfuly 
                    return true;
                }
                catch
                {
                    // Return false if exception
                    return false;
                }
            }
            else
                // If bad params or way is designated 'spare' or 'space' return false 
                return false;
        }

        internal bool SetSchedule_Description()
        {
            try
            {
                paramLoadDescription.Set(Schedule_Description);
                // Return true if we were able to set all params successfuly 
                return true;
            }
            catch
            {
                // Return false if exception
                return false;
            }

        }



        public void ClearErrors()
        {
            Schedule_CCTCheck_OK = "IGNORED";
            Schedule_CCTCheck_Summary = "Circuit is Spare or Space";
        }

        internal void SetCircuitLengthToRevit()
        {
            paramPBLength_Beca_Circuit_Length_Manual = CCT_Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidPBLength_Circuit_Length_Manual);
            if (paramPBLength_Beca_Circuit_Length_Manual != null)
            {
                if (CircuitLengthIsManual)
                {
                    paramPBLength_Beca_Circuit_Length_Manual.Set(1);
                }
                else
                {
                    paramPBLength_Beca_Circuit_Length_Manual.Set(0);
                }
            }
        }

        public void RunPowerBIMCheck()
        {
            if (!CCT_Is_Spare_Or_Space)
            {
                Check_ErrorMessage = "";//error message string needs to be cleared before 

                PowerBIM_CalculationEngine();

                // Perform all circuit checks on new calculation data
                Check_Pass = PowerBIM_Calculations.PowerBIMCircuitCheckEngine(this);

                // Write back pass message
                if (Check_Pass == true)
                {
                    //Mark message as OK
                    AddPassMessage();
                }
                else
                {
                    // Add error messages
                    AddErrorMessages();
                }

                // Add warning messages (non critical)
                if (Warning_Count > 0)
                {
                    AddWarningMessages();
                }
            }
        }


    }

}