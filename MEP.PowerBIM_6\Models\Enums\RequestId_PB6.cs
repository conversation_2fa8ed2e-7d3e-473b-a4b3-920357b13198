namespace MEP.PowerBIM_6.Models.Enums
{
    /// <summary>
    /// Enumeration of request IDs for PowerBIM 6 ExternalEvent communication
    /// Used to identify different operations that need to be executed in Revit context
    /// </summary>
    public enum RequestId_PB6
    {
        /// <summary>
        /// No operation requested
        /// </summary>
        None = 0,

        /// <summary>
        /// Initialize PowerBIM data and load distribution boards
        /// </summary>
        Initialize = 1,

        /// <summary>
        /// Update circuit calculations for all distribution boards
        /// </summary>
        UpdateCircuits = 2,

        /// <summary>
        /// Update cable calculations for specific circuits
        /// </summary>
        UpdateCables = 3,

        /// <summary>
        /// Update breaker calculations for specific circuits
        /// </summary>
        UpdateBreakers = 4,

        /// <summary>
        /// Calculate voltage drops for all circuits
        /// </summary>
        CalculateVoltageDrops = 5,

        /// <summary>
        /// Commit all changes to Revit model
        /// </summary>
        CommitToRevit = 6,

        /// <summary>
        /// Refresh data from Revit model
        /// </summary>
        RefreshFromRevit = 7,

        /// <summary>
        /// Open database edit window
        /// </summary>
        OpenDbEdit = 8,

        /// <summary>
        /// Open circuit edit window
        /// </summary>
        OpenCircuitEdit = 9,

        /// <summary>
        /// Open advanced settings window
        /// </summary>
        OpenAdvancedSettings = 10,

        /// <summary>
        /// Open export window
        /// </summary>
        OpenExport = 11,

        /// <summary>
        /// Open import settings window
        /// </summary>
        OpenImportSettings = 12,

        /// <summary>
        /// Export data to Excel
        /// </summary>
        ExportToExcel = 13,

        /// <summary>
        /// Export data to PDF
        /// </summary>
        ExportToPdf = 14,

        /// <summary>
        /// Import settings from file
        /// </summary>
        ImportSettings = 15,

        /// <summary>
        /// Save current settings
        /// </summary>
        SaveSettings = 16,

        /// <summary>
        /// Load saved settings
        /// </summary>
        LoadSettings = 17,

        /// <summary>
        /// Reset settings to defaults
        /// </summary>
        ResetSettings = 18,

        /// <summary>
        /// Validate all distribution board data
        /// </summary>
        ValidateAllData = 19,

        /// <summary>
        /// Update project information
        /// </summary>
        UpdateProjectInfo = 20,

        /// <summary>
        /// Select elements in Revit model
        /// </summary>
        SelectElements = 21,

        /// <summary>
        /// Highlight elements in Revit model
        /// </summary>
        HighlightElements = 22,

        /// <summary>
        /// Zoom to elements in Revit model
        /// </summary>
        ZoomToElements = 23,

        /// <summary>
        /// Edit circuit path interactively
        /// </summary>
        EditCircuitPath = 24,

        /// <summary>
        /// Edit distribution board path interactively
        /// </summary>
        EditDbPath = 25,

        /// <summary>
        /// Calculate short circuit values
        /// </summary>
        CalculateShortCircuit = 26,

        /// <summary>
        /// Update cable sizing
        /// </summary>
        UpdateCableSizing = 27,

        /// <summary>
        /// Update breaker sizing
        /// </summary>
        UpdateBreakerSizing = 28,

        /// <summary>
        /// Perform bulk operations on circuits
        /// </summary>
        BulkOperations = 29,

        /// <summary>
        /// Close the application
        /// </summary>
        Close = 30,

        /// <summary>
        /// Save circuit edit enhanced data
        /// </summary>
        SaveCircuitEditEnhanced = 31,

        /// <summary>
        /// Recalculate circuits
        /// </summary>
        RecalculateCircuits = 32,

        /// <summary>
        /// Save distribution board data
        /// </summary>
        SaveDistributionBoard = 33,

        /// <summary>
        /// Import from CSV file
        /// </summary>
        ImportFromCsv = 34,

        /// <summary>
        /// Activate path edit view
        /// </summary>
        ActivatePathEditView = 35,

        /// <summary>
        /// Commit distribution board settings
        /// </summary>
        CommitDistributionBoardSettings = 36,

        /// <summary>
        /// Commit project info
        /// </summary>
        CommitProjectInfo = 37,

        /// <summary>
        /// Save project
        /// </summary>
        SaveProject = 38,

        /// <summary>
        /// Refresh data from Revit
        /// </summary>
        RefreshData = 39
    }
}
