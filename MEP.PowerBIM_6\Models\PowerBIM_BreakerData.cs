using System;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Independent PowerBIM Breaker Data class for MEP.PowerBIM_6
    /// Contains breaker/protection device information
    /// </summary>
    public class PowerBIM_BreakerData
    {
        #region Properties

        /// <summary>
        /// Schedule protective device type
        /// </summary>
        public string Schedule_Protective_Device { get; set; }

        /// <summary>
        /// Schedule curve type
        /// </summary>
        public string Schedule_Curve_Type { get; set; }

        /// <summary>
        /// Schedule device rating
        /// </summary>
        public string Schedule_Device_Rating { get; set; }

        /// <summary>
        /// Schedule RCD protection
        /// </summary>
        public string Schedule_RCD_Protection { get; set; }

        /// <summary>
        /// Schedule other controls
        /// </summary>
        public string Schedule_Other_Controls { get; set; }

        /// <summary>
        /// Device rating in amps
        /// </summary>
        public double Device_Rating_Amps { get; set; }

        /// <summary>
        /// Device curve type index
        /// </summary>
        public int Device_Curve_Type_Index { get; set; }

        /// <summary>
        /// Device fault rating in kA
        /// </summary>
        public double Device_kA_Rating { get; set; }

        /// <summary>
        /// RCD rating in mA
        /// </summary>
        public double RCD_Rating_mA { get; set; }

        /// <summary>
        /// RCD type
        /// </summary>
        public string RCD_Type { get; set; }

        /// <summary>
        /// Indicates if RCD is present
        /// </summary>
        public bool RCD_Present { get; set; }

        /// <summary>
        /// Trip time at fault current
        /// </summary>
        public double Trip_Time_At_Fault_Current { get; set; }

        /// <summary>
        /// Magnetic trip current
        /// </summary>
        public double Magnetic_Trip_Current { get; set; }

        /// <summary>
        /// Thermal trip current
        /// </summary>
        public double Thermal_Trip_Current { get; set; }

        /// <summary>
        /// Indicates if device can discriminate
        /// </summary>
        public bool Can_Discriminate { get; set; }

        /// <summary>
        /// Discrimination check result
        /// </summary>
        public string Discrimination_Check { get; set; }

        /// <summary>
        /// Device validation result
        /// </summary>
        public string Device_Valid { get; set; }

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        public bool Data_Good { get; set; }

        /// <summary>
        /// Error message
        /// </summary>
        public string Error_Message { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor
        /// </summary>
        public PowerBIM_BreakerData()
        {
            InitializeDefaults();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize default values
        /// </summary>
        private void InitializeDefaults()
        {
            Schedule_Protective_Device = "MCB";
            Schedule_Curve_Type = "C";
            Schedule_Device_Rating = "16A";
            Schedule_RCD_Protection = "None";
            Schedule_Other_Controls = string.Empty;
            
            Device_Rating_Amps = 16.0;
            Device_Curve_Type_Index = 2; // C curve
            Device_kA_Rating = 6.0;
            RCD_Rating_mA = 30.0;
            RCD_Type = "AC";
            RCD_Present = false;
            
            Trip_Time_At_Fault_Current = 0.0;
            Magnetic_Trip_Current = 0.0;
            Thermal_Trip_Current = 0.0;
            
            Can_Discriminate = false;
            Discrimination_Check = "PENDING";
            Device_Valid = "PENDING";
            Data_Good = false;
            Error_Message = string.Empty;
        }

        /// <summary>
        /// Load breaker data from device rating string
        /// </summary>
        /// <param name="deviceRating">Device rating string (e.g., "16A")</param>
        public void LoadFromDeviceRating(string deviceRating)
        {
            try
            {
                Schedule_Device_Rating = deviceRating ?? "16A";
                
                // Extract numeric rating
                string numericPart = deviceRating?.Replace("A", "").Replace("a", "").Trim() ?? "16";
                if (double.TryParse(numericPart, out double rating))
                {
                    Device_Rating_Amps = rating;
                    
                    // Set typical magnetic trip current (5-10 times rating for MCB)
                    Magnetic_Trip_Current = rating * 7.0; // Typical for C curve
                    Thermal_Trip_Current = rating * 1.13; // 113% of rating
                }
                
                Data_Good = true;
                Device_Valid = "VALID";
            }
            catch (Exception ex)
            {
                Data_Good = false;
                Error_Message = ex.Message;
                Device_Valid = "ERROR";
            }
        }

        /// <summary>
        /// Validate breaker data
        /// </summary>
        public void ValidateBreaker()
        {
            try
            {
                if (Device_Rating_Amps <= 0)
                {
                    Device_Valid = "INVALID - No rating";
                    Data_Good = false;
                    return;
                }

                if (string.IsNullOrEmpty(Schedule_Protective_Device))
                {
                    Device_Valid = "INVALID - No device type";
                    Data_Good = false;
                    return;
                }

                Device_Valid = "VALID";
                Data_Good = true;
                Error_Message = string.Empty;
            }
            catch (Exception ex)
            {
                Device_Valid = "ERROR";
                Data_Good = false;
                Error_Message = ex.Message;
            }
        }

        /// <summary>
        /// Check discrimination with upstream device
        /// </summary>
        /// <param name="upstreamDevice">Upstream breaker</param>
        public void CheckDiscrimination(PowerBIM_BreakerData upstreamDevice)
        {
            try
            {
                if (upstreamDevice == null)
                {
                    Discrimination_Check = "NO UPSTREAM";
                    Can_Discriminate = false;
                    return;
                }

                // Simple discrimination check - upstream should be higher rated
                if (upstreamDevice.Device_Rating_Amps > Device_Rating_Amps * 1.6)
                {
                    Discrimination_Check = "PASS";
                    Can_Discriminate = true;
                }
                else
                {
                    Discrimination_Check = "FAIL";
                    Can_Discriminate = false;
                }
            }
            catch (Exception ex)
            {
                Discrimination_Check = "ERROR";
                Can_Discriminate = false;
                Error_Message = ex.Message;
            }
        }

        #endregion
    }
}
