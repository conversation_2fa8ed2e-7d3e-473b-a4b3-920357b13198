# PowerBIM WPF MVVM Architecture Plan

## Current MEP.PowerBIM_6 Status Analysis

### ✅ **Already Implemented:**
- **Project Structure**: Basic WPF project with UseWPF enabled
- **Converters**: `BooleanToVisibilityConverter`, `StatusColorConverter`, `NumericToColorConverter`
- **Basic Command**: `PowerBIM_6_Command.cs` entry point (skeleton)
- **Empty Folders**: Models, Services, ViewModels, Views (ready for implementation)

### 🔧 **Missing Components:**
- All MVVM architecture components
- Modeless window management system
- Request/Response handling architecture
- Service layer implementation
- View and ViewModel implementations

## Proposed WPF MVVM Folder Structure

```
MEP.PowerBIM_6/
├── 📁 RevitCommands/
│   └── PowerBIM_6_Command.cs ✅ (needs enhancement)
│
├── 📁 Views/ (WPF Windows & UserControls)
│   ├── MainWindow.xaml + .cs
│   ├── DbEditWindow.xaml + .cs
│   ├── CircuitEditWindow.xaml + .cs
│   ├── AdvancedSettingsWindow.xaml + .cs
│   ├── DbSettingsWindow.xaml + .cs
│   ├── ExportWindow.xaml + .cs
│   ├── ImportSettingsWindow.xaml + .cs
│   └── 📁 UserControls/
│       ├── DistributionBoardSummaryControl.xaml + .cs
│       ├── ProjectSettingsControl.xaml + .cs
│       ├── CircuitDataGridControl.xaml + .cs
│       └── BulkOperationsControl.xaml + .cs
│
├── 📁 ViewModels/ (MVVM ViewModels with CommunityToolkit)
│   ├── BaseViewModel.cs
│   ├── MainViewModel.cs
│   ├── DbEditViewModel.cs
│   ├── CircuitEditViewModel.cs
│   ├── AdvancedSettingsViewModel.cs
│   ├── DbSettingsViewModel.cs
│   ├── ExportViewModel.cs
│   ├── ImportSettingsViewModel.cs
│   └── 📁 ItemViewModels/
│       ├── DistributionBoardItemViewModel.cs
│       ├── CircuitItemViewModel.cs
│       └── ProjectInfoViewModel.cs
│
├── 📁 Models/ (Data Models with ObservableObject)
│   ├── ProjectInfoModel.cs
│   ├── DistributionBoardModel.cs
│   ├── CircuitModel.cs
│   ├── BreakerModel.cs
│   ├── CableModel.cs
│   ├── SettingsModel.cs
│   ├── ExportSettingsModel.cs
│   └── 📁 Enums/
│       ├── RequestId_PB6.cs
│       ├── PathModeEnum.cs
│       └── CircuitStatusEnum.cs
│
├── 📁 Services/ (Business Logic & Revit API)
│   ├── 📁 Interfaces/
│   │   ├── IRevitService.cs
│   │   ├── IDataService.cs
│   │   ├── IExportService.cs
│   │   ├── IImportService.cs
│   │   ├── ICalculationService.cs
│   │   └── IPathEditingService.cs
│   ├── RevitService.cs
│   ├── DataService.cs
│   ├── ExportService.cs
│   ├── ImportService.cs
│   ├── CalculationService.cs
│   └── PathEditingService.cs
│
├── 📁 Handlers/ (Modeless Architecture)
│   ├── ModelessMainWindowHandler.cs
│   ├── Request_PB6_Configure.cs
│   ├── RequestHandler_PB6.cs
│   └── 📁 SpecializedHandlers/
│       ├── CircuitEditHandler.cs
│       ├── DbEditHandler.cs
│       └── PathEditingHandler.cs
│
├── 📁 Converters/ ✅ (enhanced)
│   ├── BooleanToVisibilityConverter.cs ✅
│   ├── StatusColorConverter.cs ✅
│   ├── NumericFormatConverter.cs (new)
│   ├── CircuitStatusConverter.cs (new)
│   ├── PathModeConverter.cs (new)
│   └── ValidationErrorConverter.cs (new)
│
├── 📁 Helpers/ (Utility Classes)
│   ├── EditCircuitPathClicker_WPF.cs
│   ├── EditDBPathClicker_WPF.cs
│   ├── DataGridHelper.cs
│   ├── WindowHelper.cs
│   └── ValidationHelper.cs
│
├── 📁 Resources/ (WPF Resources)
│   ├── Styles/
│   │   ├── ButtonStyles.xaml
│   │   ├── DataGridStyles.xaml
│   │   └── WindowStyles.xaml
│   ├── Templates/
│   │   ├── DataTemplates.xaml
│   │   └── ControlTemplates.xaml
│   └── Images/
│       └── BecaLogoBlack.png ✅
│
└── 📁 Extensions/ (Extension Methods)
    ├── ObservableCollectionExtensions.cs
    ├── DataGridExtensions.cs
    └── ValidationExtensions.cs
```

## Required NuGet Packages

### ✅ **Already Installed:**
- Nice3point.Revit.* packages
- DocumentFormat.OpenXml
- Microsoft.Office.Interop.Excel

### 🔧 **Need to Install:**
```xml
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
<PackageReference Include="MaterialDesignColors" Version="2.1.4" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
```

## Core Architecture Components

### 1. Modeless Window Management

#### ModelessMainWindowHandler.cs
```csharp
public static class ModelessMainWindowHandler
{
    private static MainWindow _mainWindow;
    private static RequestHandler_PB6 _requestHandler;
    private static ExternalEvent _externalEvent;
    private static IServiceProvider _serviceProvider;
    
    public static void ShowWindow(List<PowerBIM_DBData> dbs, PowerBIM_ProjectInfo projInfo, BecaActivityLoggerData logger)
    {
        if (_mainWindow == null || !_mainWindow.IsLoaded)
        {
            // Initialize services
            var services = ConfigureServices(projInfo.UIDocument, logger);
            _serviceProvider = services.BuildServiceProvider();
            
            // Create modeless architecture
            _requestHandler = new RequestHandler_PB6(_serviceProvider);
            _externalEvent = ExternalEvent.Create(_requestHandler);
            
            // Create ViewModel with dependencies
            var viewModel = _serviceProvider.GetRequiredService<MainViewModel>();
            viewModel.Initialize(dbs, projInfo, _requestHandler, _externalEvent);
            
            // Create and show window
            _mainWindow = new MainWindow { DataContext = viewModel };
            _mainWindow.Show();
        }
        else
        {
            _mainWindow.Activate();
        }
    }
    
    private static IServiceCollection ConfigureServices(UIDocument uiDocument, BecaActivityLoggerData logger)
    {
        var services = new ServiceCollection();
        
        // Register services
        services.AddSingleton(uiDocument);
        services.AddSingleton(logger);
        services.AddTransient<IRevitService, RevitService>();
        services.AddTransient<IDataService, DataService>();
        services.AddTransient<IExportService, ExportService>();
        services.AddTransient<ICalculationService, CalculationService>();
        
        // Register ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<CircuitEditViewModel>();
        services.AddTransient<DbEditViewModel>();
        
        return services;
    }
}
```

### 2. Request Architecture

#### Request_PB6_Configure.cs
```csharp
public enum RequestId_PB6 : int
{
    None = 0,
    
    // Project Operations
    SaveProject = 1,
    SaveSettings = 2,
    CommitProjectInfo = 3,
    
    // Distribution Board Operations
    LoadDistributionBoards = 10,
    SaveDistributionBoard = 11,
    ImportDistributionBoardSettings = 12,
    
    // Circuit Operations
    UpdateCircuits = 20,
    SaveCircuitData = 21,
    RecalculateCircuits = 22,
    BulkEditLighting = 23,
    BulkEditPower = 24,
    
    // Path Editing
    OpenPathCustomizing = 30,
    ActivatePathEditView = 31,
    
    // Export Operations
    ExportData = 40,
    ExportCircuitImages = 41,
    
    // UI Operations
    WakeFormUp = 50,
    RefreshData = 51
}

public class Request_PB6_Configure
{
    private int _request = (int)RequestId_PB6.None;
    
    public void Make(RequestId_PB6 request)
    {
        Interlocked.Exchange(ref _request, (int)request);
    }
    
    public RequestId_PB6 Take()
    {
        return (RequestId_PB6)Interlocked.Exchange(ref _request, (int)RequestId_PB6.None);
    }
}
```

### 3. Base ViewModel with CommunityToolkit

#### BaseViewModel.cs
```csharp
public abstract partial class BaseViewModel : ObservableObject
{
    protected readonly IServiceProvider _serviceProvider;
    protected readonly RequestHandler_PB6 _requestHandler;
    protected readonly ExternalEvent _externalEvent;
    
    [ObservableProperty]
    private bool _isBusy;
    
    [ObservableProperty]
    private string _statusMessage = string.Empty;
    
    protected BaseViewModel(IServiceProvider serviceProvider, RequestHandler_PB6 requestHandler, ExternalEvent externalEvent)
    {
        _serviceProvider = serviceProvider;
        _requestHandler = requestHandler;
        _externalEvent = externalEvent;
    }
    
    protected void MakeRequest(RequestId_PB6 requestId)
    {
        IsBusy = true;
        _requestHandler.Request.Make(requestId);
        _externalEvent.Raise();
    }
    
    protected T GetService<T>() where T : class
    {
        return _serviceProvider.GetRequiredService<T>();
    }
    
    public virtual void WakeUp()
    {
        IsBusy = false;
        OnPropertyChanged(nameof(IsBusy));
    }
}
```

## Navigation Pattern Recommendations

### 🏠 **MainWindow (frmPowerBIM_Start)** → **Single-Window with Tabbed Interface**
**Rationale**: Main form has multiple functional areas (project settings, distribution board list, bulk operations)
- **Primary Tab**: Distribution Board Overview & Selection
- **Secondary Tab**: Project Settings & Configuration  
- **Action Panel**: Always visible with Save, Run, Export buttons
- **Status Bar**: Progress and status information

### 🔧 **CircuitEditWindow (FrmPowerBIM_CircuitEditEnhanced)** → **Modal Dialog with Advanced DataGrid**
**Rationale**: Complex data editing requires focused interaction
- **Modal Window**: Prevents context switching during critical edits
- **Advanced DataGrid**: Custom columns, search, filtering
- **Toolbar**: Path editing, bulk operations, save/cancel
- **Side Panel**: Circuit details and manual overrides

### 📊 **DbEditWindow (frmPowerBIM_DbEdit)** → **Modal Dialog with Import/Export**
**Rationale**: Distribution Board editing is a focused task
- **Modal Dialog**: Dedicated editing environment
- **Import Panel**: CSV import with matching interface
- **Main Grid**: Distribution Board information editing
- **Action Bar**: Save, Cancel, Import buttons

### ⚙️ **Settings Windows** → **Modal Dialogs**
**Rationale**: Settings are configuration tasks that should be completed before returning
- **AdvancedSettingsWindow**: Modal dialog with grouped settings
- **DbSettingsWindow**: Modal dialog for distribution board-specific settings
- **ImportSettingsWindow**: Modal dialog for import configuration

### 📤 **ExportWindow** → **Modal Dialog with Progress**
**Rationale**: Export is a task-oriented operation
- **Modal Dialog**: Focus on export configuration
- **Progress Indicator**: Show export progress
- **Preview Panel**: Show export preview if applicable

This architecture provides a clean separation of concerns while maintaining the proven modeless pattern for Revit integration.

## Detailed Modeless Window Management Architecture

### Core Modeless Components Integration

#### 1. ModelessMainWindowHandler - Enhanced Architecture
```csharp
public static class ModelessMainWindowHandler
{
    #region Fields
    private static MainWindow _mainWindow;
    private static RequestHandler_PB6 _requestHandler;
    private static ExternalEvent _externalEvent;
    private static IServiceProvider _serviceProvider;
    private static BecaActivityLoggerData _logger;

    // Static references for cross-form communication
    public static PowerBIM_ProjectInfo ProjectInfo { get; private set; }
    public static List<PowerBIM_DBData> AllDistributionBoards { get; private set; }
    #endregion

    #region Public Methods
    public static void ShowWindow(List<PowerBIM_DBData> dbs, PowerBIM_ProjectInfo projInfo, BecaActivityLoggerData logger)
    {
        // Singleton pattern - only one main window
        if (_mainWindow == null || !_mainWindow.IsLoaded)
        {
            InitializeServices(projInfo.UIDocument, logger);
            CreateModelessArchitecture();
            CreateMainWindow(dbs, projInfo);
        }
        else
        {
            _mainWindow.Activate();
            _mainWindow.WindowState = WindowState.Normal;
        }
    }

    public static void CloseWindow()
    {
        _mainWindow?.Close();
        _mainWindow = null;
        _externalEvent?.Dispose();
        _serviceProvider = null;
    }

    // Cross-form communication methods
    public static void ShowDbEditWindow(DistributionBoardModel distributionBoard)
    {
        var dbEditViewModel = _serviceProvider.GetRequiredService<DbEditViewModel>();
        dbEditViewModel.Initialize(distributionBoard);

        var dbEditWindow = new DbEditWindow { DataContext = dbEditViewModel };
        dbEditWindow.Owner = _mainWindow;
        dbEditWindow.ShowDialog();
    }

    public static void ShowCircuitEditWindow(DistributionBoardModel distributionBoard)
    {
        var circuitEditViewModel = _serviceProvider.GetRequiredService<CircuitEditViewModel>();
        circuitEditViewModel.Initialize(distributionBoard);

        var circuitEditWindow = new CircuitEditWindow { DataContext = circuitEditViewModel };
        circuitEditWindow.Owner = _mainWindow;
        circuitEditWindow.ShowDialog();
    }
    #endregion

    #region Private Methods
    private static void InitializeServices(UIDocument uiDocument, BecaActivityLoggerData logger)
    {
        _logger = logger;
        var services = new ServiceCollection();

        // Core services
        services.AddSingleton(uiDocument);
        services.AddSingleton(logger);

        // Business services
        services.AddTransient<IRevitService, RevitService>();
        services.AddTransient<IDataService, DataService>();
        services.AddTransient<IExportService, ExportService>();
        services.AddTransient<ICalculationService, CalculationService>();
        services.AddTransient<IPathEditingService, PathEditingService>();

        // ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<CircuitEditViewModel>();
        services.AddTransient<DbEditViewModel>();
        services.AddTransient<AdvancedSettingsViewModel>();
        services.AddTransient<ExportViewModel>();

        _serviceProvider = services.BuildServiceProvider();
    }

    private static void CreateModelessArchitecture()
    {
        _requestHandler = new RequestHandler_PB6(_serviceProvider, _logger);
        _externalEvent = ExternalEvent.Create(_requestHandler);
    }

    private static void CreateMainWindow(List<PowerBIM_DBData> dbs, PowerBIM_ProjectInfo projInfo)
    {
        ProjectInfo = projInfo;
        AllDistributionBoards = dbs;

        var mainViewModel = _serviceProvider.GetRequiredService<MainViewModel>();
        mainViewModel.Initialize(dbs, projInfo, _requestHandler, _externalEvent);

        _mainWindow = new MainWindow
        {
            DataContext = mainViewModel,
            WindowStartupLocation = WindowStartupLocation.CenterScreen
        };

        // Handle window closing
        _mainWindow.Closed += (s, e) => CloseWindow();

        _mainWindow.Show();
    }
    #endregion
}
```

#### 2. RequestHandler_PB6 - Enhanced with Dependency Injection
```csharp
public class RequestHandler_PB6 : IExternalEventHandler
{
    #region Fields
    private readonly IServiceProvider _serviceProvider;
    private readonly BecaActivityLoggerData _logger;
    private readonly Request_PB6_Configure _request;

    // Specialized handlers for complex operations
    private readonly EditCircuitPathClicker_WPF _circuitPathClicker;
    private readonly EditDBPathClicker_WPF _dbPathClicker;
    #endregion

    #region Properties
    public Request_PB6_Configure Request => _request;
    #endregion

    #region Constructor
    public RequestHandler_PB6(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _request = new Request_PB6_Configure();

        var uiDocument = serviceProvider.GetRequiredService<UIDocument>();
        _circuitPathClicker = new EditCircuitPathClicker_WPF(uiDocument.Application);
        _dbPathClicker = new EditDBPathClicker_WPF(uiDocument.Application);
    }
    #endregion

    #region IExternalEventHandler Implementation
    public string GetName() => "PowerBIM 6 Request Handler";

    public void Execute(UIApplication uiapp)
    {
        try
        {
            var requestId = _request.Take();

            switch (requestId)
            {
                case RequestId_PB6.None:
                    return;

                case RequestId_PB6.SaveProject:
                    HandleSaveProject();
                    break;

                case RequestId_PB6.UpdateCircuits:
                    HandleUpdateCircuits();
                    break;

                case RequestId_PB6.OpenPathCustomizing:
                    HandleOpenPathCustomizing();
                    break;

                case RequestId_PB6.ExportData:
                    HandleExportData();
                    break;

                // ... other cases

                default:
                    _logger.LogWarning($"Unhandled request: {requestId}");
                    break;
            }

            // Always wake up the UI
            WakeUpMainWindow();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Request execution failed: {ex.Message}");
            WakeUpMainWindow();
        }
    }
    #endregion

    #region Request Handlers
    private void HandleSaveProject()
    {
        var revitService = _serviceProvider.GetRequiredService<IRevitService>();
        var projectInfo = ModelessMainWindowHandler.ProjectInfo;

        using var trans = new Transaction(projectInfo.Document, "Save Project Settings");
        trans.Start();

        // Save project information
        projectInfo.CommitProjectInfo();

        trans.Commit();
    }

    private void HandleUpdateCircuits()
    {
        var calculationService = _serviceProvider.GetRequiredService<ICalculationService>();
        var distributionBoards = ModelessMainWindowHandler.AllDistributionBoards;

        foreach (var db in distributionBoards.Where(d => !d.IsManuallyLocked))
        {
            calculationService.RecalculateDistributionBoard(db);
        }
    }

    private void HandleOpenPathCustomizing()
    {
        // Use specialized path editing handler
        var pathEditingService = _serviceProvider.GetRequiredService<IPathEditingService>();
        pathEditingService.OpenPathCustomizingView();
    }

    private void HandleExportData()
    {
        var exportService = _serviceProvider.GetRequiredService<IExportService>();
        // Export logic here
    }

    private void WakeUpMainWindow()
    {
        Application.Current?.Dispatcher.Invoke(() =>
        {
            if (ModelessMainWindowHandler._mainWindow?.DataContext is BaseViewModel viewModel)
            {
                viewModel.WakeUp();
            }
        });
    }
    #endregion
}
```

### Thread Safety and UI Updates

#### Safe UI Updates Pattern
```csharp
public abstract partial class BaseViewModel : ObservableObject
{
    protected void SafeUIUpdate(Action updateAction)
    {
        if (Application.Current?.Dispatcher.CheckAccess() == true)
        {
            updateAction();
        }
        else
        {
            Application.Current?.Dispatcher.Invoke(updateAction);
        }
    }

    protected async Task SafeUIUpdateAsync(Action updateAction)
    {
        await Application.Current.Dispatcher.InvokeAsync(updateAction);
    }
}
```

This enhanced modeless architecture maintains the proven ExternalEvent pattern while adding modern dependency injection and proper separation of concerns.

## Detailed Navigation Pattern Analysis & Recommendations

### 🏠 **MainWindow (frmPowerBIM_Start)** - **Single-Window with Material Design Tabs**

#### **Current WinForms Structure Analysis:**
- Complex form with multiple grouped controls (project settings, distribution board list, bulk operations)
- TabControl with Project Parameters, Advanced Settings, DB Settings
- DataGridView for distribution board selection and status
- Multiple action buttons (Save, Run, Export, etc.)

#### **Recommended WPF Pattern: Single-Window with Enhanced Tabbed Interface**
```xml
<Window x:Class="MEP.PowerBIM_6.Views.MainWindow">
    <materialDesign:DialogHost>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/> <!-- Header -->
                <RowDefinition Height="*"/>    <!-- Main Content -->
                <RowDefinition Height="Auto"/> <!-- Status Bar -->
            </Grid.RowDefinitions>

            <!-- Header with Logo and Title -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <StackPanel Orientation="Horizontal">
                    <Image Source="../Resources/Images/BecaLogoBlack.png" Height="32"/>
                    <TextBlock Text="PowerBIM 6" Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                </StackPanel>
            </materialDesign:ColorZone>

            <!-- Main Tabbed Interface -->
            <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignNavigationRailTabControl}">
                <TabItem Header="Distribution Board Overview">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Distribution Board List with Status -->
                        <DataGrid Grid.Column="0" ItemsSource="{Binding DistributionBoards}"/>

                        <!-- Action Panel -->
                        <StackPanel Grid.Column="1" Margin="16">
                            <Button Content="Run Calculations" Command="{Binding RunCalculationsCommand}"/>
                            <Button Content="Enhanced Circuit Edit" Command="{Binding OpenCircuitEditCommand}"/>
                            <Button Content="Export Data" Command="{Binding ExportDataCommand}"/>
                        </StackPanel>
                    </Grid>
                </TabItem>

                <TabItem Header="Project Settings">
                    <!-- Project configuration controls -->
                </TabItem>

                <TabItem Header="Bulk Operations">
                    <!-- Bulk editing controls -->
                </TabItem>
            </TabControl>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2">
                <TextBlock Text="{Binding StatusMessage}"/>
                <ProgressBar Value="{Binding Progress}" Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisConverter}}"/>
            </StatusBar>
        </Grid>
    </materialDesign:DialogHost>
</Window>
```

**Benefits:**
- ✅ Maintains familiar workflow while modernizing UI
- ✅ Material Design provides professional appearance
- ✅ Tabbed interface reduces cognitive load
- ✅ Responsive layout adapts to different screen sizes
- ✅ Status bar provides clear feedback

---

### 🔧 **CircuitEditWindow (FrmPowerBIM_CircuitEditEnhanced)** - **Modal Dialog with Advanced DataGrid**

#### **Current WinForms Structure Analysis:**
- Most complex form with advanced DataGridView (35+ columns)
- Search functionality with AdvancedDataGridViewSearchToolBar
- Custom column types (buttons, dropdowns, checkboxes)
- Path editing integration with Revit 3D views
- Real-time calculations and validation

#### **Recommended WPF Pattern: Modal Dialog with Modern DataGrid**
```xml
<Window x:Class="MEP.PowerBIM_6.Views.CircuitEditWindow"
        WindowState="Maximized"
        WindowStyle="SingleBorderWindow">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="Auto"/> <!-- Search -->
            <RowDefinition Height="*"/>    <!-- DataGrid -->
            <RowDefinition Height="Auto"/> <!-- Actions -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBarTray Grid.Row="0">
            <ToolBar>
                <Button Content="Save" Command="{Binding SaveCommand}"/>
                <Button Content="Run Calculations" Command="{Binding RecalculateCommand}"/>
                <Separator/>
                <Button Content="Activate Path Edit" Command="{Binding ActivatePathEditCommand}"/>
                <Button Content="Bulk Edit" Command="{Binding BulkEditCommand}"/>
            </ToolBar>
        </ToolBarTray>

        <!-- Search Bar -->
        <materialDesign:Card Grid.Row="1" Margin="8">
            <TextBox materialDesign:HintAssist.Hint="Search circuits..."
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
        </materialDesign:Card>

        <!-- Advanced DataGrid -->
        <DataGrid Grid.Row="2"
                  ItemsSource="{Binding FilteredCircuits}"
                  SelectedItem="{Binding SelectedCircuit}"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False">
            <DataGrid.Columns>
                <!-- Circuit Number -->
                <DataGridTextColumn Header="Circuit" Binding="{Binding CircuitNumber}" IsReadOnly="True"/>

                <!-- Description -->
                <DataGridTextColumn Header="Description" Binding="{Binding Description}"/>

                <!-- Load -->
                <DataGridTextColumn Header="Load (VA)" Binding="{Binding Load, StringFormat=N2}"/>

                <!-- Manual Override -->
                <DataGridCheckBoxColumn Header="Manual" Binding="{Binding IsManualOverride}"/>

                <!-- Path Edit Button -->
                <DataGridTemplateColumn Header="Path">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="Edit Path"
                                    Command="{Binding DataContext.EditPathCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                    CommandParameter="{Binding}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>

                <!-- Status with Color Coding -->
                <DataGridTemplateColumn Header="Status">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{Binding Status, Converter={StaticResource StatusColorConverter}}"
                                    CornerRadius="4" Padding="8,4">
                                <TextBlock Text="{Binding Status}" Foreground="White" HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="8">
            <Button Content="Cancel" Command="{Binding CancelCommand}" Margin="4"/>
            <Button Content="Save & Close" Command="{Binding SaveAndCloseCommand}" Margin="4"/>
        </StackPanel>
    </Grid>
</Window>
```

**Benefits:**
- ✅ Maximized window provides space for complex data
- ✅ Modern search replaces AdvancedDataGridViewSearchToolBar
- ✅ Template columns provide rich UI elements
- ✅ Toolbar provides quick access to common actions
- ✅ Modal dialog maintains focus during critical edits

---

### 📊 **DbEditWindow (frmPowerBIM_DbEdit)** - **Modal Dialog with Import Panel**

#### **Current WinForms Structure Analysis:**
- Distribution Board information editing with DataGridView
- CSV import functionality with matching interface
- Path editing integration
- Save/Cancel workflow

#### **Recommended WPF Pattern: Modal Dialog with Side Panel**
```xml
<Window x:Class="MEP.PowerBIM_6.Views.DbEditWindow"
        Width="1000" Height="600"
        WindowStartupLocation="CenterOwner">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Main Distribution Board Grid -->
        <materialDesign:Card Grid.Column="0" Margin="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="Distribution Board Information"
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="16"/>

                <DataGrid Grid.Row="1" ItemsSource="{Binding DistributionBoardItems}" Margin="16"/>
            </Grid>
        </materialDesign:Card>

        <!-- Side Panel for Import/Actions -->
        <materialDesign:Card Grid.Column="1" Margin="8">
            <StackPanel Margin="16">
                <TextBlock Text="Actions" Style="{StaticResource MaterialDesignHeadline6TextBlock}"/>

                <Button Content="Import from CSV" Command="{Binding ImportCsvCommand}" Margin="0,8"/>
                <Button Content="Activate Path Edit" Command="{Binding ActivatePathEditCommand}" Margin="0,8"/>

                <Separator Margin="0,16"/>

                <TextBlock Text="Import Settings" Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                <!-- Import configuration controls -->

                <Separator Margin="0,16"/>

                <Button Content="Save Changes" Command="{Binding SaveCommand}" Margin="0,8"/>
                <Button Content="Cancel" Command="{Binding CancelCommand}" Margin="0,8"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
```

**Benefits:**
- ✅ Side panel organizes actions logically
- ✅ Main area focuses on data editing
- ✅ Import functionality is easily accessible
- ✅ Clear save/cancel workflow

---

### ⚙️ **Settings Windows** - **Modal Dialogs with Grouped Controls**

#### **AdvancedSettingsWindow & DbSettingsWindow**
```xml
<Window x:Class="MEP.PowerBIM_6.Views.AdvancedSettingsWindow"
        Width="600" Height="500"
        WindowStartupLocation="CenterOwner">
    <ScrollViewer>
        <StackPanel Margin="16">
            <!-- Grouped Settings -->
            <Expander Header="Calculation Settings" IsExpanded="True">
                <StackPanel Margin="16">
                    <!-- Settings controls -->
                </StackPanel>
            </Expander>

            <Expander Header="Display Settings" IsExpanded="False">
                <StackPanel Margin="16">
                    <!-- Settings controls -->
                </StackPanel>
            </Expander>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                <Button Content="Reset to Defaults" Command="{Binding ResetCommand}" Margin="4"/>
                <Button Content="Cancel" Command="{Binding CancelCommand}" Margin="4"/>
                <Button Content="Apply" Command="{Binding ApplyCommand}" Margin="4"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Window>
```

**Benefits:**
- ✅ Expandable groups organize complex settings
- ✅ ScrollViewer handles overflow gracefully
- ✅ Standard Apply/Cancel pattern

---

### 📤 **ExportWindow** - **Modal Dialog with Progress**

#### **Recommended Pattern: Wizard-Style Dialog**
```xml
<Window x:Class="MEP.PowerBIM_6.Views.ExportWindow"
        Width="500" Height="400"
        WindowStartupLocation="CenterOwner">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Export Configuration -->
        <TabControl Grid.Row="0" SelectedIndex="{Binding CurrentStep}">
            <TabItem Header="Select Data">
                <!-- Data selection controls -->
            </TabItem>
            <TabItem Header="Configure Export">
                <!-- Export settings -->
            </TabItem>
            <TabItem Header="Export Progress">
                <StackPanel VerticalAlignment="Center">
                    <ProgressBar Value="{Binding ExportProgress}" Height="20" Margin="16"/>
                    <TextBlock Text="{Binding ExportStatus}" HorizontalAlignment="Center" Margin="16"/>
                </StackPanel>
            </TabItem>
        </TabControl>

        <!-- Navigation Buttons -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="16">
            <Button Content="Previous" Command="{Binding PreviousCommand}" Margin="4"/>
            <Button Content="Next" Command="{Binding NextCommand}" Margin="4"/>
            <Button Content="Export" Command="{Binding ExportCommand}" Margin="4"/>
            <Button Content="Close" Command="{Binding CloseCommand}" Margin="4"/>
        </StackPanel>
    </Grid>
</Window>
```

**Benefits:**
- ✅ Wizard pattern guides user through export process
- ✅ Progress indication provides feedback
- ✅ Step-by-step approach reduces complexity

## Architectural Solutions for Identified Coupling Issues

### 🔧 **Issue 1: Direct DataGridView Manipulation**

#### **Problem in WinForms:**
```csharp
// Direct UI manipulation mixed with business logic
private void UpdateDBSummary(string result)
{
    dgvDBSel.Rows.Clear();
    foreach (var db in AllDBs)
    {
        var row = dgvDBSel.Rows.Add();
        row.Cells[0].Value = db.Schedule_DB_Name;
        row.Cells[1].Value = db.PassCount;
        // Business logic mixed with UI updates
        if (db.PassCount > 0)
            row.DefaultCellStyle.BackColor = Color.Green;
    }
}
```

#### **MVVM Solution:**
```csharp
// ViewModel with proper separation
public partial class MainViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<DistributionBoardItemViewModel> _distributionBoards;

    [RelayCommand]
    private async Task UpdateDistributionBoardSummary()
    {
        var dataService = GetService<IDataService>();
        var updatedData = await dataService.RefreshDistributionBoardSummaryAsync();

        // Update collection - UI updates automatically via binding
        DistributionBoards.Clear();
        foreach (var db in updatedData)
        {
            DistributionBoards.Add(new DistributionBoardItemViewModel(db));
        }
    }
}

// Separate ViewModel for each distribution board item
public partial class DistributionBoardItemViewModel : ObservableObject
{
    [ObservableProperty]
    private string _name;

    [ObservableProperty]
    private int _passCount;

    [ObservableProperty]
    private Brush _statusColor;

    public DistributionBoardItemViewModel(PowerBIM_DBData dbData)
    {
        Name = dbData.Schedule_DB_Name;
        PassCount = dbData.PassCount;
        StatusColor = PassCount > 0 ? Brushes.Green : Brushes.Red;
    }
}
```

---

### 🔧 **Issue 2: Form-to-Form Communication via Static Handlers**

#### **Problem in WinForms:**
```csharp
// Tight coupling through static handlers
public static class ModelessPowerBIM_StartFormHandler
{
    static frmPowerBIM_Start _startForm;

    public static void ShowMsgToTheUser(string title, string message)
    {
        MessageBox.Show(message, title);
    }

    public static void UpdaterRequired_All()
    {
        _startForm?.UpdaterRequired_All();
    }
}
```

#### **MVVM Solution with Dependency Injection:**
```csharp
// Service-based communication
public interface IDialogService
{
    Task<bool> ShowMessageAsync(string title, string message);
    Task<bool> ShowConfirmationAsync(string title, string message);
    void ShowWindow<TViewModel>(TViewModel viewModel) where TViewModel : BaseViewModel;
}

public class DialogService : IDialogService
{
    public async Task<bool> ShowMessageAsync(string title, string message)
    {
        return await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var dialog = new MessageDialog { Title = title, Message = message };
            return dialog.ShowDialog() == true;
        });
    }

    public void ShowWindow<TViewModel>(TViewModel viewModel) where TViewModel : BaseViewModel
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            Window window = viewModel switch
            {
                CircuitEditViewModel => new CircuitEditWindow(),
                DbEditViewModel => new DbEditWindow(),
                _ => throw new ArgumentException($"No window registered for {typeof(TViewModel)}")
            };

            window.DataContext = viewModel;
            window.Show();
        });
    }
}

// Usage in ViewModel
public partial class MainViewModel : BaseViewModel
{
    [RelayCommand]
    private async Task OpenCircuitEdit()
    {
        var dialogService = GetService<IDialogService>();
        var circuitEditViewModel = GetService<CircuitEditViewModel>();

        circuitEditViewModel.Initialize(SelectedDistributionBoard);
        dialogService.ShowWindow(circuitEditViewModel);
    }
}
```

---

### 🔧 **Issue 3: Scattered UI State Management**

#### **Problem in WinForms:**
```csharp
// State scattered across multiple forms
private bool _isValuesUnsaved;
private bool _requestToCloseForm;
private bool _isloading;

private void btnSave_Click(object sender, EventArgs e)
{
    _isValuesUnsaved = false;
    // State management mixed with event handling
}
```

#### **MVVM Solution with Centralized State:**
```csharp
// Centralized state management
public partial class BaseViewModel : ObservableObject
{
    [ObservableProperty]
    private bool _isBusy;

    [ObservableProperty]
    private bool _hasUnsavedChanges;

    [ObservableProperty]
    private string _statusMessage = string.Empty;

    [ObservableProperty]
    private bool _isValid = true;

    protected void SetBusyState(bool isBusy, string message = "")
    {
        IsBusy = isBusy;
        StatusMessage = message;
    }

    protected void MarkAsChanged()
    {
        HasUnsavedChanges = true;
    }

    protected void MarkAsSaved()
    {
        HasUnsavedChanges = false;
    }
}

// State-aware commands
public partial class CircuitEditViewModel : BaseViewModel
{
    [RelayCommand(CanExecute = nameof(CanSave))]
    private async Task Save()
    {
        SetBusyState(true, "Saving changes...");

        try
        {
            var dataService = GetService<IDataService>();
            await dataService.SaveCircuitDataAsync(Circuits);
            MarkAsSaved();
        }
        finally
        {
            SetBusyState(false);
        }
    }

    private bool CanSave() => HasUnsavedChanges && IsValid && !IsBusy;
}
```

---

### 🔧 **Issue 4: WinForms-Specific Event Patterns**

#### **Problem in WinForms:**
```csharp
// Event-driven architecture with tight coupling
private void dgvDBSel_CellClick(object sender, DataGridViewCellEventArgs e)
{
    if (e.ColumnIndex == 0) // Hardcoded column index
    {
        var selectedDB = AllDBs[e.RowIndex];
        // Direct manipulation
        OpenCircuitEdit(selectedDB);
    }
}
```

#### **MVVM Solution with Commands and Binding:**
```csharp
// Command-based interaction
public partial class MainViewModel : BaseViewModel
{
    [ObservableProperty]
    private DistributionBoardItemViewModel _selectedDistributionBoard;

    [RelayCommand(CanExecute = nameof(CanOpenCircuitEdit))]
    private void OpenCircuitEdit()
    {
        var circuitEditViewModel = GetService<CircuitEditViewModel>();
        circuitEditViewModel.Initialize(SelectedDistributionBoard.DistributionBoardData);

        var dialogService = GetService<IDialogService>();
        dialogService.ShowWindow(circuitEditViewModel);
    }

    private bool CanOpenCircuitEdit() => SelectedDistributionBoard != null && !SelectedDistributionBoard.IsLocked;

    // Property change automatically updates command state
    partial void OnSelectedDistributionBoardChanged(DistributionBoardItemViewModel value)
    {
        OpenCircuitEditCommand.NotifyCanExecuteChanged();
    }
}
```

```xml
<!-- XAML with proper binding -->
<DataGrid ItemsSource="{Binding DistributionBoards}"
          SelectedItem="{Binding SelectedDistributionBoard}">
    <DataGrid.Columns>
        <DataGridTemplateColumn Header="Actions">
            <DataGridTemplateColumn.CellTemplate>
                <DataTemplate>
                    <Button Content="Edit Circuits"
                            Command="{Binding DataContext.OpenCircuitEditCommand,
                                     RelativeSource={RelativeSource AncestorType=DataGrid}}"
                            IsEnabled="{Binding IsNotLocked}"/>
                </DataTemplate>
            </DataGridTemplateColumn.CellTemplate>
        </DataGridTemplateColumn>
    </DataGrid.Columns>
</DataGrid>
```

---

### 🔧 **Issue 5: Manual DataSet/DataTable Binding**

#### **Problem in WinForms:**
```csharp
// Manual data binding with DataSets
private void PopulateDataGrid()
{
    var dataTable = new DataTable();
    dataTable.Columns.Add("CircuitNumber", typeof(string));
    dataTable.Columns.Add("Load", typeof(double));

    foreach (var circuit in circuits)
    {
        var row = dataTable.NewRow();
        row["CircuitNumber"] = circuit.CCT_Number;
        row["Load"] = circuit.Load;
        dataTable.Rows.Add(row);
    }

    dgvCircuits.DataSource = dataTable;
}
```

#### **MVVM Solution with ObservableCollection:**
```csharp
// Modern data binding with ObservableCollection
public partial class CircuitEditViewModel : BaseViewModel
{
    [ObservableProperty]
    private ObservableCollection<CircuitItemViewModel> _circuits;

    [ObservableProperty]
    private string _searchText = string.Empty;

    public ICollectionView FilteredCircuits { get; private set; }

    public CircuitEditViewModel()
    {
        Circuits = new ObservableCollection<CircuitItemViewModel>();
        FilteredCircuits = CollectionViewSource.GetDefaultView(Circuits);
        FilteredCircuits.Filter = FilterCircuits;
    }

    private bool FilterCircuits(object item)
    {
        if (string.IsNullOrEmpty(SearchText)) return true;

        var circuit = item as CircuitItemViewModel;
        return circuit?.CircuitNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true;
    }

    partial void OnSearchTextChanged(string value)
    {
        FilteredCircuits.Refresh();
    }

    public void LoadCircuits(List<PowerBIM_CircuitData> circuitData)
    {
        Circuits.Clear();
        foreach (var circuit in circuitData)
        {
            Circuits.Add(new CircuitItemViewModel(circuit));
        }
    }
}

// Individual circuit item with change tracking
public partial class CircuitItemViewModel : ObservableObject
{
    [ObservableProperty]
    private string _circuitNumber;

    [ObservableProperty]
    private double _load;

    [ObservableProperty]
    private bool _isManualOverride;

    private readonly PowerBIM_CircuitData _originalData;

    public CircuitItemViewModel(PowerBIM_CircuitData circuitData)
    {
        _originalData = circuitData;
        CircuitNumber = circuitData.CCT_Number;
        Load = circuitData.Load;
        IsManualOverride = circuitData.IsManualOverride;
    }

    // Automatic change notification
    partial void OnLoadChanged(double value)
    {
        _originalData.Load = value;
        // Trigger recalculation if needed
    }
}
```

This architectural approach eliminates the identified coupling issues while maintaining all existing functionality through proper MVVM patterns and dependency injection.
