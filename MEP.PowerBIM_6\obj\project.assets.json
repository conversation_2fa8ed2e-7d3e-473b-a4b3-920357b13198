{"version": 3, "targets": {".NETFramework,Version=v4.8": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["CommunityToolkit.Mvvm >= 8.2.2", "DocumentFormat.OpenXml >= 3.0.1", "MaterialDesignColors >= 2.1.4", "MaterialDesignThemes >= 4.9.0", "Microsoft.Extensions.DependencyInjection >= 8.0.0", "Microsoft.Extensions.Hosting >= 8.0.0", "Microsoft.Extensions.Logging >= 8.0.0", "Microsoft.Office.Interop.Excel >= 15.0.4795.1001", "Nice3point.Revit.Api.RevitAPI >= 2024.*", "Nice3point.Revit.Api.RevitAPIUI >= 2024.*", "Nice3point.Revit.Build.Tasks >= 2.0.2", "Nice3point.Revit.Extensions >= 2024.*", "Nice3point.Revit.Toolkit >= 2024.*", "System.Resources.Extensions >= 8.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\PB6 WPF Conversion\\MEP.PowerBIM_6\\MEP.PowerBIM_6.csproj", "projectName": "MEP.PowerBIM_6", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\PB6 WPF Conversion\\MEP.PowerBIM_6\\MEP.PowerBIM_6.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\PB6 WPF Conversion\\MEP.PowerBIM_6\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[3.0.1, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Office.Interop.Excel": {"target": "Package", "version": "[15.0.4795.1001, )"}, "Nice3point.Revit.Api.RevitAPI": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Api.RevitAPIUI": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Build.Tasks": {"target": "Package", "version": "[2.0.2, )"}, "Nice3point.Revit.Extensions": {"target": "Package", "version": "[2024.*, )"}, "Nice3point.Revit.Toolkit": {"target": "Package", "version": "[2024.*, )"}, "System.Resources.Extensions": {"target": "Package", "version": "[8.0.0, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.401\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "CommunityToolkit.Mvvm"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Microsoft.Extensions.Logging"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Microsoft.Extensions.Hosting"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Nice3point.Revit.Toolkit"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Nice3point.Revit.Extensions"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Nice3point.Revit.Api.RevitAPI"}, {"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://pkgs.dev.azure.com/BecaCAD/_packaging/RevitSDKs%40Local/nuget/v3/index.json.", "libraryId": "Nice3point.Revit.Api.RevitAPIUI"}]}