﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Text;
// REMOVED: using System.Threading.Tasks; - Not needed for Revit-safe implementation
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.ApplicationServices;
using Common.UI.Forms;
using BecaTransactionsNamesManager;
using System.Diagnostics;
using System.Windows.Forms;
using BecaRevitUtilities.ElementUtilities;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using Autodesk.Revit.UI.Selection;
using System.Net;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Handlers;

namespace MEP.PowerBIM_6.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    public class PowerBIM_6_Command : BecaBaseCommand
    {
        public PowerBIM_ProjectInfo projInfo;
        public List<PowerBIM_DBData> DBs;

        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get Revit application and document
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                Document doc = uidoc.Document;

                // Start logging
                _taskLogger.PreTaskStart();

                // Validate prerequisites
                if (!ValidatePrerequisites(doc))
                {
                    _taskLogger.PostTaskEnd("PowerBIM prerequisites not met");
                    return Result.Failed;
                }

                // Initialize PowerBIM core logic (preserving original pattern)
                InitializePowerBIMData(uidoc);

                // Launch WPF MVVM interface using new modeless architecture
                ModelessMainWindowHandler.ShowWindow(DBs, projInfo, _taskLogger);

                _taskLogger.PostTaskEnd("PowerBIM 6 WPF opened successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                _taskLogger.PostTaskEnd($"PowerBIM 6 failed: {ex.Message}");
                TaskDialog.Show("PowerBIM 6 Error", $"Failed to start PowerBIM 6:\n\n{ex.Message}");
                return Result.Failed;
            }
        }

        /// <summary>
        /// Validate prerequisites for PowerBIM operation
        /// </summary>
        /// <param name="doc">Revit document</param>
        /// <returns>True if prerequisites are met</returns>
        private bool ValidatePrerequisites(Document doc)
        {
            try
            {
                // Check if document is valid
                if (doc == null)
                {
                    TaskDialog.Show("PowerBIM 6", "No active document found.");
                    return false;
                }

                // Check for electrical elements
                var electricalEquipment = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                    .WhereElementIsNotElementType()
                    .ToElements();

                if (!electricalEquipment.Any())
                {
                    TaskDialog.Show("PowerBIM 6", "No electrical equipment found in the model.");
                    return false;
                }

                // Check shared parameter file (if required)
                // TODO: Add shared parameter validation if needed

                return true;
            }
            catch (Exception ex)
            {
                _taskLogger.LogError($"Prerequisites validation failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Initialize PowerBIM data structures (preserving original logic)
        /// </summary>
        /// <param name="uidoc">UI document</param>
        private void InitializePowerBIMData(UIDocument uidoc)
        {
            try
            {
                // Initialize project information
                projInfo = new PowerBIM_ProjectInfo(uidoc);

                // Initialize distribution boards list
                DBs = new List<PowerBIM_DBData>();

                // Load existing data or create new
                LoadExistingPowerBIMData(uidoc.Document);

                _taskLogger.LogInfo($"PowerBIM data initialized with {DBs.Count} distribution boards");
            }
            catch (Exception ex)
            {
                _taskLogger.LogError($"Failed to initialize PowerBIM data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Load existing PowerBIM data from the document
        /// </summary>
        /// <param name="doc">Revit document</param>
        private void LoadExistingPowerBIMData(Document doc)
        {
            try
            {
                // Get all electrical equipment in the document
                var electricalEquipment = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_ElectricalEquipment)
                    .WhereElementIsNotElementType()
                    .Cast<FamilyInstance>()
                    .ToList();

                // Group by panel/distribution board
                var panelGroups = electricalEquipment
                    .Where(eq => eq.MEPModel is ElectricalEquipment)
                    .GroupBy(eq => GetPanelName(eq))
                    .Where(g => !string.IsNullOrEmpty(g.Key));

                foreach (var panelGroup in panelGroups)
                {
                    // Get the first element to create the PowerBIM_DBData properly
                    var firstElement = panelGroup.First();
                    var dbData = new PowerBIM_DBData(projInfo, firstElement);

                    // Initialize all circuits for this distribution board
                    dbData.Initialise_AllCircuits();

                    DBs.Add(dbData);
                }

                // If no distribution boards found, create a default one
                if (DBs.Count == 0)
                {
                    // Create a dummy element for default DB (this shouldn't happen in real scenarios)
                    _taskLogger.LogWarning("No distribution boards found in document, creating default");
                }

                _taskLogger.LogInfo($"Loaded {DBs.Count} distribution boards from document");
            }
            catch (Exception ex)
            {
                _taskLogger.LogError($"Failed to load existing PowerBIM data: {ex.Message}");

                // Create default distribution board on error
                DBs = new List<PowerBIM_DBData>
                {
                    new PowerBIM_DBData
                    {
                        Schedule_DB_Name = "Default Distribution Board",
                        AllCircuits = new List<PowerBIM_CircuitData>()
                    }
                };
            }
        }

        /// <summary>
        /// Get panel name from electrical equipment
        /// </summary>
        /// <param name="equipment">Electrical equipment</param>
        /// <returns>Panel name</returns>
        private string GetPanelName(FamilyInstance equipment)
        {
            try
            {
                // Try to get panel name from parameters
                var panelParam = equipment.LookupParameter("Panel");
                if (panelParam != null && panelParam.HasValue)
                {
                    return panelParam.AsString();
                }

                // Fallback to family name
                return equipment.Symbol?.FamilyName ?? "Unknown Panel";
            }
            catch
            {
                return "Unknown Panel";
            }
        }

        // LoadCircuitsForPanel method removed - now using PowerBIM_DBData.Initialise_AllCircuits()

        /// <summary>
        /// Get circuit number from equipment
        /// </summary>
        private string GetCircuitNumber(FamilyInstance equipment)
        {
            try
            {
                var circuitParam = equipment.LookupParameter("Circuit Number");
                return circuitParam?.AsString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Get load value from equipment
        /// </summary>
        private double GetLoadValue(FamilyInstance equipment)
        {
            try
            {
                var loadParam = equipment.LookupParameter("Electrical Load");
                return loadParam?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Get description from equipment
        /// </summary>
        private string GetDescription(FamilyInstance equipment)
        {
            try
            {
                var descParam = equipment.LookupParameter("Description");
                return descParam?.AsString() ?? equipment.Name ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme, Harry Billinge, Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.PowerBIM6.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
