using System;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing project information for WPF binding
    /// Wraps PowerBIM_ProjectInfo with ObservableObject for MVVM
    /// </summary>
    public partial class ProjectInfoModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_ProjectInfo _originalData;

        #endregion

        #region Observable Properties

        [ObservableProperty]
        private string _jobName = string.Empty;

        [ObservableProperty]
        private string _jobNumber = string.Empty;

        [ObservableProperty]
        private string _engineer = string.Empty;

        [ObservableProperty]
        private string _verifier = string.Empty;

        [ObservableProperty]
        private double _systemVoltage = 415.0;

        [ObservableProperty]
        private double _systemVoltageDropMax = 5.0;

        [ObservableProperty]
        private int _ambientTemperature = 30;

        [ObservableProperty]
        private double _clearingTimePower = 0.4;

        [ObservableProperty]
        private double _clearingTimeLighting = 5.0;

        [ObservableProperty]
        private double _clearingTimeOther = 0.4;

        [ObservableProperty]
        private bool _voltageDropCalcPower = true;

        [ObservableProperty]
        private bool _voltageDropCalcLighting = true;

        [ObservableProperty]
        private bool _voltageDropCalcOther = true;

        [ObservableProperty]
        private bool _gpoCalc = true;

        [ObservableProperty]
        private double _lengthExtraPerCctPower = 0.0;

        [ObservableProperty]
        private double _lengthExtraPerElementPower = 0.0;

        [ObservableProperty]
        private double _lengthExtraPerCctLighting = 0.0;

        [ObservableProperty]
        private double _lengthExtraPerElementLighting = 0.0;

        [ObservableProperty]
        private double _lengthExtraPerCctOther = 0.0;

        [ObservableProperty]
        private double _lengthExtraPerElementOther = 0.0;

        [ObservableProperty]
        private bool _hasErrors;

        [ObservableProperty]
        private string _errorMessage = string.Empty;

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_ProjectInfo
        /// </summary>
        public PowerBIM_ProjectInfo OriginalData => _originalData;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the ProjectInfoModel with PowerBIM_ProjectInfo
        /// </summary>
        /// <param name="projectInfo">Original project information data</param>
        public ProjectInfoModel(PowerBIM_ProjectInfo projectInfo)
        {
            _originalData = projectInfo ?? throw new ArgumentNullException(nameof(projectInfo));

            // Initialize properties from original data
            LoadFromOriginalData();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_ProjectInfo
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                JobName = _originalData.JobName ?? string.Empty;
                JobNumber = _originalData.JobNumber ?? string.Empty;
                Engineer = _originalData.Engineer ?? string.Empty;
                Verifier = _originalData.Verifier ?? string.Empty;
                SystemVoltage = _originalData.SystemVoltage;
                SystemVoltageDropMax = _originalData.SystemVoltageDropMax;
                AmbientTemperature = _originalData.AmbientTemperature;
                ClearingTimePower = _originalData.ClearingTimePower;
                ClearingTimeLighting = _originalData.ClearingTimeLighting;
                ClearingTimeOther = _originalData.ClearingTimeOther;
                VoltageDropCalcPower = _originalData.VoltageDropCalcPower;
                VoltageDropCalcLighting = _originalData.VoltageDropCalcLighting;
                VoltageDropCalcOther = _originalData.VoltageDropCalcOther;
                GpoCalc = _originalData.GPOCalc;
                LengthExtraPerCctPower = _originalData.Length_ExtraPerCCT_Power;
                LengthExtraPerElementPower = _originalData.Length_ExtraPerElement_Power;
                LengthExtraPerCctLighting = _originalData.Length_ExtraPerCCT_Lighting;
                LengthExtraPerElementLighting = _originalData.Length_ExtraPerElement_Lighting;
                LengthExtraPerCctOther = _originalData.Length_ExtraPerCCT_Other;
                LengthExtraPerElementOther = _originalData.Length_ExtraPerElement_Other;

                // Validate data
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_ProjectInfo
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.JobName = JobName;
                _originalData.JobNumber = JobNumber;
                _originalData.Engineer = Engineer;
                _originalData.Verifier = Verifier;
                _originalData.SystemVoltage = SystemVoltage;
                _originalData.SystemVoltageDropMax = SystemVoltageDropMax;
                _originalData.AmbientTemperature = AmbientTemperature;
                _originalData.ClearingTimePower = ClearingTimePower;
                _originalData.ClearingTimeLighting = ClearingTimeLighting;
                _originalData.ClearingTimeOther = ClearingTimeOther;
                _originalData.VoltageDropCalcPower = VoltageDropCalcPower;
                _originalData.VoltageDropCalcLighting = VoltageDropCalcLighting;
                _originalData.VoltageDropCalcOther = VoltageDropCalcOther;
                _originalData.GPOCalc = GpoCalc;
                _originalData.Length_ExtraPerCCT_Power = LengthExtraPerCctPower;
                _originalData.Length_ExtraPerElement_Power = LengthExtraPerElementPower;
                _originalData.Length_ExtraPerCCT_Lighting = LengthExtraPerCctLighting;
                _originalData.Length_ExtraPerElement_Lighting = LengthExtraPerElementLighting;
                _originalData.Length_ExtraPerCCT_Other = LengthExtraPerCctOther;
                _originalData.Length_ExtraPerElement_Other = LengthExtraPerElementOther;
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        /// <summary>
        /// Commit changes to Revit
        /// </summary>
        public void CommitToRevit()
        {
            try
            {
                SaveToOriginalData();
                _originalData?.CommitToRevit();
                HasErrors = false;
                ErrorMessage = string.Empty;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Failed to commit to Revit: {ex.Message}";
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Validate the project information data
        /// </summary>
        private void ValidateData()
        {
            try
            {
                var errors = new System.Collections.Generic.List<string>();

                // Basic validation
                if (string.IsNullOrWhiteSpace(JobName))
                    errors.Add("Job name is required");

                if (string.IsNullOrWhiteSpace(Engineer))
                    errors.Add("Engineer name is required");

                if (SystemVoltage <= 0)
                    errors.Add("System voltage must be greater than 0");

                if (SystemVoltageDropMax <= 0 || SystemVoltageDropMax > 100)
                    errors.Add("System voltage drop max must be between 0 and 100%");

                if (AmbientTemperature < -50 || AmbientTemperature > 100)
                    errors.Add("Ambient temperature must be between -50°C and 100°C");

                HasErrors = errors.Count > 0;
                ErrorMessage = HasErrors ? string.Join("; ", errors) : string.Empty;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        #endregion
    }




}
