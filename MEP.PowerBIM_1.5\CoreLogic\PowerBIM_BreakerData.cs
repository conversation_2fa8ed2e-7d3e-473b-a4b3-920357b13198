﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_BreakerData
    {

        public PowerBIM_DBData DB { get; set; }
        public string Schedule_Protective_Device { get; set; }
        public string Schedule_Curve_Type { get; set; }
        public double Schedule_Trip_Rating { get; set; }
        public double Min_kA_Rating { get; set; }

        public double kA_Rating_Used { get; set; }

        public double I2t_Phase { get; set; }
        public double I2t_Earth { get; set; }
        public double kA_Earth { get; set; }
        public double EFLI_04 { get; set; }
        public double EFLI_50 { get; set; }
        public double EFLI_Max { get; set; } 

        public bool Data_Good { get; set; }
        public bool Parameters_Good { get; set; }
        public bool Values_Missing { get; set; }
        public string Error_Message { get; set; }
        public bool Is_Spare_Or_Space { get; set; }

        Autodesk.Revit.DB.Parameter paramCircuitTripRating;//
        Autodesk.Revit.DB.Parameter paramProtCurveType;
        Autodesk.Revit.DB.Parameter paramProtDevice;
        Autodesk.Revit.DB.Parameter paramRCDprotection;

        private PowerBIM_ProjectInfo projInfo;
        private ElectricalSystem Electrical_System;

        public PowerBIM_BreakerData(PowerBIM_ProjectInfo pi, ElectricalSystem elecSys, PowerBIM_DBData db)
        {
            //
            // PowerBIM_BreakerData
            //
            // Constructor populating data from the passed ciruit for this breaker..
            //

            // Pass project info information into breaker class for use.
            projInfo = pi;

            DB = db;

            // Pass electrical system for circuit the breaker is on
            Electrical_System = elecSys;

            // Pass DB kA rating through as int.
            Min_kA_Rating = db.Device_kA_Rating;

            Is_Spare_Or_Space = false; // default

            // Get from revit
            if (GetValuesFromRevit())
                UpdateBreakerData();
        }

        public void UpdateBreakerData()
        {
            //
            // UpdateBreakerData will be called initially during the constructor, and then may be called again 
            //   externally whenever the circuit data has been updated.  
            //

            // Cross reference this data to the CPD manufacturer tables
            if ((Parameters_Good == true))
            {
                if (DetermineCircuitBreakerData())
                    Data_Good = true;
                else
                    Data_Good = false;
            }

        }


        private bool GetValuesFromRevit()
        {
            try
            {
                //
                //
                // extract values from revit
                Parameters_Good = true;
                Error_Message = "";

                // Check breaker rating is valid.
                paramCircuitTripRating = Electrical_System.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_RATING_PARAM);
                if (paramCircuitTripRating == null)
                {
                    Error_Message += "Revit project is missing the parameter '" + "Breaker Rating" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramCircuitTripRating.HasValue == false)
                {
                    Error_Message += "The parameter '" + "Breaker Rating" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                {
                    // Sweet, we can store this value now - but first check it within requirments
                    if (PowerBIM_Constants.Standard_TripRatings.ContainsKey(paramCircuitTripRating.AsDouble()))
                        Schedule_Trip_Rating = paramCircuitTripRating.AsDouble();
                    else
                    {
                        Error_Message += "CPD Trip Rating is invalid, ";
                        Values_Missing = true;
                    }
                }

                // Check curve type is valid.
                paramProtCurveType = Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidProtCurveType);
                if (paramProtCurveType == null)
                {
                    Error_Message += "Revit project is missing the parameter '" + "Curve Type" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramProtCurveType.HasValue == false)
                {
                    Error_Message += "The parameter '" + "Curve Type" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                {
                    // Sweet, we can store this value now - but first check it within requirments
                    if (PowerBIM_Constants.Standard_CurveTypes.ContainsKey(paramProtCurveType.AsString()))
                        Schedule_Curve_Type = paramProtCurveType.AsString();
                    else
                    {
                        Error_Message += "CPD Curve Type is invalid, ";
                        Values_Missing = true;
                    }
                }

                // Check curve type is valid.
                paramProtDevice = Electrical_System.get_Parameter(PowerBIM_Constants.paramGuidProtDevice);
                if (paramProtDevice == null)
                {
                    Error_Message += "Revit project is missing the parameter '" + "Curve Type" + "'. ";
                    Parameters_Good = false;
                }
                else if (paramProtDevice.HasValue == false)
                {
                    Error_Message += "The parameter '" + "Curve Type" + "' is empty. ";
                    Values_Missing = true;
                }
                else
                {
                    // Sweet, we can store this value now - but first check it within requirments
                    if (PowerBIM_Constants.Standard_ProtectiveDevices.ContainsKey(Schedule_Protective_Device = paramProtDevice.AsString()))
                        Schedule_Protective_Device = paramProtDevice.AsString();
                    else
                    {
                        Error_Message += "CPD Type is invalid, ";
                        Values_Missing = true;
                    }
                }

                // if we have any problems getting good data here, we must return false
                if ((Parameters_Good == false) || (Values_Missing == true))
                    return false;
                else
                    return true;
            }
            catch
            {
                Error_Message = "An unexpected error occured while constructing breaker data for " + Electrical_System.PanelName + ":" + Electrical_System.CircuitNumber.ToString();
                return false;
            }
        }


        #region private_methods
        private bool DetermineCircuitBreakerData()
        {
            // 2.072 Find EFLIMax
            int kARatingCol = -1;
            int CPDCurveCol = -1;
            bool blFound = false;
            int intFoundColIndex = 0;
            int intFoundRowIndex = 0;

            Min_kA_Rating = DB.Device_kA_Rating;

            //Calculation variables
            double dblResCPDCurrentRating = Schedule_Trip_Rating;
            double dblResCPDkARating = Min_kA_Rating;
            string strResCPDCurve = Schedule_Curve_Type;

            try
            {
                if (Schedule_Curve_Type !=null)
                {

                // try to find the curve type in our dictionary of valid curve types
                if (PowerBIM_Constants.Standard_CurveTypes.TryGetValue(Schedule_Curve_Type, out CPDCurveCol))
                {
                    // try to find the kA rating in our dictionary of valid kA ratings
                    if (PowerBIM_Constants.Standard_kARatings.TryGetValue(Min_kA_Rating, out kARatingCol))
                    {
                        // GREAT!!! you've got valid breaker params.

                        // Itterate through all rows of the CPD database and try to match the current rating
                        for (int row = 0; row <= PowerBIM_Constants.file_MCBRowsMax - 1; row++)
                        {
                            // Find matching row (current rating)
                            if (Schedule_Trip_Rating == double.Parse(projInfo.CPD_Database[row, 0]))
                            {
                                //when we find ther correct current rating, we're going to try the specifed KA rating, otherwise go up in size
                                while ((blFound == false) && (kARatingCol < 5))
                                {
                                    //check for value in this location
                                    int col = 1 + (CPDCurveCol * 25) + (kARatingCol * 5);
                                    string valueString = projInfo.CPD_Database[row, col];

                                    //if value exists
                                    if ((valueString != "N/A") & (valueString != ""))
                                    {
                                        blFound = true; //found!!
                                        kA_Rating_Used = PowerBIM_Constants.Standard_kARatings.FirstOrDefault(x => x.Value == kARatingCol).Key; //Code copied from internet.. does this work?
                                    }
                                    else
                                    {
                                        kARatingCol++;
                                        dblResCPDkARating = -1;     //flag as non standard ---- update to find actuall KA rating used another day
                                        blFound = false;            // try a larger kA rating..?
                                    }
                                }
                                if (blFound == true)
                                {
                                    intFoundColIndex = 1 + (CPDCurveCol * 25) + (kARatingCol * 5);
                                    intFoundRowIndex = row;
                                }
                            }
                        }
                    }
                }
                }

                //If breaker exists 
                if (blFound == true)
                {
                    // Fetch resulting Breaker parameters from database file
                    I2t_Phase = double.Parse(projInfo.CPD_Database[intFoundRowIndex, intFoundColIndex]);
                    I2t_Earth = double.Parse(projInfo.CPD_Database[intFoundRowIndex, intFoundColIndex + 1]);
                    kA_Earth = double.Parse(projInfo.CPD_Database[intFoundRowIndex, intFoundColIndex + 2]);
                    EFLI_04 = double.Parse(projInfo.CPD_Database[intFoundRowIndex, intFoundColIndex + 3]);
                    EFLI_50 = double.Parse(projInfo.CPD_Database[intFoundRowIndex, intFoundColIndex + 4]);

                    return true;
                }
                else
                {
                    // If breaker cant be matched set results to 0
                    I2t_Phase = 0;
                    I2t_Earth = 0;
                    kA_Earth = 0;
                    EFLI_04 = 0;
                    EFLI_50 = 0;

                    return false;
                }
            }
            catch
            {
                // If breaker results in error (say only some of the breaker infor has been filled out..
                I2t_Phase = 0;
                I2t_Earth = 0;
                kA_Earth = 0;
                EFLI_04 = 0;
                EFLI_50 = 0;

                return false;
            }
        }
        #endregion

        #region public_methods
        public double Find_EFLIMax(bool ClearingTimeIs5Sec)
        {
            if (ClearingTimeIs5Sec == true)
                EFLI_Max = EFLI_50;

            else
                EFLI_Max = EFLI_04;
            
            return EFLI_Max;
        }


        public double Find_MaxSCWithstand()
        {
            double dblPhaseI2T = I2t_Phase;

            return dblPhaseI2T; // TODO HIGH PRIORITY add lookup for eath I2C and kA current
        }

        public bool Commit_AllBreakerDataToRevit()
        {
            if ((Parameters_Good == true))
            {
                try
                {
                    paramCircuitTripRating.Set(Schedule_Trip_Rating);
                    paramProtCurveType.Set(Schedule_Curve_Type);
                    paramProtDevice.Set(Schedule_Protective_Device);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        public bool Commit_BreakerRatingOnlyToRevit()
        {
            if ((Parameters_Good == true))
            {
                try
                {
                    paramCircuitTripRating.Set(Schedule_Trip_Rating);
                    return true;
                }
                catch
                {
                    return false;
                }
            }
            return false;
        }

        public void CreateSpareSpaceEntry()
        {
            // Creates a null entry if this breaker is for a space/space circuit
            // Only trip rating can be used.
            // also, clears error warnings. 
            Is_Spare_Or_Space = true;

            Error_Message = "Spare/Space";

            I2t_Phase = 0;
            I2t_Earth = 0;
            kA_Earth = 0;
            EFLI_04 = 0;
            EFLI_50 = 0;
        }
        #endregion
    }
}

