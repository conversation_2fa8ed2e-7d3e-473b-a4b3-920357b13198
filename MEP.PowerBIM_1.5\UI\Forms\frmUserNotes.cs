﻿using Common.UI.Forms;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmUserNotes : BecaBaseForm
    {
        PowerBIM_DBData _db;
        public bool IsSave { get; set; } = false;
        public bool NotesEmpty { get; set; } = true;

        public frmUserNotes(PowerBIM_DBData db)
        {
            InitializeComponent();

            _db = db;
            TitleText = _db.DB_Element.Name;
            rtb_Notes.Text = db.User_Notes;
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            _db.User_Notes = rtb_Notes.Text;
            NotesEmpty = !string.IsNullOrWhiteSpace(rtb_Notes.Text);
            IsSave = true;
        }

        private void btn_Cancel_Click(object sender, EventArgs e)
        {
            NotesEmpty = !string.IsNullOrWhiteSpace(rtb_Notes.Text);
        }
    }
}
