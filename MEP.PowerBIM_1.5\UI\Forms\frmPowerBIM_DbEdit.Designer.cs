﻿namespace MEP.PowerBIM_5.UI.Forms { 
    partial class frmPowerBIM_DbEdit
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            this.tlp_MainContent = new System.Windows.Forms.TableLayoutPanel();
            this.label1 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btn_ActivateEditPathView = new System.Windows.Forms.Button();
            this.rqOpenPowerCADcsv = new System.Windows.Forms.Button();
            this.rqEditDBClose = new System.Windows.Forms.Button();
            this.rqEditDBSave = new System.Windows.Forms.Button();
            this.dgvDBData = new Common.UI.Controls.ExtendedAdvancedDataGridView();
            this.dBInformationBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.dBInfo = new MEP.PowerBIM_5.DataSets.DBInfo();
            this.dBNameDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.mainSwitchRatingDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.feederCableDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.locationDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.seismicCategoryDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.formRatingDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.busFaultLevelDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.surgeProtectionDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.meteringDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.upstreamDeviceRatingDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.eFLIRDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.eFLIXDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dBVDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.pSCCDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.deviceFaultRatingDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.finalCircuitMaxVDDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.manualDataGridViewCheckBoxColumn = new System.Windows.Forms.DataGridViewCheckBoxColumn();
            this.circuitPathModeDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewComboBoxColumn();
            this.SetPathColumn = new System.Windows.Forms.DataGridViewButtonColumn();
            this.lengthTotalDataGridViewTextBoxColumn = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.tlp_MainContent.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDBData)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBInformationBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBInfo)).BeginInit();
            this.SuspendLayout();
            // 
            // tlp_MainContent
            // 
            this.tlp_MainContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tlp_MainContent.ColumnCount = 1;
            this.tlp_MainContent.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1417F));
            this.tlp_MainContent.Controls.Add(this.label1, 0, 0);
            this.tlp_MainContent.Controls.Add(this.groupBox1, 0, 2);
            this.tlp_MainContent.Controls.Add(this.dgvDBData, 0, 1);
            this.tlp_MainContent.Location = new System.Drawing.Point(0, 40);
            this.tlp_MainContent.Name = "tlp_MainContent";
            this.tlp_MainContent.Padding = new System.Windows.Forms.Padding(3, 3, 3, 3);
            this.tlp_MainContent.RowCount = 3;
            this.tlp_MainContent.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 19F));
            this.tlp_MainContent.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlp_MainContent.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 62F));
            this.tlp_MainContent.Size = new System.Drawing.Size(1423, 403);
            this.tlp_MainContent.TabIndex = 3;
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(5, 3);
            this.label1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(1413, 19);
            this.label1.TabIndex = 78;
            this.label1.Text = "Edit DB data manually in the form below, or import lastest PowerCAD values (magen" +
    "ta cells only)";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.btn_ActivateEditPathView);
            this.groupBox1.Controls.Add(this.rqOpenPowerCADcsv);
            this.groupBox1.Controls.Add(this.rqEditDBClose);
            this.groupBox1.Controls.Add(this.rqEditDBSave);
            this.groupBox1.Location = new System.Drawing.Point(5, 340);
            this.groupBox1.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Padding = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.groupBox1.Size = new System.Drawing.Size(1413, 58);
            this.groupBox1.TabIndex = 77;
            this.groupBox1.TabStop = false;
            // 
            // btn_ActivateEditPathView
            // 
            this.btn_ActivateEditPathView.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btn_ActivateEditPathView.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btn_ActivateEditPathView.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.btn_ActivateEditPathView.FlatStyle = System.Windows.Forms.FlatStyle.Popup;
            this.btn_ActivateEditPathView.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_ActivateEditPathView.ForeColor = System.Drawing.Color.White;
            this.btn_ActivateEditPathView.Location = new System.Drawing.Point(708, 12);
            this.btn_ActivateEditPathView.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.btn_ActivateEditPathView.Name = "btn_ActivateEditPathView";
            this.btn_ActivateEditPathView.Size = new System.Drawing.Size(172, 38);
            this.btn_ActivateEditPathView.TabIndex = 204;
            this.btn_ActivateEditPathView.Text = "Activate Edit Path View";
            this.btn_ActivateEditPathView.UseVisualStyleBackColor = false;
            this.btn_ActivateEditPathView.Click += new System.EventHandler(this.btn_ActivateEditPathView_Click);
            // 
            // rqOpenPowerCADcsv
            // 
            this.rqOpenPowerCADcsv.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rqOpenPowerCADcsv.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(141)))), ((int)(((byte)(14)))), ((int)(((byte)(132)))));
            this.rqOpenPowerCADcsv.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqOpenPowerCADcsv.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold);
            this.rqOpenPowerCADcsv.ForeColor = System.Drawing.Color.White;
            this.rqOpenPowerCADcsv.Location = new System.Drawing.Point(524, 12);
            this.rqOpenPowerCADcsv.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.rqOpenPowerCADcsv.Name = "rqOpenPowerCADcsv";
            this.rqOpenPowerCADcsv.Size = new System.Drawing.Size(181, 38);
            this.rqOpenPowerCADcsv.TabIndex = 72;
            this.rqOpenPowerCADcsv.Text = "Import From PowerCAD";
            this.rqOpenPowerCADcsv.UseVisualStyleBackColor = false;
            this.rqOpenPowerCADcsv.Click += new System.EventHandler(this.rqOpenPowerCADcsv_Click);
            // 
            // rqEditDBClose
            // 
            this.rqEditDBClose.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rqEditDBClose.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            this.rqEditDBClose.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqEditDBClose.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqEditDBClose.ForeColor = System.Drawing.Color.White;
            this.rqEditDBClose.Location = new System.Drawing.Point(884, 12);
            this.rqEditDBClose.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.rqEditDBClose.Name = "rqEditDBClose";
            this.rqEditDBClose.Size = new System.Drawing.Size(138, 38);
            this.rqEditDBClose.TabIndex = 76;
            this.rqEditDBClose.Text = "Close";
            this.rqEditDBClose.UseVisualStyleBackColor = false;
            this.rqEditDBClose.Click += new System.EventHandler(this.frmEditDBClose_Click);
            // 
            // rqEditDBSave
            // 
            this.rqEditDBSave.Anchor = System.Windows.Forms.AnchorStyles.Top;
            this.rqEditDBSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.rqEditDBSave.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(206)))), ((int)(((byte)(0)))));
            this.rqEditDBSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rqEditDBSave.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rqEditDBSave.ForeColor = System.Drawing.Color.Black;
            this.rqEditDBSave.Location = new System.Drawing.Point(381, 12);
            this.rqEditDBSave.Margin = new System.Windows.Forms.Padding(2, 2, 2, 2);
            this.rqEditDBSave.Name = "rqEditDBSave";
            this.rqEditDBSave.Size = new System.Drawing.Size(138, 38);
            this.rqEditDBSave.TabIndex = 75;
            this.rqEditDBSave.Text = "Save";
            this.rqEditDBSave.UseVisualStyleBackColor = false;
            this.rqEditDBSave.Click += new System.EventHandler(this.frmEditDBSave_Click);
            // 
            // dgvDBData
            // 
            this.dgvDBData.AllowDrop = true;
            this.dgvDBData.AllowUserToAddRows = false;
            this.dgvDBData.AllowUserToDeleteRows = false;
            this.dgvDBData.AutoGenerateColumns = false;
            this.dgvDBData.BackgroundColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(18)))), ((int)(((byte)(168)))), ((int)(((byte)(178)))));
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Tahoma", 8F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDBData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvDBData.ColumnHeadersHeight = 60;
            this.dgvDBData.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.dBNameDataGridViewTextBoxColumn,
            this.mainSwitchRatingDataGridViewTextBoxColumn,
            this.feederCableDataGridViewTextBoxColumn,
            this.locationDataGridViewTextBoxColumn,
            this.seismicCategoryDataGridViewTextBoxColumn,
            this.formRatingDataGridViewTextBoxColumn,
            this.busFaultLevelDataGridViewTextBoxColumn,
            this.surgeProtectionDataGridViewTextBoxColumn,
            this.meteringDataGridViewTextBoxColumn,
            this.upstreamDeviceRatingDataGridViewTextBoxColumn,
            this.eFLIRDataGridViewTextBoxColumn,
            this.eFLIXDataGridViewTextBoxColumn,
            this.dBVDDataGridViewTextBoxColumn,
            this.pSCCDataGridViewTextBoxColumn,
            this.deviceFaultRatingDataGridViewTextBoxColumn,
            this.finalCircuitMaxVDDataGridViewTextBoxColumn,
            this.manualDataGridViewCheckBoxColumn,
            this.circuitPathModeDataGridViewTextBoxColumn,
            this.SetPathColumn,
            this.lengthTotalDataGridViewTextBoxColumn});
            this.dgvDBData.DataSource = this.dBInformationBindingSource;
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle9.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle9.Font = new System.Drawing.Font("Arial Narrow", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle9.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle9.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvDBData.DefaultCellStyle = dataGridViewCellStyle9;
            this.dgvDBData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvDBData.EnableHeadersVisualStyles = false;
            this.dgvDBData.FilterAndSortEnabled = true;
            this.dgvDBData.FilterStringChangedInvokeBeforeDatasourceUpdate = true;
            this.dgvDBData.Location = new System.Drawing.Point(6, 25);
            this.dgvDBData.Name = "dgvDBData";
            this.dgvDBData.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.dgvDBData.RowHeadersVisible = false;
            this.dgvDBData.RowHeadersWidth = 51;
            this.dgvDBData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect;
            this.dgvDBData.Size = new System.Drawing.Size(1411, 310);
            this.dgvDBData.SortStringChangedInvokeBeforeDatasourceUpdate = true;
            this.dgvDBData.TabIndex = 79;
            this.dgvDBData.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvDBData_CellClick);
            this.dgvDBData.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgvDBData_CellValueChanged);
            this.dgvDBData.CurrentCellDirtyStateChanged += new System.EventHandler(this.dgvDBData_CurrentCellDirtyStateChanged);
            this.dgvDBData.DataError += new System.Windows.Forms.DataGridViewDataErrorEventHandler(this.dgvDBData_DataError);
            // 
            // dBInformationBindingSource
            // 
            this.dBInformationBindingSource.DataMember = "DBInformation";
            this.dBInformationBindingSource.DataSource = this.dBInfo;
            // 
            // dBInfo
            // 
            this.dBInfo.DataSetName = "DBInfo";
            this.dBInfo.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema;
            // 
            // dBNameDataGridViewTextBoxColumn
            // 
            this.dBNameDataGridViewTextBoxColumn.DataPropertyName = "DB_Name";
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.dBNameDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle2;
            this.dBNameDataGridViewTextBoxColumn.FillWeight = 60F;
            this.dBNameDataGridViewTextBoxColumn.Frozen = true;
            this.dBNameDataGridViewTextBoxColumn.HeaderText = "DB Name";
            this.dBNameDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.dBNameDataGridViewTextBoxColumn.Name = "dBNameDataGridViewTextBoxColumn";
            this.dBNameDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.dBNameDataGridViewTextBoxColumn.Width = 125;
            // 
            // mainSwitchRatingDataGridViewTextBoxColumn
            // 
            this.mainSwitchRatingDataGridViewTextBoxColumn.DataPropertyName = "Main_Switch_Rating";
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.mainSwitchRatingDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle3;
            this.mainSwitchRatingDataGridViewTextBoxColumn.FillWeight = 60F;
            this.mainSwitchRatingDataGridViewTextBoxColumn.HeaderText = "Isolator Rating (A)";
            this.mainSwitchRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.mainSwitchRatingDataGridViewTextBoxColumn.Name = "mainSwitchRatingDataGridViewTextBoxColumn";
            this.mainSwitchRatingDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.mainSwitchRatingDataGridViewTextBoxColumn.Width = 125;
            // 
            // feederCableDataGridViewTextBoxColumn
            // 
            this.feederCableDataGridViewTextBoxColumn.DataPropertyName = "Feeder_Cable";
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.feederCableDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle4;
            this.feederCableDataGridViewTextBoxColumn.FillWeight = 60F;
            this.feederCableDataGridViewTextBoxColumn.HeaderText = "Feeder_Cable";
            this.feederCableDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.feederCableDataGridViewTextBoxColumn.Name = "feederCableDataGridViewTextBoxColumn";
            this.feederCableDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.feederCableDataGridViewTextBoxColumn.Width = 125;
            // 
            // locationDataGridViewTextBoxColumn
            // 
            this.locationDataGridViewTextBoxColumn.DataPropertyName = "Location";
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.locationDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle5;
            this.locationDataGridViewTextBoxColumn.FillWeight = 60F;
            this.locationDataGridViewTextBoxColumn.HeaderText = "Location";
            this.locationDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.locationDataGridViewTextBoxColumn.Name = "locationDataGridViewTextBoxColumn";
            this.locationDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.locationDataGridViewTextBoxColumn.Width = 125;
            // 
            // seismicCategoryDataGridViewTextBoxColumn
            // 
            this.seismicCategoryDataGridViewTextBoxColumn.DataPropertyName = "Seismic_Category";
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.seismicCategoryDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle6;
            this.seismicCategoryDataGridViewTextBoxColumn.FillWeight = 60F;
            this.seismicCategoryDataGridViewTextBoxColumn.HeaderText = "Seismic_Category";
            this.seismicCategoryDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.seismicCategoryDataGridViewTextBoxColumn.Name = "seismicCategoryDataGridViewTextBoxColumn";
            this.seismicCategoryDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.seismicCategoryDataGridViewTextBoxColumn.Width = 125;
            // 
            // formRatingDataGridViewTextBoxColumn
            // 
            this.formRatingDataGridViewTextBoxColumn.DataPropertyName = "Form_Rating";
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.formRatingDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle7;
            this.formRatingDataGridViewTextBoxColumn.FillWeight = 60F;
            this.formRatingDataGridViewTextBoxColumn.HeaderText = "Form Rating";
            this.formRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.formRatingDataGridViewTextBoxColumn.Name = "formRatingDataGridViewTextBoxColumn";
            this.formRatingDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.formRatingDataGridViewTextBoxColumn.Width = 125;
            // 
            // busFaultLevelDataGridViewTextBoxColumn
            // 
            this.busFaultLevelDataGridViewTextBoxColumn.DataPropertyName = "Bus_Fault_Level";
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            this.busFaultLevelDataGridViewTextBoxColumn.DefaultCellStyle = dataGridViewCellStyle8;
            this.busFaultLevelDataGridViewTextBoxColumn.FillWeight = 60F;
            this.busFaultLevelDataGridViewTextBoxColumn.HeaderText = "Bus_Fault_Level";
            this.busFaultLevelDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.busFaultLevelDataGridViewTextBoxColumn.Name = "busFaultLevelDataGridViewTextBoxColumn";
            this.busFaultLevelDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.busFaultLevelDataGridViewTextBoxColumn.Width = 125;
            // 
            // surgeProtectionDataGridViewTextBoxColumn
            // 
            this.surgeProtectionDataGridViewTextBoxColumn.DataPropertyName = "Surge_Protection";
            this.surgeProtectionDataGridViewTextBoxColumn.HeaderText = "Surge_Protection";
            this.surgeProtectionDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.surgeProtectionDataGridViewTextBoxColumn.Name = "surgeProtectionDataGridViewTextBoxColumn";
            this.surgeProtectionDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.surgeProtectionDataGridViewTextBoxColumn.Width = 125;
            // 
            // meteringDataGridViewTextBoxColumn
            // 
            this.meteringDataGridViewTextBoxColumn.DataPropertyName = "Metering";
            this.meteringDataGridViewTextBoxColumn.HeaderText = "Metering";
            this.meteringDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.meteringDataGridViewTextBoxColumn.Name = "meteringDataGridViewTextBoxColumn";
            this.meteringDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.meteringDataGridViewTextBoxColumn.Width = 125;
            // 
            // upstreamDeviceRatingDataGridViewTextBoxColumn
            // 
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.DataPropertyName = "Upstream_Device_Rating";
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.HeaderText = "Upstream_Device_Rating";
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.Name = "upstreamDeviceRatingDataGridViewTextBoxColumn";
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.upstreamDeviceRatingDataGridViewTextBoxColumn.Width = 125;
            // 
            // eFLIRDataGridViewTextBoxColumn
            // 
            this.eFLIRDataGridViewTextBoxColumn.DataPropertyName = "EFLI_R";
            this.eFLIRDataGridViewTextBoxColumn.HeaderText = "EFLI_R";
            this.eFLIRDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.eFLIRDataGridViewTextBoxColumn.Name = "eFLIRDataGridViewTextBoxColumn";
            this.eFLIRDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.eFLIRDataGridViewTextBoxColumn.Width = 125;
            // 
            // eFLIXDataGridViewTextBoxColumn
            // 
            this.eFLIXDataGridViewTextBoxColumn.DataPropertyName = "EFLI_X";
            this.eFLIXDataGridViewTextBoxColumn.FillWeight = 60F;
            this.eFLIXDataGridViewTextBoxColumn.HeaderText = "EFLI X";
            this.eFLIXDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.eFLIXDataGridViewTextBoxColumn.Name = "eFLIXDataGridViewTextBoxColumn";
            this.eFLIXDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.eFLIXDataGridViewTextBoxColumn.Width = 125;
            // 
            // dBVDDataGridViewTextBoxColumn
            // 
            this.dBVDDataGridViewTextBoxColumn.DataPropertyName = "DBVD";
            this.dBVDDataGridViewTextBoxColumn.FillWeight = 60F;
            this.dBVDDataGridViewTextBoxColumn.HeaderText = "DB VD (%)";
            this.dBVDDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.dBVDDataGridViewTextBoxColumn.Name = "dBVDDataGridViewTextBoxColumn";
            this.dBVDDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.dBVDDataGridViewTextBoxColumn.Width = 125;
            // 
            // pSCCDataGridViewTextBoxColumn
            // 
            this.pSCCDataGridViewTextBoxColumn.DataPropertyName = "PSCC";
            this.pSCCDataGridViewTextBoxColumn.FillWeight = 60F;
            this.pSCCDataGridViewTextBoxColumn.HeaderText = "PSCC";
            this.pSCCDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.pSCCDataGridViewTextBoxColumn.Name = "pSCCDataGridViewTextBoxColumn";
            this.pSCCDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.pSCCDataGridViewTextBoxColumn.Width = 125;
            // 
            // deviceFaultRatingDataGridViewTextBoxColumn
            // 
            this.deviceFaultRatingDataGridViewTextBoxColumn.DataPropertyName = "Device_Fault_Rating";
            this.deviceFaultRatingDataGridViewTextBoxColumn.HeaderText = "Device Fault Rating";
            this.deviceFaultRatingDataGridViewTextBoxColumn.Items.AddRange(new object[] {
            "6",
            "10",
            "15",
            "20",
            "25",
            " "});
            this.deviceFaultRatingDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.deviceFaultRatingDataGridViewTextBoxColumn.Name = "deviceFaultRatingDataGridViewTextBoxColumn";
            this.deviceFaultRatingDataGridViewTextBoxColumn.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.deviceFaultRatingDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.deviceFaultRatingDataGridViewTextBoxColumn.Width = 125;
            // 
            // finalCircuitMaxVDDataGridViewTextBoxColumn
            // 
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.DataPropertyName = "Final_Circuit_MaxVD";
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.HeaderText = "Final_Circuit_MaxVD (%)";
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.Name = "finalCircuitMaxVDDataGridViewTextBoxColumn";
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.finalCircuitMaxVDDataGridViewTextBoxColumn.Width = 125;
            // 
            // manualDataGridViewCheckBoxColumn
            // 
            this.manualDataGridViewCheckBoxColumn.DataPropertyName = "Manual";
            this.manualDataGridViewCheckBoxColumn.HeaderText = "Manual";
            this.manualDataGridViewCheckBoxColumn.MinimumWidth = 22;
            this.manualDataGridViewCheckBoxColumn.Name = "manualDataGridViewCheckBoxColumn";
            this.manualDataGridViewCheckBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.manualDataGridViewCheckBoxColumn.Width = 125;
            // 
            // circuitPathModeDataGridViewTextBoxColumn
            // 
            this.circuitPathModeDataGridViewTextBoxColumn.DataPropertyName = "Circuit_Path_Mode";
            this.circuitPathModeDataGridViewTextBoxColumn.DisplayStyleForCurrentCellOnly = true;
            this.circuitPathModeDataGridViewTextBoxColumn.FillWeight = 60F;
            this.circuitPathModeDataGridViewTextBoxColumn.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.circuitPathModeDataGridViewTextBoxColumn.HeaderText = "Circuit_Path_Mode";
            this.circuitPathModeDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.circuitPathModeDataGridViewTextBoxColumn.Name = "circuitPathModeDataGridViewTextBoxColumn";
            this.circuitPathModeDataGridViewTextBoxColumn.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.circuitPathModeDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.circuitPathModeDataGridViewTextBoxColumn.Width = 125;
            // 
            // SetPathColumn
            // 
            this.SetPathColumn.HeaderText = "Edit Custom Cable";
            this.SetPathColumn.MinimumWidth = 22;
            this.SetPathColumn.Name = "SetPathColumn";
            this.SetPathColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.SetPathColumn.Text = "Set Path";
            this.SetPathColumn.UseColumnTextForButtonValue = true;
            this.SetPathColumn.Width = 125;
            // 
            // lengthTotalDataGridViewTextBoxColumn
            // 
            this.lengthTotalDataGridViewTextBoxColumn.DataPropertyName = "Length_Total";
            this.lengthTotalDataGridViewTextBoxColumn.HeaderText = "Length_Total";
            this.lengthTotalDataGridViewTextBoxColumn.MinimumWidth = 22;
            this.lengthTotalDataGridViewTextBoxColumn.Name = "lengthTotalDataGridViewTextBoxColumn";
            this.lengthTotalDataGridViewTextBoxColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Programmatic;
            this.lengthTotalDataGridViewTextBoxColumn.Width = 125;
            // 
            // frmPowerBIM_DbEdit
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.btnHelp_Visiblity = true;
            this.ClientSize = new System.Drawing.Size(1423, 494);
            this.Controls.Add(this.tlp_MainContent);
            this.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
            this.Name = "frmPowerBIM_DbEdit";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "PowerBIM | DB Data";
            this.TitleText = "Edit DB Data";
            this.VerisionText = "© 2021   01.05.01  ";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmPowerBIM_DbEdit_FormClosing);
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.frmPowerBIM_DbEdit_FormClosed);
            this.Load += new System.EventHandler(this.frmPowerBIM_DbEdit_Load_1);
            this.Controls.SetChildIndex(this.tlp_MainContent, 0);
            this.tlp_MainContent.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvDBData)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBInformationBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dBInfo)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel tlp_MainContent;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Button rqOpenPowerCADcsv;
        private System.Windows.Forms.Button rqEditDBClose;
        private System.Windows.Forms.Button rqEditDBSave;
        private System.Windows.Forms.Label label1;
        private Common.UI.Controls.ExtendedAdvancedDataGridView dgvDBData;
        private System.Windows.Forms.Button btn_ActivateEditPathView;
        private System.Windows.Forms.BindingSource dBInformationBindingSource;
        private PowerBIM_5.DataSets.DBInfo dBInfo;
        private System.Windows.Forms.DataGridViewTextBoxColumn dBNameDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn mainSwitchRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn feederCableDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn locationDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn seismicCategoryDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn formRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn busFaultLevelDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn surgeProtectionDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn meteringDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn upstreamDeviceRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn eFLIRDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn eFLIXDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn dBVDDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn pSCCDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn deviceFaultRatingDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn finalCircuitMaxVDDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewCheckBoxColumn manualDataGridViewCheckBoxColumn;
        private System.Windows.Forms.DataGridViewComboBoxColumn circuitPathModeDataGridViewTextBoxColumn;
        private System.Windows.Forms.DataGridViewButtonColumn SetPathColumn;
        private System.Windows.Forms.DataGridViewTextBoxColumn lengthTotalDataGridViewTextBoxColumn;
    }
}