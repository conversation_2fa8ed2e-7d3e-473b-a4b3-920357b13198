using System;
using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// ViewModel for individual circuit items in the DataGrid
    /// Wraps PowerBIM_CircuitData with ObservableObject for WPF binding
    /// Provides all properties needed for the complex circuit editing DataGrid
    /// </summary>
    public partial class CircuitItemViewModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_CircuitData _originalData;
        private readonly PowerBIM_ProjectInfo _projectInfo;

        #endregion

        #region Properties

        /// <summary>
        /// Reference to original PowerBIM circuit data
        /// </summary>
        public PowerBIM_CircuitData OriginalData => _originalData;

        #endregion

        #region Observable Properties - Basic Circuit Info

        [ObservableProperty]
        private string _circuitNumber = string.Empty;

        [ObservableProperty]
        private string _description = string.Empty;

        [ObservableProperty]
        private double _current;

        [ObservableProperty]
        private bool _isManual;

        [ObservableProperty]
        private bool _manualCurrent;

        [ObservableProperty]
        private double _manualCurrentValue;

        [ObservableProperty]
        private int _numberOfElements = 1;

        #endregion

        #region Observable Properties - Device Settings

        [ObservableProperty]
        private string _deviceRating = string.Empty;

        [ObservableProperty]
        private string _deviceCurveType = string.Empty;

        [ObservableProperty]
        private string _protectionDevice = string.Empty;

        [ObservableProperty]
        private string _rcdProtection = string.Empty;

        [ObservableProperty]
        private string _otherControls = string.Empty;

        #endregion

        #region Observable Properties - Cable Settings

        [ObservableProperty]
        private string _cableToFirst = string.Empty;

        [ObservableProperty]
        private string _cableToRemainder = string.Empty;

        [ObservableProperty]
        private string _installationMethod = string.Empty;

        [ObservableProperty]
        private double _deratingFactor = 1.0;

        [ObservableProperty]
        private double _diversity = 1.0;

        #endregion

        #region Observable Properties - Path and Length

        [ObservableProperty]
        private string _pathMode = "Automatic";

        [ObservableProperty]
        private double _lengthToFirst;

        [ObservableProperty]
        private double _lengthToFinal;

        [ObservableProperty]
        private bool _canEditPath = true;

        #endregion

        #region Observable Properties - Validation Results

        [ObservableProperty]
        private string _checkTripRating = string.Empty;

        [ObservableProperty]
        private string _cable1Valid = string.Empty;

        [ObservableProperty]
        private string _cable2Valid = string.Empty;

        [ObservableProperty]
        private string _checkCpdDiscriminates = string.Empty;

        [ObservableProperty]
        private string _checkLoadCurrent = string.Empty;

        [ObservableProperty]
        private string _checkCable1Current = string.Empty;

        [ObservableProperty]
        private string _checkCable2Current = string.Empty;

        [ObservableProperty]
        private string _checkCable1VoltageDropPercent = string.Empty;

        [ObservableProperty]
        private string _checkCable2VoltageDropPercent = string.Empty;

        [ObservableProperty]
        private string _checkCable1ScWithstand = string.Empty;

        [ObservableProperty]
        private string _checkCable2ScWithstand = string.Empty;

        [ObservableProperty]
        private string _circuitCheckSummary = string.Empty;

        [ObservableProperty]
        private string _circuitCheckResult = string.Empty;

        [ObservableProperty]
        private string _circuitRevision = string.Empty;

        [ObservableProperty]
        private bool _isSpareOrSpace;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize CircuitItemViewModel with PowerBIM circuit data
        /// </summary>
        /// <param name="circuitData">Original PowerBIM circuit data</param>
        /// <param name="projectInfo">Project information</param>
        public CircuitItemViewModel(PowerBIM_CircuitData circuitData, PowerBIM_ProjectInfo projectInfo)
        {
            _originalData = circuitData ?? throw new ArgumentNullException(nameof(circuitData));
            _projectInfo = projectInfo ?? throw new ArgumentNullException(nameof(projectInfo));

            // Load initial data from original circuit
            RefreshFromOriginalData();

            // Set up property change notifications to mark as dirty
            PropertyChanged += OnPropertyChanged;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refresh all properties from original PowerBIM circuit data
        /// </summary>
        public void RefreshFromOriginalData()
        {
            if (_originalData == null) return;

            // Basic circuit info
            CircuitNumber = _originalData.CCT_Number ?? string.Empty;
            Description = _originalData.Schedule_Description ?? string.Empty;
            Current = _originalData.GetCurrent();
            IsManual = _originalData.Manual;
            ManualCurrent = _originalData.ManualCurrent;
            ManualCurrentValue = _originalData.Manual_PowerBim_User_Current;
            NumberOfElements = _originalData.Number_Of_Elements;

            // Device settings
            DeviceRating = _originalData.Schedule_Device_Rating ?? string.Empty;
            DeviceCurveType = _originalData.Schedule_Device_Curve_Type ?? string.Empty;
            ProtectionDevice = _originalData.Schedule_Protection_Device ?? string.Empty;
            RcdProtection = _originalData.Schedule_RCD_Protection ?? string.Empty;
            OtherControls = _originalData.Schedule_Other_Controls ?? string.Empty;

            // Cable settings
            CableToFirst = _originalData.Schedule_Cable_To_First ?? string.Empty;
            CableToRemainder = _originalData.Schedule_Cable_To_Final ?? string.Empty;
            InstallationMethod = _originalData.Schedule_Cable_Installation_Method ?? string.Empty;
            DeratingFactor = _originalData.Schedule_Derating_Factor;
            Diversity = _originalData.Schedule_Diversity;

            // Path and length
            PathMode = _originalData.LengthClass?.Circuit_Path_Mode ?? "Automatic";
            LengthToFirst = _originalData.CCT_Length_To_First_Element;
            LengthToFinal = _originalData.CCT_Length;
            CanEditPath = !_originalData.Manual && !_originalData.IsLocked;

            // Validation results
            CheckTripRating = _originalData.Check_Trip_Rating ?? string.Empty;
            Cable1Valid = _originalData.Cable_1_Valid ?? string.Empty;
            Cable2Valid = _originalData.Cable_2_Valid ?? string.Empty;
            CheckCpdDiscriminates = _originalData.Check_CPD_Descriminates ?? string.Empty;
            CheckLoadCurrent = _originalData.Check_Load_Current ?? string.Empty;
            CheckCable1Current = _originalData.Check_Cable_1_Current ?? string.Empty;
            CheckCable2Current = _originalData.Check_Cable_2_Current ?? string.Empty;
            CheckCable1VoltageDropPercent = _originalData.Check_Cable_1_Voltage_Drop_Percent ?? string.Empty;
            CheckCable2VoltageDropPercent = _originalData.Check_Cable_2_Voltage_Drop_Percent ?? string.Empty;
            CheckCable1ScWithstand = _originalData.Check_Cable_1_SC_Withstand ?? string.Empty;
            CheckCable2ScWithstand = _originalData.Check_Cable_2_SC_Withstand ?? string.Empty;
            CircuitCheckSummary = _originalData.Circuit_Check_Summary ?? string.Empty;
            CircuitCheckResult = _originalData.Circuit_Check_Result ?? string.Empty;
            CircuitRevision = _originalData.Circuit_Revision ?? string.Empty;
            IsSpareOrSpace = _originalData.IsSpareOrSpace;
        }

        /// <summary>
        /// Save changes back to original PowerBIM circuit data
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData == null) return;

            // Basic circuit info
            _originalData.CCT_Number = CircuitNumber;
            _originalData.Schedule_Description = Description;
            _originalData.Manual = IsManual;
            _originalData.ManualCurrent = ManualCurrent;
            _originalData.Manual_PowerBim_User_Current = ManualCurrentValue;
            _originalData.Number_Of_Elements = NumberOfElements;

            // Device settings
            _originalData.Schedule_Device_Rating = DeviceRating;
            _originalData.Schedule_Device_Curve_Type = DeviceCurveType;
            _originalData.Schedule_Protection_Device = ProtectionDevice;
            _originalData.Schedule_RCD_Protection = RcdProtection;
            _originalData.Schedule_Other_Controls = OtherControls;

            // Cable settings
            _originalData.Schedule_Cable_To_First = CableToFirst;
            _originalData.Schedule_Cable_To_Final = CableToRemainder;
            _originalData.Schedule_Cable_Installation_Method = InstallationMethod;
            _originalData.Schedule_Derating_Factor = DeratingFactor;
            _originalData.Schedule_Diversity = Diversity;

            // Path and length
            if (_originalData.LengthClass != null)
            {
                _originalData.LengthClass.Circuit_Path_Mode = PathMode;
            }
            _originalData.CCT_Length_To_First_Element = LengthToFirst;
            _originalData.CCT_Length = LengthToFinal;

            // Other properties
            _originalData.Circuit_Revision = CircuitRevision;
            _originalData.IsSpareOrSpace = IsSpareOrSpace;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle property changes to trigger recalculation and mark as dirty
        /// </summary>
        private void OnPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            // Mark as having unsaved changes when properties change
            // This will be handled by the parent ViewModel
        }

        #endregion
    }
}
