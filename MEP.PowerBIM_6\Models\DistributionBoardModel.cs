using System;
using System.Collections.Generic;
using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a PowerBIM Distribution Board for WPF binding
    /// Wraps PowerBIM_DBData with ObservableObject for MVVM
    /// </summary>
    public partial class DistributionBoardModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_DBData _originalData;
        private string _name;
        private int _circuitCount;
        private string _status;
        private bool _isLocked;
        private int _passCount;
        private bool _hasErrors;
        private List<string> _errorMessages;
        private double _totalLoad;
        private string _description;

        #endregion

        #region Properties

        /// <summary>
        /// Distribution Board name
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Number of circuits in the distribution board
        /// </summary>
        public int CircuitCount
        {
            get => _circuitCount;
            set
            {
                if (_circuitCount != value)
                {
                    _circuitCount = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Distribution Board status
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Indicates if the distribution board is locked
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set
            {
                if (_isLocked != value)
                {
                    _isLocked = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsNotLocked));
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// Pass count for calculations
        /// </summary>
        public int PassCount
        {
            get => _passCount;
            set
            {
                if (_passCount != value)
                {
                    _passCount = value;
                    OnPropertyChanged();
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        public bool HasErrors
        {
            get => _hasErrors;
            set
            {
                if (_hasErrors != value)
                {
                    _hasErrors = value;
                    OnPropertyChanged();
                    UpdateStatus();
                }
            }
        }

        /// <summary>
        /// List of validation error messages
        /// </summary>
        public List<string> ErrorMessages
        {
            get => _errorMessages;
            set
            {
                if (_errorMessages != value)
                {
                    _errorMessages = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Total load for the distribution board
        /// </summary>
        public double TotalLoad
        {
            get => _totalLoad;
            set
            {
                if (Math.Abs(_totalLoad - value) > 0.001)
                {
                    _totalLoad = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Distribution Board description
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_DBData
        /// </summary>
        public PowerBIM_DBData OriginalData => _originalData;

        /// <summary>
        /// Indicates if the distribution board is not locked
        /// </summary>
        public bool IsNotLocked => !IsLocked;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the DistributionBoardModel with PowerBIM_DBData
        /// </summary>
        /// <param name="dbData">Original distribution board data</param>
        public DistributionBoardModel(PowerBIM_DBData dbData)
        {
            _originalData = dbData ?? throw new ArgumentNullException(nameof(dbData));
            
            // Initialize properties from original data
            LoadFromOriginalData();
            
            // Initialize collections
            ErrorMessages = new List<string>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_DBData
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                Name = _originalData.Schedule_DB_Name ?? "Unknown Distribution Board";
                CircuitCount = _originalData.CCTs?.Count ?? 0;
                IsLocked = _originalData.IsManuallyLocked;
                PassCount = _originalData.PassCount;
                Description = _originalData.Description ?? string.Empty;
                
                // Calculate status based on data
                UpdateStatus();
                
                // Calculate total load
                CalculateTotalLoad();
                
                // Check for errors
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_DBData
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.Schedule_DB_Name = Name;
                _originalData.IsManuallyLocked = IsLocked;
                _originalData.Description = Description;
                // Note: Other properties are typically calculated, not directly set
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update the status based on current data
        /// </summary>
        private void UpdateStatus()
        {
            try
            {
                if (IsLocked)
                {
                    Status = "Locked";
                }
                else if (HasErrors)
                {
                    Status = "Error";
                }
                else if (PassCount > 0)
                {
                    Status = "Calculated";
                }
                else
                {
                    Status = "Pending";
                }
            }
            catch (Exception)
            {
                Status = "Unknown";
            }
        }

        /// <summary>
        /// Calculate the total load for the distribution board
        /// </summary>
        private void CalculateTotalLoad()
        {
            try
            {
                double total = 0;
                
                if (_originalData?.CCTs != null)
                {
                    foreach (var circuit in _originalData.CCTs)
                    {
                        if (circuit != null && !double.IsNaN(circuit.Load))
                        {
                            total += circuit.Load;
                        }
                    }
                }
                
                TotalLoad = total;
            }
            catch (Exception)
            {
                TotalLoad = 0;
            }
        }

        /// <summary>
        /// Validate the distribution board data and update error status
        /// </summary>
        private void ValidateData()
        {
            try
            {
                var errors = new List<string>();
                
                // Basic validation
                if (string.IsNullOrWhiteSpace(Name))
                {
                    errors.Add("Distribution Board name is required");
                }

                if (_originalData?.CCTs == null || _originalData.CCTs.Count == 0)
                {
                    errors.Add("No circuits found in distribution board");
                }

                // Check for circuit validation errors
                if (_originalData?.CCTs != null)
                {
                    foreach (var circuit in _originalData.CCTs)
                    {
                        if (circuit != null && double.IsNaN(circuit.Load))
                        {
                            errors.Add($"Invalid load value in circuit {circuit.CCT_Number}");
                        }
                    }
                }
                
                ErrorMessages = errors;
                HasErrors = errors.Count > 0;
            }
            catch (Exception)
            {
                HasErrors = true;
                ErrorMessages = new List<string> { "Validation error occurred" };
            }
        }

        #endregion

        #region INotifyPropertyChanged Implementation

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
