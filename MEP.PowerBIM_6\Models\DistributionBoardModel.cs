using System;
using System.Collections.Generic;
using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a PowerBIM Distribution Board for WPF binding
    /// Wraps PowerBIM_DBData with ObservableObject for MVVM
    /// </summary>
    public partial class DistributionBoardModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_DBData _originalData;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Distribution Board name
        /// </summary>
        [ObservableProperty]
        private string _name;

        /// <summary>
        /// Number of circuits in the distribution board
        /// </summary>
        [ObservableProperty]
        private int _circuitCount;

        /// <summary>
        /// Distribution Board status
        /// </summary>
        [ObservableProperty]
        private string _status;

        /// <summary>
        /// Indicates if the distribution board is locked
        /// </summary>
        [ObservableProperty]
        private bool _isLocked;

        /// <summary>
        /// Pass count for calculations
        /// </summary>
        [ObservableProperty]
        private int _passCount;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        /// <summary>
        /// List of validation error messages
        /// </summary>
        [ObservableProperty]
        private List<string> _errorMessages;

        /// <summary>
        /// Total load for the distribution board
        /// </summary>
        [ObservableProperty]
        private double _totalLoad;

        /// <summary>
        /// Distribution Board description
        /// </summary>
        [ObservableProperty]
        private string _description;

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_DBData
        /// </summary>
        public PowerBIM_DBData OriginalData => _originalData;

        /// <summary>
        /// Indicates if the distribution board is not locked
        /// </summary>
        public bool IsNotLocked => !IsLocked;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the DistributionBoardModel with PowerBIM_DBData
        /// </summary>
        /// <param name="dbData">Original distribution board data</param>
        public DistributionBoardModel(PowerBIM_DBData dbData)
        {
            _originalData = dbData ?? throw new ArgumentNullException(nameof(dbData));
            
            // Initialize properties from original data
            LoadFromOriginalData();
            
            // Initialize collections
            ErrorMessages = new List<string>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_DBData
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                Name = _originalData.Schedule_DB_Name ?? "Unknown Distribution Board";
                CircuitCount = _originalData.AllCircuits?.Count ?? 0;
                IsLocked = _originalData.IsManuallyLocked;
                PassCount = _originalData.PassCount;
                Description = _originalData.Description ?? string.Empty;
                
                // Calculate status based on data
                UpdateStatus();
                
                // Calculate total load
                CalculateTotalLoad();
                
                // Check for errors
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_DBData
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.Schedule_DB_Name = Name;
                _originalData.IsManuallyLocked = IsLocked;
                _originalData.Description = Description;
                // Note: Other properties are typically calculated, not directly set
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update the status based on current data
        /// </summary>
        private void UpdateStatus()
        {
            try
            {
                if (IsLocked)
                {
                    Status = "Locked";
                }
                else if (HasErrors)
                {
                    Status = "Error";
                }
                else if (PassCount > 0)
                {
                    Status = "Calculated";
                }
                else
                {
                    Status = "Pending";
                }
            }
            catch (Exception)
            {
                Status = "Unknown";
            }
        }

        /// <summary>
        /// Calculate the total load for the distribution board
        /// </summary>
        private void CalculateTotalLoad()
        {
            try
            {
                double total = 0;
                
                if (_originalData?.AllCircuits != null)
                {
                    foreach (var circuit in _originalData.AllCircuits)
                    {
                        if (circuit != null && !double.IsNaN(circuit.Load))
                        {
                            total += circuit.Load;
                        }
                    }
                }
                
                TotalLoad = total;
            }
            catch (Exception)
            {
                TotalLoad = 0;
            }
        }

        /// <summary>
        /// Validate the distribution board data and update error status
        /// </summary>
        private void ValidateData()
        {
            try
            {
                var errors = new List<string>();
                
                // Basic validation
                if (string.IsNullOrWhiteSpace(Name))
                {
                    errors.Add("Distribution Board name is required");
                }
                
                if (_originalData?.AllCircuits == null || _originalData.AllCircuits.Count == 0)
                {
                    errors.Add("No circuits found in distribution board");
                }
                
                // Check for circuit validation errors
                if (_originalData?.AllCircuits != null)
                {
                    foreach (var circuit in _originalData.AllCircuits)
                    {
                        if (circuit != null && double.IsNaN(circuit.Load))
                        {
                            errors.Add($"Invalid load value in circuit {circuit.CCT_Number}");
                        }
                    }
                }
                
                ErrorMessages = errors;
                HasErrors = errors.Count > 0;
            }
            catch (Exception)
            {
                HasErrors = true;
                ErrorMessages = new List<string> { "Validation error occurred" };
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle IsLocked property change
        /// </summary>
        /// <param name="value">New locked state</param>
        partial void OnIsLockedChanged(bool value)
        {
            UpdateStatus();
            OnPropertyChanged(nameof(IsNotLocked));
        }

        /// <summary>
        /// Handle PassCount property change
        /// </summary>
        /// <param name="value">New pass count</param>
        partial void OnPassCountChanged(int value)
        {
            UpdateStatus();
        }

        /// <summary>
        /// Handle HasErrors property change
        /// </summary>
        /// <param name="value">New error state</param>
        partial void OnHasErrorsChanged(bool value)
        {
            UpdateStatus();
        }

        #endregion
    }
}
