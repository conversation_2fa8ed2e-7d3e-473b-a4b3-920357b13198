using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Service interface for all Revit API operations
    /// Provides thread-safe access to Revit API functionality through ExternalEvent architecture
    /// </summary>
    public interface IRevitService
    {
        #region Project Information Operations

        /// <summary>
        /// Save project information to Revit parameters (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="projectInfo">Project information to save</param>
        /// <returns>True if successful</returns>
        bool SaveProjectInfo(ProjectInfoModel projectInfo);

        /// <summary>
        /// Load project information from Revit parameters (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>Project information model</returns>
        ProjectInfoModel LoadProjectInfo();

        /// <summary>
        /// Commit project settings to Revit document (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="projectInfo">Project information to commit</param>
        /// <returns>True if successful</returns>
        bool CommitProjectInfo(PowerBIM_ProjectInfo projectInfo);

        #endregion

        #region Distribution Board Operations

        /// <summary>
        /// Load all distribution boards from the current Revit document (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>List of distribution board models</returns>
        List<DistributionBoardModel> LoadDistributionBoards();

        /// <summary>
        /// Save distribution board information to Revit (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoard">Distribution board to save</param>
        /// <returns>True if successful</returns>
        bool SaveDistributionBoard(DistributionBoardModel distributionBoard);

        /// <summary>
        /// Refresh distribution board summary information (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoards">Distribution boards to refresh</param>
        /// <returns>Updated distribution board list</returns>
        List<DistributionBoardModel> RefreshDistributionBoardSummary(List<DistributionBoardModel> distributionBoards);

        #endregion

        #region Circuit Operations

        /// <summary>
        /// Update circuits for specified distribution boards (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoards">Distribution boards containing circuits to update</param>
        /// <returns>True if successful</returns>
        bool UpdateCircuits(List<DistributionBoardModel> distributionBoards);

        /// <summary>
        /// Save circuit data to Revit (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuits">Circuits to save</param>
        /// <returns>True if successful</returns>
        bool SaveCircuitData(List<CircuitModel> circuits);

        /// <summary>
        /// Recalculate circuits for a specific distribution board (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoard">Distribution board to recalculate</param>
        /// <returns>True if successful</returns>
        bool RecalculateCircuits(DistributionBoardModel distributionBoard);

        /// <summary>
        /// Perform bulk edit operations on lighting circuits (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoard">Distribution board containing circuits</param>
        /// <param name="settings">Bulk edit settings</param>
        /// <returns>Number of circuits updated</returns>
        int BulkEditLighting(DistributionBoardModel distributionBoard, BulkEditSettings settings);

        /// <summary>
        /// Perform bulk edit operations on power circuits (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="distributionBoard">Distribution board containing circuits</param>
        /// <param name="settings">Bulk edit settings</param>
        /// <returns>Number of circuits updated</returns>
        int BulkEditPower(DistributionBoardModel distributionBoard, BulkEditSettings settings);

        #endregion

        #region Path Editing Operations

        /// <summary>
        /// Open path customizing view for circuit editing (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuit">Circuit to edit path for</param>
        /// <returns>True if successful</returns>
        bool OpenPathCustomizingView(CircuitModel circuit);

        /// <summary>
        /// Activate path edit view in Revit (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>True if successful</returns>
        bool ActivatePathEditView();

        /// <summary>
        /// Set manual length for a circuit (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuit">Circuit to update</param>
        /// <param name="length">Manual length value</param>
        /// <returns>True if successful</returns>
        bool SetCircuitLengthManual(CircuitModel circuit, double length);

        #endregion

        #region Element Selection and Validation

        /// <summary>
        /// Check if required elements exist in the document (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="document">Document to check</param>
        /// <returns>True if all required elements exist</returns>
        bool RequiredElementsExist(Document document);

        /// <summary>
        /// Get selected electrical equipment from Revit (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>List of selected electrical equipment</returns>
        List<Element> GetSelectedElectricalEquipment();

        /// <summary>
        /// Validate shared parameter file exists (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>True if shared parameter file exists</returns>
        bool ValidateSharedParameterFile();

        #endregion

        #region Transaction Management

        // REMOVED: Async transaction method - use synchronous version below

        /// <summary>
        /// Execute an operation within a Revit transaction (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="transactionName">Name of the transaction</param>
        /// <param name="operation">Operation to execute</param>
        /// <returns>True if successful</returns>
        bool ExecuteInTransaction(string transactionName, Func<bool> operation);

        #endregion

        #region Document Properties

        /// <summary>
        /// Get the current Revit document
        /// </summary>
        Document Document { get; }

        /// <summary>
        /// Get the current UI document
        /// </summary>
        UIDocument UIDocument { get; }

        /// <summary>
        /// Get the current UI application
        /// </summary>
        UIApplication UIApplication { get; }

        #endregion
    }

    /// <summary>
    /// Settings for bulk edit operations
    /// </summary>
    public class BulkEditSettings
    {
        public string CircuitType { get; set; }
        public double? LoadValue { get; set; }
        public string LoadUnit { get; set; }
        public bool OverwriteExisting { get; set; }
        public Dictionary<string, object> CustomSettings { get; set; } = new Dictionary<string, object>();
    }
}
