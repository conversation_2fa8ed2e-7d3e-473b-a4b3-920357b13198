﻿using Autodesk.Revit.DB;
using Common.UI.Forms;
using MEP.PowerBIM_5.CoreLogic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmElementsOnCircuit : BecaBaseForm
    {

        #region Fields

        PowerBIM_CircuitData _circuit;

        #endregion

        #region Properties



        #endregion

        #region Constructors

        public frmElementsOnCircuit(PowerBIM_CircuitData circuit)
        {
            InitializeComponent();
            _circuit = circuit;
            TitleText = "Elements on Circuit " + circuit.CCT_Number;
        }

        #endregion

        #region Methods

        #region UI

        private void frmElementsOnCircuit_Load(object sender, EventArgs e)
        {
            foreach (Element circuitElement in _circuit.CCT_Electrical_System.Elements)
            {
                rtb_CircuitElements.Text += Environment.NewLine + circuitElement.Name;
            }
        }

        #region UI Helpers



        #endregion

        #region Button Clicks



        #endregion

        #endregion

        #endregion


    }
}