﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities.RevitViewsUtilities;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using GEN.Easy3DView.CoreLogic;
using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_5.UI.Forms;
using View = Autodesk.Revit.DB.View;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_Start : BecaBaseForm
    {
        // For Modeless
        RequestHandler _handler;
        ExternalEvent _externalEvent;

        string _lockedDB = "(in use) ";

        //
        public List<PowerBIM_DBData> SelectedDBs = new List<PowerBIM_DBData>();
        public List<PowerBIM_DBData> AllDBs = new List<PowerBIM_DBData>();

        //
        public PowerBIM_DBData SelectedDBToManuallyLock { get; set; }
        public bool DBIsManuallyLock { get; set; }
        public PowerBIM_DBData SelectedDBToSaveUserNote { get; set; }

        //
        public PowerBIM_ProjectInfo projInfo;

        public DataGridView DgvDBSel { get { return dgvDBSel; } }

        // 
        private PowerBIM_CSVExport fileExport;

        private bool _projectInfoIsLocked;
        private string _projectInfoLockedOwner;

        private List<string> _curveOptions = new List<string>() { "B", "C", "D", "FUSE", "" };
        private List<string> _typeOptions = new List<string>() { "RCCB", "RCBO", "MCB", "" };

        #region Constructor

        public frmPowerBIM_Start(BecaActivityLoggerData logger, ExternalEvent exEvent, RequestHandler handler, List<PowerBIM_DBData> AllProjectDBs, PowerBIM_ProjectInfo pi)
        {
            // map across project info pointer
            projInfo = pi;

            // Create instance of verificaation report Class
            fileExport = new PowerBIM_CSVExport(projInfo);

            AllDBs = AllProjectDBs;

            // Show form
            InitializeComponent();

            // For Modeless
            _handler = handler;
            _externalEvent = exEvent;

            //
            // Read global setting from Proj Info class
            //
            if (projInfo.System_VD_Max_Perc == 0.05)
                guiSysVD5pc.Checked = true;
            else if (projInfo.System_VD_Max_Perc == 0.07)
                guiSysVD7pc.Checked = true;

            if (projInfo.Ambient_Temp == 30)
                guiNZcableSel.Checked = true;
            else if (projInfo.Ambient_Temp == 40)
                guiAUScableSel.Checked = true;

            if (projInfo.Discrimination_Test_Multiplier == 1.5)
                guiDiscrim1pt5Times.Checked = true;
            else if (projInfo.Discrimination_Test_Multiplier == 2)
                guiDiscrim2Times.Checked = true;

            //Installation method initialisation
            for (int i = 0; i <= 10; i++)
            {
                guiInstallLighting.Items.Add(PowerBIM_Constants.strInstallMethodsList[i]);
                guiInstallPower.Items.Add(PowerBIM_Constants.strInstallMethodsList[i]);
                guiInstallOther.Items.Add(PowerBIM_Constants.strInstallMethodsList[i]);
            }

            guiInstallLighting.SelectedIndex = PowerBIM_Constants.Default_LightingInstallMethod;
            guiInstallPower.SelectedIndex = PowerBIM_Constants.Default_PowerInstallMethod;
            guiInstallOther.SelectedIndex = PowerBIM_Constants.Default_OtherInstallMethod;

            // grey out buttton
            btnProjectParametersSave.Enabled = false;
            btnProjectParametersSave.BackColor = System.Drawing.Color.LightGray;

            // Disable Project Parameters if it's locked
            var owner = WorksharingUtils.GetWorksharingTooltipInfo(projInfo.Document, projInfo.Document.ProjectInformation.Id).Owner;
            if (WorksharingUtils.GetWorksharingTooltipInfo(projInfo.Document, projInfo.Document.ProjectInformation.Id).Owner !=
                projInfo.Document.Application.Username && owner != "")
            {
                rqProjectParam.Text = "(Locked) Project Parameters";
                _projectInfoIsLocked = true;
                _projectInfoLockedOwner = owner;
                //rqSettings.Enabled = false;
                btnProjectParametersSave.Enabled = false;
            }

            guiTxtCurveLighting.DataSource = _curveOptions;
            guiTxtDeviceLighting.DataSource = _typeOptions;
            guiTxtCurvePower.DataSource = _curveOptions;
            guiTxtDevicePower.DataSource = _typeOptions;
            guiTxtCurveOther.DataSource = _curveOptions;
            guiTxtDeviceOther.DataSource = _typeOptions;

            ShowOrHideDB_SettingsColumn();
        }

        #endregion

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.btnCancel.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        #region Button Clicks

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            if (Environment.UserDomainName == "BECAMAIL")
            {
                using (frmPowerBIM_Help rqFrmHelp = new frmPowerBIM_Help())
                {
                    rqFrmHelp.ShowDialog();
                }
            }
            else
            {
                using (var bedarHelpFrm = new frmBedarPowerBIM_Help())
                {
                    bedarHelpFrm.ShowDialog();
                }
            }

        }



        private void btnSettings_Click(object sender, EventArgs e)
        {
            if (!_projectInfoIsLocked)
            {
                frmPowerBIM_AdvancedSettings advadcedSettingsForm = new frmPowerBIM_AdvancedSettings(_externalEvent, _handler, projInfo);
                ModelessPowerBIM_AdvancedSettingsFormHandler.ProjInfo = projInfo;
                FormCollection fc = Application.OpenForms;
                foreach (System.Windows.Forms.Form frm in fc)
                {
                    if (frm.Name == advadcedSettingsForm.Name)
                        return;
                }
                advadcedSettingsForm.Show(this);

                btnProjectParametersSave.Enabled = true;
                btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"Project Information is locked by {_projectInfoLockedOwner}.\n\n" +
                    $"This user need to synchronise, and you will need to reload latest before you can edit");
            }

        }


        private void btnProjectParametersSave_Click(object sender, EventArgs e)
        {
            if (!_projectInfoIsLocked)
            {
                btnProjectParametersSave.Enabled = false;
                btnProjectParametersSave.BackColor = System.Drawing.Color.LightGray;

                ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;

                // save settings
                MakeRequest(RequestId.SaveSettings);

                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Success!", "Success!: \n\nSettings saved successfully. You may now need to re-run PowerBIM to update circuit results.");
            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"Project Information is locked by {_projectInfoLockedOwner}.\n\n" +
                    $"This user need to synchronise, and you will need to reload latest before you can edit");
            }
        }

        public void RegenerateCircuitProperties()
        {
            foreach (PowerBIM_DBData DB in AllDBs)
            {
                foreach (PowerBIM_CircuitData cct in DB.CCTs)
                {
                    cct.Refresh_DerrivedCircuitProperties();
                }
            }
        }


        private void btnExport_Click(object sender, EventArgs e)
        {
            this.WindowState = FormWindowState.Minimized;
            // Launch file export with selected DBs
            frmPowerBIM_Export FrmExport = new frmPowerBIM_Export(projInfo, SelectedDBs);
            FrmExport.Show();
            FrmExport.TopMost = true;
            //this.WindowState = FormWindowState.Minimized;
        }


        private void btnDBedit_Click(object sender, EventArgs e)
        {
            var selectedDb = AllDBs.Find(x => x.Schedule_DB_Name == SelectedDBs[0].Schedule_DB_Name);
            if (DBIsLocked(selectedDb))
                return;

            // Launch DB edit with selected DBs
            frmPowerBIM_DbEdit FrmDBedit = new frmPowerBIM_DbEdit(_externalEvent, _handler, projInfo, SelectedDBs);
            ModelessPowerBIM_DbEditFormHandler.DbEditForm = FrmDBedit;
            FrmDBedit.FormClosed += FrmPowerBIM_CircuitEditEnhanced_Closed;
            FrmDBedit.Show();
            FrmDBedit.WindowState = FormWindowState.Maximized;
            this.SetTopLevel(false);

        }



        private bool DBIsLocked(PowerBIM_DBData selectedDb)
        {
            if(selectedDb == null) return true;
            var sbOwner = new StringBuilder();
            foreach (var owner in selectedDb.CCTs.Select(x => x.LockedOwnerName).Distinct())
            {
                sbOwner.AppendLine(owner);
            }
            sbOwner.AppendLine(selectedDb.DB_Element.ElementOwner(projInfo.Document));

            ////Check if the user has manually locked this DB.
            //if (selectedDb.IsManuallyLocked)
            //{
            //    ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning: DB: ", selectedDb.Schedule_DB_Name + " is has been manually locked.");
            //    return true;
            //}

            // If there are locked DB we cant use this function
            if (selectedDb.DB_Element.IsLocked(projInfo.Document))
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning: worksharing issue", selectedDb.Schedule_DB_Name + " is locked!\nThis DB is locked because\n" + sbOwner.ToString() +
                    "\nis currently editing circuits on this DB\nThis user need to synchronise, and you will need to reload latest before you can edit");
                return true;
            }
            else
            {
                return false;
            }
        }

        private bool MultipleDBIsSelected(List<PowerBIM_DBData> selectedDBs)
        {
            if (selectedDBs.Count() > 1)
            {
                ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning", "This function can't be used on multiple or locked DBs. \n\nPlease select only a single DB");
                return true;
            }
            else
            {
                return false;
            }
        }


        private void btnEnhancedCircuitEdit_Click(object sender, EventArgs e)
        {
            if (MultipleDBIsSelected(SelectedDBs))
                return;

            var selectedDb = AllDBs.Find(x => x.Schedule_DB_Name == SelectedDBs[0].Schedule_DB_Name);
            if (DBIsLocked(selectedDb))
                return;

            if (ModelessPowerBIM_CircuitEditEnhancedFormHandler._circuitEditEnhancedForm == null
                    || ModelessPowerBIM_CircuitEditEnhancedFormHandler._circuitEditEnhancedForm.IsDisposed)
            {
                FrmPowerBIM_CircuitEditEnhanced frmCircuitEdit = new FrmPowerBIM_CircuitEditEnhanced(_externalEvent, _handler, projInfo, selectedDb);
                //frmCircuitEdit.TopMost = true;
                ModelessPowerBIM_CircuitEditEnhancedFormHandler._circuitEditEnhancedForm = frmCircuitEdit;
                frmCircuitEdit.FormClosed += FrmPowerBIM_CircuitEditEnhanced_Closed;

                this.SetTopLevel(false);
            }

            ModelessPowerBIM_CircuitEditEnhancedFormHandler._circuitEditEnhancedForm.Show();

            if (SelectedDBs.Count() == 1)
            {


            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "This function can't be used on multiple or locked DBs. \n\nPlease select only a single DB");
            }
        }

        private void FrmPowerBIM_CircuitEditEnhanced_Closed(object sender, FormClosedEventArgs e)
        {
            // Once closed, we can update pass/fail count for all circuits
            SelectedDBs[0].Check_PassFailWarningCount();

            // Updage DGV
            Update_dgvDBSel();

            // Then update DB coloruing
            GUI_colourDBNamesBasedOnCircuitResults();
        }


        private void btnSave_Click(object sender, EventArgs e)
        {
            {
                ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
                // save settings
                MakeRequest(RequestId.SavePowerBIM);

                //// flag warnings
                //GUI_flagWarnings();

                //string flatDBList = "";
                //foreach (var DB in SelectedDBs)
                //{
                //    if (!DB.CCTs.Any(x => x.IsLocked))
                //        flatDBList += "\n -" + DB.Schedule_DB_Name;
                //}

                //// TODO actually save the circuit results!!!!

                //TaskDialog.Show("COMPLETE", "The Following DBs have been updated in Revit \n" + flatDBList);
            }
        }


        private void btnRunSizer_Click(object sender, EventArgs e)
        {
            ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
            MakeRequest(RequestId.IterateCircuits);

        }

        public void RunAutoSizer()
        {
            string bigResult = "";

            foreach (var selDB in SelectedDBs)
            {

                // counter for failed ccts
                int autoselfail = 0;
                int autoselpass = 0;
                int autoselignored = 0;

                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in selDB.CCTs)
                {
                    var isOneElement = false;
                    if (CCT.Number_Of_Elements == 1)
                        isOneElement = true;

                    // First, update all information inclase something has changed
                    CCT.Refresh_DerrivedCircuitProperties();

                    // If the circuit is not a "Spare" or "Space"
                    if (!CCT.CCT_Is_Spare_Or_Space)
                    {
                        // If overwrite cable
                        if (rbSizerOverwriteExisting.Checked)
                        {
                            // If we are autoselecting cables, run the method
                            if (CCT.Auto_SelectCable(isOneElement))
                                autoselpass++;
                            else
                                autoselfail++;
                        }
                        //else if ((CCT.Schedule_Cable_To_First != "-" && CCT.Schedule_Cable_To_First != PowerBIM_CableData.InvalidCableName && CCT.Schedule_Cable_To_First != "") && CCT.Schedule_Cable_To_Final == "-")
                        //{
                        //    CCT.Schedule_Cable_To_Final = CCT.Schedule_Cable_To_First;
                        //    //// If we are autoselecting cables, run the method
                        //    //if (CCT.Auto_SelectCable(isOneElement))
                        //    //    autoselpass++;
                        //    //else
                        //    //    autoselfail++;

                        //}

                        // Or if the cct is blank
                        else if ((CCT.Schedule_Cable_To_First == "" && CCT.Schedule_Cable_To_First == "")
                            || (CCT.Schedule_Cable_To_First == PowerBIM_CableData.InvalidCableName && CCT.Schedule_Cable_To_First == PowerBIM_CableData.InvalidCableName))
                        {
                            // If we are autoselecting cables, run the method
                            if (CCT.Auto_SelectCable(isOneElement))
                                autoselpass++;
                            else
                                autoselfail++;
                        }
                        else
                            autoselignored++;

                    }
                }

                bigResult += UpdateDBSummary(bigResult) + Environment.NewLine;

                //// report hoe many ccts fail
                //TaskDialog td = new TaskDialog("Auto-sizerer");
                //td.MainContent = "Autosizering complete. \n\n" + bigResult +
                //    "\n Click either [OK] to Commit, or [Cancel] to Roll Back the sizering";
                //TaskDialogCommonButtons buttons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                //td.CommonButtons = buttons;

                //if (TaskDialogResult.Ok == td.Show())
                //{
                //    // Write all our good work back to the schedule
                //    CommitCircuitDataInStartForm(selDB);
                //}



                // TODO add method to resotre old data if changes are disposed

                // Once closed, we can update pass/fail count for all circuits
                selDB.Check_PassFailWarningCount();

            }


            var dialogResult = UI.Forms.ModelessPowerBIM_StartFormHandler.ShowDialogMsgToTheUser("Auto-sizerer", bigResult +
                   "\n Click either [OK] to Commit, or [Cancel] to Roll Back the sizering");
            if (dialogResult == DialogResult.OK)
            {
                foreach (var selDB in SelectedDBs)
                {

                    // Write all our good work back to the schedule
                    CommitCircuitDataInStartForm(selDB);
                }

            }


            // Updage DGV
            UpdaterRequired_Selected();
            Update_dgvDBSel();

            // Then update DB coloruing
            GUI_colourDBNamesBasedOnCircuitResults();

        }

        private string UpdateDBSummary(string bigResult)
        {
            // Update DB Summary
            var sb = new StringBuilder();
            foreach (PowerBIM_DBData db in SelectedDBs)
            {
                var dBNotCommited = string.Empty;
                db.Run_DBCheckAllCircuits(out dBNotCommited);
                if (dBNotCommited != string.Empty)
                    sb.AppendLine(dBNotCommited);

                db.Check_PassFailWarningCount();

                bigResult += db.DB_Result_Summary + "\n\n";
            }
            if (sb.Length > 0)
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "These DBs cannot be commited:\n\n" + sb.ToString());
            return bigResult;
        }

        private void CommitCircuitDataInStartForm(PowerBIM_DBData selDb)
        {
            StringBuilder sb;
            selDb.Commit_CircuitData(out sb);
            if (sb.Length > 0)
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "These circuits cannot be commited:\n\n" + sb.ToString());
        }

        private void btnWriteLighting_Click(object sender, EventArgs e)
        {
            ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
            MakeRequest(RequestId.WriteLightingToSchedule);
        }

        public void WriteLightingToSchedule()
        {
            // Going through each selected DBs
            foreach (PowerBIM_DBData DB in SelectedDBs)
            {
                //Start the circuit counter
                int cctsTotal = 0;
                bool wasValueSet = false;
                int cctsUpdated = 0;

                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    // First, update all information inclase something has changed
                    CCT.Refresh_DerrivedCircuitProperties();

                    // If the circuit is not a "Spare" or "Space"
                    if (CCT.CCT_Is_Spare_Or_Space == false)
                    {
                        if (CCT.Schedule_Description.Length >= 8 && CCT.Schedule_Description.Substring(0, 8).ToUpper() == "LIGHTING")
                        {

                            //
                            // Fill out the values as assigned. 
                            //
                            if (cbRatingLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Trip_Rating == 0 || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Breaker.Schedule_Trip_Rating = double.Parse(guiTxtRatingLighting.Text);
                                    wasValueSet = true;
                                }

                            if (cbCurveLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Curve_Type == null || CCT.Breaker.Schedule_Curve_Type == "" || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Breaker.Schedule_Curve_Type = guiTxtCurveLighting.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbDeviceLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Protective_Device == null || CCT.Breaker.Schedule_Protective_Device == "" || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Breaker.Schedule_Protective_Device = guiTxtDeviceLighting.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbRCDRatingLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_RCD == null || CCT.Schedule_RCD == "" || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Schedule_RCD = guiTxtRCDratingLighting.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbDiversityLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.CCT_Diversity == 0 || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.CCT_Diversity = double.Parse(guiTxtDiversityLighting.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }

                            if (cbDeratingFactorLighting.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_Derating_Factor == 0 || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Schedule_Derating_Factor = double.Parse(guiTxtDeratingFactorLighting.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }

                            if (cbCircuitRevisionLighting.Checked)
                            {
                                // Check that the value is null, or unassigned
                                //if (CCT.Schedule_Revision == null || CCT.Schedule_Revision == "" || rbOverwriteExistingLighting.Checked)
                                //{
                                CCT.Schedule_Revision = guiTxtRevisionLighting.Text.ToString();
                                wasValueSet = true;
                                //}
                            }
                            if (cbInstallMethodLighting.Checked)
                            {
                                if (CCT.Schedule_Install_Method == "Invalid" || CCT.Schedule_Install_Method == null || CCT.Schedule_Install_Method == "" || rbOverwriteExistingLighting.Checked)
                                {
                                    CCT.Schedule_Install_Method = guiInstallLighting.SelectedIndex.ToString();
                                    CCT.MatchUserEnteredInstallMethod();
                                    wasValueSet = true;
                                    //Schedule_Install_Method = PowerBIM_Constants.strInstallMethodsList[projInfo.GUI_Install_Method_Lighting_Selected_Index];
                                }
                            }




                            // Run length engine
                            if (rbOverwriteExistingLighting.Checked == true || (CCT.LengthClass.Length_To_First == 0 && CCT.LengthClass.Length_Total == 0))
                            {


                                wasValueSet = true;
                            }


                            if (wasValueSet)
                                cctsUpdated++;

                            cctsTotal++;
                        }
                    }
                }

                //Write all our good work back to the schedule

                // promt the user to confirm they want to commit to revit
                //TaskDialog td = new TaskDialog("Bulk Edit - Lighting");
                //td.MainContent = DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " Lighting circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                //    + "Click [OK] to Commit to revit";

                //TaskDialogCommonButtons buttons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                //td.CommonButtons = buttons;

                //if (TaskDialogResult.Ok == td.Show())
                //    CommitCircuitDataInStartForm(DB);

                var dialogResult = UI.Forms.ModelessPowerBIM_StartFormHandler.ShowDialogMsgToTheUser("Bulk Edit - Lighting", DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " Lighting circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                    + "Click [OK] to Commit to revit");
                if (dialogResult == DialogResult.OK)
                    CommitCircuitDataInStartForm(DB);

                // We should reload all circuit properties after doing this.... 
                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    CCT.Refresh_DerrivedCircuitProperties();
                }

                // Update reuired for DBs
                UpdaterRequired_Selected();
            }
        }


        private void btnWritePower_Click(object sender, EventArgs e)
        {
            ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
            MakeRequest(RequestId.WritePowerToSchedule);
        }

        public void WritePowerToSchedule()
        {
            // Going through each selected DBs
            foreach (PowerBIM_DBData DB in SelectedDBs)
            {
                //Start the circuit counter
                int cctsTotal = 0;
                bool wasValueSet = false;
                int cctsUpdated = 0;

                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    // First, update all information inclase something has changed
                    CCT.Refresh_DerrivedCircuitProperties();

                    // If the circuit is not a "Spare" or "Space"
                    if (CCT.CCT_Is_Spare_Or_Space == false)
                    {
                        if (CCT.Schedule_Description.Length >= 5 && CCT.Schedule_Description.Substring(0, 5).ToUpper() == "POWER")
                        {

                            //
                            // Fill out the values as assigned. 
                            //
                            if (cbRatingPower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Trip_Rating == 0 || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Breaker.Schedule_Trip_Rating = double.Parse(guiTxtRatingPower.Text);
                                    wasValueSet = true;
                                }
                            }

                            if (cbCurvePower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Curve_Type == null || CCT.Breaker.Schedule_Curve_Type == "" || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Breaker.Schedule_Curve_Type = guiTxtCurvePower.Text.ToString();
                                    wasValueSet = true;
                                }
                            }

                            if (cbDevicePower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Protective_Device == null || CCT.Breaker.Schedule_Protective_Device == "" || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Breaker.Schedule_Protective_Device = guiTxtDevicePower.Text.ToString();
                                    wasValueSet = true;
                                }
                                else
                                {

                                }
                            }
                            if (cbRCDRatingPower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_RCD == null || CCT.Schedule_RCD == "" || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Schedule_RCD = guiTxtRCDratingPower.Text.ToString();
                                    wasValueSet = true;
                                }
                            }

                            if (cbDiversityPower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.CCT_Diversity == 0 || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.CCT_Diversity = double.Parse(guiTxtDiversityPower.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }
                            }

                            if (cbDeratingFactorPower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_Derating_Factor == 0 || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Schedule_Derating_Factor = double.Parse(guiTxtDeratingFactorPower.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }
                            }

                            if (cbCircuitRevisionPower.Checked)
                            {
                                // Check that the value is null, or unassigned
                                //if (CCT.Schedule_Revision == null || CCT.Schedule_Revision == "" || rbOverwriteExistingPower.Checked)
                                //{
                                CCT.Schedule_Revision = guiTxtRevisionPower.Text.ToString();
                                wasValueSet = true;
                                //}
                            }

                            if (cbInstallMethodPower.Checked)
                            {
                                if (CCT.Schedule_Install_Method == "Invalid" || CCT.Schedule_Install_Method == null || CCT.Schedule_Install_Method == "" || rbOverwriteExistingPower.Checked)
                                {
                                    CCT.Schedule_Install_Method = guiInstallPower.SelectedIndex.ToString();
                                    CCT.MatchUserEnteredInstallMethod();
                                    wasValueSet = true;
                                    //Schedule_Install_Method = PowerBIM_Constants.strInstallMethodsList[projInfo.GUI_Install_Method_Power_Selected_Index];
                                }
                            }




                            // Run length engine
                            if (rbOverwriteExistingPower.Checked == true || (CCT.LengthClass.Length_To_First == 0 && CCT.LengthClass.Length_Total == 0))
                            {

                                wasValueSet = true;
                            }


                            if (wasValueSet)
                                cctsUpdated++;

                            cctsTotal++;
                        }
                    }

                }

                //Write all our good work back to the schedule

                // promt the user to confirm they want to commit to revit
                //TaskDialog td = new TaskDialog("Bulk Edit - 'Power'");
                //td.MainContent = DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " 'Power' circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                //    + "Click [OK] to Commit to revit";

                //TaskDialogCommonButtons buttons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                //td.CommonButtons = buttons;

                //if (TaskDialogResult.Ok == td.Show())
                //    CommitCircuitDataInStartForm(DB);

                var dialogResult = UI.Forms.ModelessPowerBIM_StartFormHandler.ShowDialogMsgToTheUser("Bulk Edit - 'Power'", DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " 'Power' circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                    + "Click [OK] to Commit to revit");
                if (dialogResult == DialogResult.OK)
                {
                    CommitCircuitDataInStartForm(DB);
                }

                // We should reload all circuit properties after doing this.... 
                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    CCT.Refresh_DerrivedCircuitProperties();
                }

                // Update reuired for DBs
                UpdaterRequired_Selected();
            }
        }


        private void btnWriteOther_Click(object sender, EventArgs e)
        {
            ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
            MakeRequest(RequestId.WriteOtherToSchedule);
        }

        public void WriteOtherToSchedule()
        {
            // Going through each selected DBs
            foreach (PowerBIM_DBData DB in SelectedDBs)
            {
                //Start the circuit counter
                int cctsTotal = 0;
                bool wasValueSet = false;
                int cctsUpdated = 0;

                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    // First, update all information inclase something has changed
                    CCT.Refresh_DerrivedCircuitProperties();

                    // If the circuit is not a "Spare" or "Space"
                    if (CCT.CCT_Is_Spare_Or_Space == false)
                    {
                        if (CCT.Schedule_Description.Length >= 5 && CCT.Schedule_Description.Substring(0, 5).ToUpper() == "OTHER")
                        {

                            //
                            // Fill out the values as assigned. 
                            //
                            if (cbRatingOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Trip_Rating == 0 || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Breaker.Schedule_Trip_Rating = double.Parse(guiTxtRatingOther.Text);
                                    wasValueSet = true;
                                }

                            if (cbCurveOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Curve_Type == null || CCT.Breaker.Schedule_Curve_Type == "" || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Breaker.Schedule_Curve_Type = guiTxtCurveOther.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbDeviceOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Breaker.Schedule_Protective_Device == null || CCT.Breaker.Schedule_Protective_Device == "" || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Breaker.Schedule_Protective_Device = guiTxtDeviceOther.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbRCDRatingOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_RCD == null || CCT.Schedule_RCD == "" || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Schedule_RCD = guiTxtRCDratingOther.Text.ToString();
                                    wasValueSet = true;
                                }

                            if (cbDiversityOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.CCT_Diversity == 0 || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.CCT_Diversity = double.Parse(guiTxtDiversityOther.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }

                            if (cbDeratingFactorOther.Checked)
                                // Check that the value is null, or unassigned
                                if (CCT.Schedule_Derating_Factor == 0 || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Schedule_Derating_Factor = double.Parse(guiTxtDeratingFactorOther.Text.Substring(0, 3)) / 100;
                                    wasValueSet = true;
                                }

                            if (cbCircuitRevisionOther.Checked)
                            {
                                // Check that the value is null, or unassigned
                                //if (CCT.Schedule_Revision == null || CCT.Schedule_Revision == "" || rbOverwriteExistingOther.Checked)
                                //{
                                CCT.Schedule_Revision = guiTxtRevisionOther.Text.ToString();
                                wasValueSet = true;
                                //}
                            }
                            if (cbInstallMethodOther.Checked)
                            {
                                if (CCT.Schedule_Install_Method == "Invalid" || CCT.Schedule_Install_Method == null || CCT.Schedule_Install_Method == "" || rbOverwriteExistingOther.Checked)
                                {
                                    CCT.Schedule_Install_Method = guiInstallOther.SelectedIndex.ToString();
                                    CCT.MatchUserEnteredInstallMethod();
                                    wasValueSet = true;
                                    //Schedule_Install_Method = PowerBIM_Constants.strInstallMethodsList[projInfo.GUI_Install_Method_Other_Selected_Index];
                                }
                            }



                            if (wasValueSet)
                                cctsUpdated++;

                            cctsTotal++;
                        }
                    }
                }

                //Write all our good work back to the schedule

                // promt the user to confirm they want to commit to revit
                //TaskDialog td = new TaskDialog("Bulk Edit - 'Other'");
                //td.MainContent = DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " 'Other' circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                //    + "Click [OK] to Commit to revit";

                //TaskDialogCommonButtons buttons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
                //td.CommonButtons = buttons;

                //if (TaskDialogResult.Ok == td.Show())
                //    CommitCircuitDataInStartForm(DB);


                var dialogResult = UI.Forms.ModelessPowerBIM_StartFormHandler.ShowDialogMsgToTheUser("Bulk Edit - 'Other'", DB.Schedule_DB_Name + "update complete \n\n - " + cctsTotal + " 'Other' circuits found \n - " + cctsUpdated + "/" + cctsTotal + " Circuits updated \n\n"
                    + "Click [OK] to Commit to revit");
                if (dialogResult == DialogResult.OK)
                {
                    CommitCircuitDataInStartForm(DB);
                }

                // We should reload all circuit properties after doing this.... 
                // Going through each circuit
                foreach (PowerBIM_CircuitData CCT in DB.CCTs)
                {
                    CCT.Refresh_DerrivedCircuitProperties();
                }

                // Update reuired for DBs
                UpdaterRequired_Selected();
            }
        }

        private void btnRun_Click(object sender, EventArgs e)
        {
            ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
            MakeRequest(RequestId.RunCircuitCheckManual);
        }

        public void RunCircuitCheckManual()
        {
            //added CPD select
            projInfo.GUI_Gen_CPD_Range_Selected_Index = rqCPDrange.SelectedIndex;
            //_startForm.projInfo.GUI_Gen_CPD_Range_Selected_Index = rqCPDrange.SelectedIndex;

            // Protective device settings

            // Cable Summary Reports
            //_startForm.projInfo.GUI_CheckDB_Run = guiCheckDBcircuits.Checked;

            // TODO add safety factor selection in advanced settings
            projInfo.GUI_Calc_Lengths_Extra_Termination = PowerBIM_Constants.ExtraLengthPerTermination;


            // Passing back Cable Database Selected
            if (guiAUScableSel.Checked)
            {
                projInfo.Cable_Database_Name = "Cable Database AU";
                projInfo.Ambient_Temp = 40;
                projInfo.Parameters_Changed = true;
            }
            else if (guiNZcableSel.Checked)
            {
                projInfo.Cable_Database_Name = "Cable Database NZ";
                projInfo.Ambient_Temp = 30;
                projInfo.Parameters_Changed = true;
            }

            // Passing back System Max VD % selected
            if (guiSysVD5pc.Checked)
            {
                projInfo.System_VD_Max_Perc = 0.05;
                projInfo.Parameters_Changed = true;
            }
            else if (guiSysVD7pc.Checked)
            {
                projInfo.System_VD_Max_Perc = 0.07;
                projInfo.Parameters_Changed = true;
            }

            // Passing back System Max VD % selected
            if (guiDiscrim1pt5Times.Checked)
            {
                projInfo.Discrimination_Test_Multiplier = 1.5;
                projInfo.Parameters_Changed = true;
            }

            else if (guiDiscrim2Times.Checked)
            {
                projInfo.Discrimination_Test_Multiplier = 2;
                projInfo.Parameters_Changed = true;
            }

            // Passing back CPD Selected
            if (projInfo.GUI_Gen_CPD_Range_Selected_Index != -1)
            {
                projInfo.CPD_Range_Selected = PowerBIM_Constants.strCPDRangeList[projInfo.GUI_Gen_CPD_Range_Selected_Index];
            }
            else
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning", "Please select proper protective device product. Current value that was saved : " + projInfo.CPD_Range_Selected);
            }

            // Commit settings to revit
            projInfo.CommitProjectInfo();

            // flag warnings
            GUI_flagWarnings();

            // update project info based on settings
            projInfo.UpdateAllProjectInfo();

            //
            // Start Run
            //
            string bigResult = "";
            UpdateDBSummary(bigResult);

            ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("PowerBIM Run Complete!", bigResult);

            // Updage DGV
            UpdaterRequired_Clear();
            Update_dgvDBSel();


            // Colour DB names green or red for pass or fail
            GUI_colourDBNamesBasedOnCircuitResults();

            return;
        }

        #endregion

        private void GUI_colourDBNamesBasedOnCircuitResults()
        {
            int i = 0;
            foreach (PowerBIM_DBData DB in AllDBs)
            {

                // if all circuits pass we make green
                if (DB.DB_All_Circuits_Pass == true)
                {
                    // If all circuits pass on DB, make green
                    dgvDBSel.Rows[i].DefaultCellStyle.ForeColor = System.Drawing.Color.Green;
                }

                // if even once circuit fails we set to red
                else if (DB.DB_All_Circuits_Pass == false)
                {
                    // If even one circuit fail, make red
                    dgvDBSel.Rows[i].DefaultCellStyle.ForeColor = System.Drawing.Color.Red;
                }

                // If there's a problem with the DB (such as some DB data being missing) we'll set the cells to grey
                else if (DB.Parameters_Missing == true || DB.Data_Good == false)
                {
                    // If DB parameters are missing and cant comute. Make grey. 
                    dgvDBSel.Rows[i].DefaultCellStyle.ForeColor = System.Drawing.Color.LightGray;
                }

                i++;
                if (i > dgvDBSel.Rows.Count)
                {
                    return;
                }
            }
        }


        private void GUI_flagWarnings()
        {
            //
            // Flag warnings if settings are not set.
            //

            if (!((true == (guiNZcableSel.Checked || guiAUScableSel.Checked))))
            {
                // display popup
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Error: \nUnable to run PowerBIM. Please select project location");

                //End method early
                return;
            }

            if (!((true == (guiSysVD7pc.Checked || guiSysVD5pc.Checked))))
            {
                // display popup
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Error: \nUnable to run PowerBIM. Please select system max volt drop");

                //End method early
                return;
            }

            if (!((true == (guiDiscrim1pt5Times.Checked || guiDiscrim2Times.Checked))))
            {
                // display popup
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Error: \nUnable to run PowerBIM. Please select discrimination limit");

                //End method early
                return;
            }

            if (rqCPDrange.SelectedIndex == 0)
            {
                // display popup
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Error: \nUnable to run PowerBIM. Please select a protective device manufacturer");

                //End method early
                return;
            }
        }

        public void saveSettings()
        {
            // 
            // If the user has changed a setting in the GUI, then record this change of settings in projInfo
            //

            //added CPD select
            projInfo.GUI_Gen_CPD_Range_Selected_Index = rqCPDrange.SelectedIndex;

            // Protective device settings

            // TODO add safety factor selection in advanced settings
            projInfo.GUI_Calc_Lengths_Extra_Termination = PowerBIM_Constants.ExtraLengthPerTermination;


            // Passing back Cable Database Selected
            if (guiAUScableSel.Checked)
            {
                projInfo.Cable_Database_Name = "Cable Database AU";
                projInfo.Ambient_Temp = 40;
                projInfo.Parameters_Changed = true;
            }
            else if (guiNZcableSel.Checked)
            {
                projInfo.Cable_Database_Name = "Cable Database NZ";
                projInfo.Ambient_Temp = 30;
                projInfo.Parameters_Changed = true;
            }

            // Passing back System Max VD % selected
            if (guiSysVD5pc.Checked)
            {
                projInfo.System_VD_Max_Perc = 0.05;
                projInfo.Parameters_Changed = true;
            }
            else if (guiSysVD7pc.Checked)
            {
                projInfo.System_VD_Max_Perc = 0.07;
                projInfo.Parameters_Changed = true;
            }

            // Passing back System Max VD % selected
            if (guiDiscrim1pt5Times.Checked)
            {
                projInfo.Discrimination_Test_Multiplier = 1.5;
                projInfo.Parameters_Changed = true;
            }

            else if (guiDiscrim2Times.Checked)
            {
                projInfo.Discrimination_Test_Multiplier = 2;
                projInfo.Parameters_Changed = true;
            }

            // Passing back CPD Selected
            if (projInfo.GUI_Gen_CPD_Range_Selected_Index == -1)
            {
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warning", "Protective device product is invalid.Saved value is" + projInfo.CPD_Range_Selected);
            }
            else
                projInfo.CPD_Range_Selected = PowerBIM_Constants.strCPDRangeList[projInfo.GUI_Gen_CPD_Range_Selected_Index];

            // flag warnings
            GUI_flagWarnings();

            string flatDBList = "";
            foreach (var DB in SelectedDBs)
            {
                if (!DB.CCTs.Any(x => x.IsLocked))
                    flatDBList += "\n -" + DB.Schedule_DB_Name;
            }

            // TODO actually save the circuit results!!!!

            //UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("COMPLETE", "The Following DBs have been updated in Revit \n" + flatDBList);
        }


        private void dgv_SelectedIndexChanged(object sender, EventArgs e)
        {
            // we update the list of selected DBS based on any changes to the LV
            SelectedDBs.Clear();

            // added for multiselect
            foreach (DataGridViewRow row in dgvDBSel.SelectedRows)
            {
                SelectedDBs.Add(AllDBs.ElementAt(row.Index));
            }
        }


        private void frm_Load(object sender, EventArgs e)
        {
            Load_dgvDBSel();
            Load_CPDSel();
            Update_dgvDBSel();
        }

        #region Load

        private void Load_dgvDBSel()
        {
            int i = 0;
            List<string> lockedDBNames = new List<string>();

            foreach (var DB in AllDBs)
            {
                DataRow dr = dBSummaryDataSet.Tables[0].NewRow();

                dr.ItemArray = new object[]
                {
                    // Inputs
                    DB.Schedule_DB_Name,
                    DB.Result_PassCount,
                    DB.Result_WarningCount,
                    DB.Result_FailCount,
                    DB.User_Notes,
                    DB.GUI_Notes,
                    DB.Update_Required
                };

                dBSummaryDataSet.Tables[0].Rows.Add(dr);

                if (DB.LockedCircuits.Count() > 0)
                    lockedDBNames.Add(DB.Schedule_DB_Name);

                if (DB.DB_Element.IsLocked(projInfo.Document) && !lockedDBNames.Contains(DB.Schedule_DB_Name))
                    lockedDBNames.Add(DB.Schedule_DB_Name);

            }

            foreach (DataGridViewRow dgvr in dgvDBSel.Rows)
            {
                SelectedDBToManuallyLock = AllDBs.Find(d => d.DB_Element.Name == dgvr.Cells[1].Value.ToString());

                // Set the appropriate image based on whether db is null or manually locked on the image column
                dgvr.Cells[ManualLock.Index].Value = SelectedDBToManuallyLock == null
                    ? Properties.Resources.warning
                    : (SelectedDBToManuallyLock.IsManuallyLocked ? Properties.Resources.Locked : Properties.Resources.Unlocked);
                DBIsManuallyLock = SelectedDBToManuallyLock.IsManuallyLocked;

                // select if this is the current DB
                if (dgvr.Index == projInfo.GUI_Gen_DB_Selected_Position)
                {
                    dgvDBSel.ClearSelection();
                    dgvr.Selected = true;
                }

                if (dgvr.Cells[dBNotesDataGridViewTextBoxColumn.Index].Value.ToString() == "View Warnings")
                {
                    dgvr.Cells[dBNotesDataGridViewTextBoxColumn.Index] = new DataGridViewButtonCell();
                    dgvr.Cells[dBNotesDataGridViewTextBoxColumn.Index].Value = "View Warnings";
                }

                // Force the button cell to refresh its display
                dgvDBSel.InvalidateCell(DB_UserNotes.Index, dgvr.Index);

                // Make read only for DBs that have any circuit locked
                if (lockedDBNames.Contains(dgvr.Cells[1].Value.ToString()))
                {
                    dgvr.Cells[1].Value = _lockedDB + dgvr.Cells[1].Value.ToString();
                    dgvr.DefaultCellStyle.BackColor = System.Drawing.Color.LightGray;
                    dgvr.DefaultCellStyle.ForeColor = System.Drawing.Color.White;
                }
                else
                {
                    dgvr.DefaultCellStyle.BackColor = System.Drawing.Color.LightGray;
                    dgvr.DefaultCellStyle.ForeColor = System.Drawing.Color.White;
                }

                dgvr.Cells[DB_Settings.Index].Value = "Set";
            }

            GUI_colourDBNamesBasedOnCircuitResults();
        }

        private void UpdateGUINotes()
        {
            foreach (var DB in AllDBs)
            {
                DB.Update_GUI_Notes();
            }

            Update_dgvDBSel();
        }

        public void UpdaterRequired_All()
        {
            foreach (var DB in AllDBs)
            {
                DB.Update_Required = true;
            }

            Update_dgvDBSel();
        }

        private void UpdaterRequired_Selected()
        {
            foreach (var DB in SelectedDBs)
            {
                DB.Update_Required = true;
            }

            Update_dgvDBSel();
        }

        private void UpdaterRequired_Clear()
        {
            foreach (var DB in AllDBs)
            {
                DB.Update_Required = false;
            }

            Update_dgvDBSel();
        }

        private void Load_CPDSel()
        {
            //add CPD select
            for (int i = 0; i <= (PowerBIM_Constants.strCPDRangeList.Length - 1); i++)
            {
                rqCPDrange.Items.Add(PowerBIM_Constants.strCPDRangeList[i]);
            }

            var cPDParameter = projInfo.Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBCPDManufacturer);
            if (cPDParameter.HasValue && cPDParameter.AsInteger() > 0)
            {
                rqCPDrange.SelectedIndex = cPDParameter.AsInteger();
                projInfo.GUI_Gen_CPD_Range_Selected_Index = cPDParameter.AsInteger();
            }
            else
            {
                rqCPDrange.SelectedIndex = 2;
                projInfo.GUI_Gen_CPD_Range_Selected_Index = 2;
            }
        }

        #endregion

        private void Update_dgvDBSel()
        {
            int index = 0;

            foreach (var DB in AllDBs)
            {
                DataRow row = dBSummaryDataSet.Tables[0].Rows[index];

                row.SetField<int>("DB_PassCount", DB.Result_PassCount);
                row.SetField<int>("DB_WarningCount", DB.Result_WarningCount);
                row.SetField<int>("DB_FailCount", DB.Result_FailCount);
                row.SetField<string>("DB_UserNotes", !string.IsNullOrEmpty(DB.User_Notes) ? "Edit notes" : "Add notes");
                row.SetField<string>("DB_Notes", DB.GUI_Notes);
                row.SetField<bool>("DB_UpdateRequired", DB.Update_Required);

                index++;
            }

            dgvDBSel.Refresh();
        }


        #region Checkbox_Changed

        private void cbRatingLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRatingLighting.Checked)
                guiTxtRatingLighting.Enabled = true;
            else
                guiTxtRatingLighting.Enabled = false;
        }

        private void cbCurveLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCurveLighting.Checked)
                guiTxtCurveLighting.Enabled = true;
            else
                guiTxtCurveLighting.Enabled = false;
        }

        private void cbDeviceLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDeviceLighting.Checked)
                guiTxtDeviceLighting.Enabled = true;
            else
                guiTxtDeviceLighting.Enabled = false;
        }

        private void cbDeratingFactorLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDeratingFactorLighting.Checked)
                guiTxtDeratingFactorLighting.Enabled = true;
            else
                guiTxtDeratingFactorLighting.Enabled = false;
        }

        private void cbRCDRatingLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRCDRatingLighting.Checked)
                guiTxtRCDratingLighting.Enabled = true;
            else
                guiTxtRCDratingLighting.Enabled = false;
        }

        private void cbDiversityLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDiversityLighting.Checked)
                guiTxtDiversityLighting.Enabled = true;
            else
                guiTxtDiversityLighting.Enabled = false;
        }

        private void cbCircuitRevisionLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCircuitRevisionLighting.Checked)
                guiTxtRevisionLighting.Enabled = true;
            else
                guiTxtRevisionLighting.Enabled = false;
        }

        private void cbInstallMethodLighting_CheckedChanged(object sender, EventArgs e)
        {
            if (cbInstallMethodLighting.Checked)
                guiInstallLighting.Enabled = true;
            else
                guiInstallLighting.Enabled = false;
        }


        private void cbRatingPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRatingPower.Checked)
                guiTxtRatingPower.Enabled = true;
            else
                guiTxtRatingPower.Enabled = false;
        }

        private void cbCurvePower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCurvePower.Checked)
                guiTxtCurvePower.Enabled = true;
            else
                guiTxtCurvePower.Enabled = false;
        }

        private void cbDevicePower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDevicePower.Checked)
                guiTxtDevicePower.Enabled = true;
            else
                guiTxtDevicePower.Enabled = false;
        }

        private void cbDeratingFactorPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDeratingFactorPower.Checked)
                guiTxtDeratingFactorPower.Enabled = true;
            else
                guiTxtDeratingFactorPower.Enabled = false;
        }

        private void cbRCDRatingPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRCDRatingPower.Checked)
                guiTxtRCDratingPower.Enabled = true;
            else
                guiTxtRCDratingPower.Enabled = false;
        }

        private void cbDiversityPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDiversityPower.Checked)
                guiTxtDiversityPower.Enabled = true;
            else
                guiTxtDiversityPower.Enabled = false;
        }

        private void cbCircuitRevisionPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCircuitRevisionPower.Checked)
                guiTxtRevisionPower.Enabled = true;
            else
                guiTxtRevisionPower.Enabled = false;
        }

        private void cbInstallMethodPower_CheckedChanged(object sender, EventArgs e)
        {
            if (cbInstallMethodPower.Checked)
                guiInstallPower.Enabled = true;
            else
                guiInstallPower.Enabled = false;
        }



        private void cbRatingOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRatingOther.Checked)
                guiTxtRatingOther.Enabled = true;
            else
                guiTxtRatingOther.Enabled = false;
        }

        private void cbCurveOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCurveOther.Checked)
                guiTxtCurveOther.Enabled = true;
            else
                guiTxtCurveOther.Enabled = false;
        }

        private void cbDeviceOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDeviceOther.Checked)
                guiTxtDeviceOther.Enabled = true;
            else
                guiTxtDeviceOther.Enabled = false;
        }

        private void cbDeratingFactorOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDeratingFactorOther.Checked)
                guiTxtDeratingFactorOther.Enabled = true;
            else
                guiTxtDeratingFactorOther.Enabled = false;
        }

        private void cbRCDRatingOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbRCDRatingOther.Checked)
                guiTxtRCDratingOther.Enabled = true;
            else
                guiTxtRCDratingOther.Enabled = false;
        }

        private void cbDiversityOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbDiversityOther.Checked)
                guiTxtDiversityOther.Enabled = true;
            else
                guiTxtDiversityOther.Enabled = false;
        }

        private void cbCircuitRevisionOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbCircuitRevisionOther.Checked)
                guiTxtRevisionOther.Enabled = true;
            else
                guiTxtRevisionOther.Enabled = false;
        }

        private void cbInstallMethodOther_CheckedChanged(object sender, EventArgs e)
        {
            if (cbInstallMethodOther.Checked)
                guiInstallOther.Enabled = true;
            else
                guiInstallOther.Enabled = false;
        }


        #endregion

        private void guiSysVD7pc_CheckedChanged(object sender, EventArgs e)
        {
            btnProjectParametersSave.Enabled = true;
            btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
        }

        private void guiDiscrim1pt5Times_CheckedChanged(object sender, EventArgs e)
        {
            btnProjectParametersSave.Enabled = true;
            btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
        }

        private void guiAUScableSel_CheckedChanged(object sender, EventArgs e)
        {
            btnProjectParametersSave.Enabled = true;
            btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
        }

        private void rqCPDrange_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnProjectParametersSave.Enabled = true;
            btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
        }

        private void rbOverwriteExistingLighting_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void guiTxtRatingLighting_SelectedValueChanged(object sender, EventArgs e)
        {
            if (double.Parse(guiTxtRatingLighting.SelectedItem.ToString()) > 63)
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Info", "Warning: MCB larger than 1 pole width, please be aware that DB schedule must show an adjacent space to accommodate this breaker.");
        }

        private void dgvDBSel_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            btnEnhancedCircuitEdit.PerformClick();
        }

        private void dgvDBSel_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            var senderGrid = (DataGridView)sender;
            if(senderGrid.Rows == null || e.RowIndex > senderGrid.Rows.Count)
                return; // Prevents index out of range error if the row count is zero or the clicked row index is greater than the number of rows.

            var row = senderGrid.Rows[e.RowIndex];
            //var dbName = dgvDBSel.SelectedRows[0].Cells[0].Value.ToString();

            if (e.ColumnIndex == dBNotesDataGridViewTextBoxColumn.Index && row.Cells[dBNotesDataGridViewTextBoxColumn.Index] is DataGridViewButtonCell)
            {
                if (!dgvDBSel.SelectedRows[0].Cells[0].Value.ToString().Contains(_lockedDB))
                {
                    var dbData = AllDBs.Find(x => x.Schedule_DB_Name == row.Cells[1].Value.ToString());
                    var sb = new StringBuilder();
                    dbData.CCTs.ForEach(x => sb.AppendLine($"{x.CCT_Number} - {x.Schedule_Description}"));
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Warnings", dbData?.GUI_Notes_Message + "\n\nUnable to find length to first element - Length to final used (Check path intersects with elements):\n" + sb.ToString());
                }
            }

            if (e.ColumnIndex == DB_UserNotes.Index && row.Cells[DB_UserNotes.Index] is DataGridViewButtonCell)
            {
                if (!dgvDBSel.SelectedRows[0].Cells[0].Value.ToString().Contains(_lockedDB))
                {
                    SelectedDBToSaveUserNote = AllDBs.Find(x => x.Schedule_DB_Name == row.Cells[1].Value.ToString());
                    using (var notesForm = new frmUserNotes(SelectedDBToSaveUserNote))
                    {
                        notesForm.ShowDialog();

                        if (notesForm.NotesEmpty)
                        {
                            row.Cells[DB_UserNotes.Index].Value = "Edit notes";
                        }
                        else
                        {
                            row.Cells[DB_UserNotes.Index].Value = "Add notes";
                        }

                        if (notesForm.IsSave)
                        {
                            MakeRequest(RequestId.SaveUserNotes);
                        }
                    }
                }
            }

            // When clicking Set button in DB Settings column
            if (e.ColumnIndex == DB_Settings.Index && row.Cells[DB_Settings.Index] is DataGridViewButtonCell)
            {
                if (!dgvDBSel.SelectedRows[1].Cells[1].Value.ToString().Contains(_lockedDB))
                {
                    var selectedDB = AllDBs.Find(x => x.Schedule_DB_Name == row.Cells[1].Value.ToString());

                    frmPowerBIM_DBSettings advadcedSettingsForm = new frmPowerBIM_DBSettings(_externalEvent, _handler, selectedDB);
                    ModelessPowerBIM_DBSettingsFormHandler.DBData = selectedDB;
                    FormCollection fc = Application.OpenForms;
                    foreach (System.Windows.Forms.Form frm in fc)
                    {
                        if (frm.Name == advadcedSettingsForm.Name)
                            return;
                    }
                    advadcedSettingsForm.Show(this);

                    btnProjectParametersSave.Enabled = true;
                    btnProjectParametersSave.BackColor = System.Drawing.Color.FromArgb(255, 206, 0);
                }
                else
                {
                    ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"DB is locked by {_projectInfoLockedOwner}.\n\n" +
                        $"This user need to synchronise, and you will need to reload latest before you can edit");
                }
            }

            //bool isWorkshared = !dgvDBSel.SelectedRows[1].Cells[1].Value.ToString().Contains(_lockedDB);
            // When clicking manual lock cell
            //var selectedDb = AllDBs.Find(x => x.Schedule_DB_Name == dbName);
            //if (DBIsLocked(selectedDb))
            //{

            //}

            if (e.ColumnIndex == ManualLock.Index && row.Cells[ManualLock.Index] is DataGridViewImageCell)
            {
                var dbName = row.Cells[1].Value.ToString();
                
                var selectedDb = AllDBs.Find(x => x.Schedule_DB_Name == dbName);

                //if (dbName.Contains(_lockedDB)) // If workshare locked.
                //{

                //}

                //var selectedDb = AllDBs.Find(x => x.Schedule_DB_Name == dbName);
                if (DBIsLocked(selectedDb))
                {
                    ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"DB is locked by {_projectInfoLockedOwner}.\n\n" +
                        $"This user need to synchronise, and you will need to reload latest before you can edit");
                }
                else
                {
                    // Get the current tag to determine if the image is locked or unlocked
                    var imageTag = row.Cells[ManualLock.Index].Tag?.ToString();

                    // Check if the image is currently locked
                    bool isCurrentlyLocked = imageTag == "Locked";

                    // Toggle between locked and unlocked images
                    if (isCurrentlyLocked)
                    {
                        row.Cells[ManualLock.Index].Value = Properties.Resources.Unlocked;
                        row.Cells[ManualLock.Index].Tag = "Unlocked"; // Update tag to "Unlocked"
                    }
                    else
                    {
                        row.Cells[ManualLock.Index].Value = Properties.Resources.Locked;
                        row.Cells[ManualLock.Index].Tag = "Locked"; // Update tag to "Locked"
                    }

                    // Update the DBIsManuallyLock flag based on the new state
                    DBIsManuallyLock = !isCurrentlyLocked;
                    selectedDb.IsManuallyLocked = DBIsManuallyLock; // Manually set DB_Data Manual Lock state.
                    ModelessPowerBIM_StartFormHandler.ProjInfo = projInfo;
                    //TODO::::
                    SelectedDBToManuallyLock = selectedDb;
                    MakeRequest(RequestId.SetManualLock);
                }
            }
        }

        private void rqCPDrange_Click(object sender, EventArgs e)
        {
            if (_projectInfoIsLocked)
            {

                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"Project Information is locked by {_projectInfoLockedOwner}.\n\n" +
                        $"This user need to synchronise, and you will need to reload latest before you can edit");
                rqCPDrange.SelectedIndex = 1;
            }

        }

        private void guiSysVD5pc_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiSysVD5pc);
        }

        private void guiSysVD7pc_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiSysVD7pc);
        }

        private void guiDiscrim2Times_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiDiscrim2Times);
        }

        private void guiDiscrim1pt5Times_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiDiscrim1pt5Times);
        }

        private void guiNZcableSel_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiNZcableSel);
        }

        private void guiAUScableSel_Click(object sender, EventArgs e)
        {
            RBPopUpMessageWhenLocked(guiAUScableSel);
        }

        private void RBPopUpMessageWhenLocked(RadioButton rb)
        {
            if (_projectInfoIsLocked)
            {
                var currentChecked = rb.Checked;
                UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Element is locked!", $"Project Information is locked by {_projectInfoLockedOwner}.\n\n" +
                        $"This user need to synchronise, and you will need to reload latest before you can edit");
                rb.Checked = !currentChecked;
            }

        }

        internal void RequestExportCircuitPathImages()
        {
            MakeRequest(RequestId.ExportCircuitPathImages);
        }


        internal void ExportCircuitPathImages(UIDocument uiDoc, BecaActivityLoggerData logger)
        {
            string RvtTitle = uiDoc.Document.Title;
            RvtTitle = RvtTitle.Substring(0, RvtTitle.Length - 4);

            var dbRootFolderImages = Path.Combine(
                 Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "Beca MEP Tools", "PowerBIM Reports", RvtTitle,
                 "Circuits paths images");
            if (Directory.Exists(dbRootFolderImages))
            {
                Directory.Delete(dbRootFolderImages, true);
            }
            var currentActiveDoc = uiDoc.ActiveView;
            var doc = SelectedDBs.First().DB_Element.Document;
            using (TransactionGroup txGrp = new TransactionGroup(doc, "Exporting circuits images"))
            {
                txGrp.Start();

                #region Create Line Style

                GraphicsStyle circuitPathLineStyle = null;
                using (Transaction tx = new Transaction(doc, "Create line style"))
                {
                    tx.Start();
                    circuitPathLineStyle = LineStyleUtility.GetOrCreateLinestyle(doc, "Circuit path Model Lines", 8,
                    new Autodesk.Revit.DB.Color(System.Drawing.Color.Red.R, System.Drawing.Color.Red.G, System.Drawing.Color.Red.B)).GetGraphicsStyle(GraphicsStyleType.Projection);
                    tx.Commit();
                }

                #endregion

                BecaProgressForm2 pb = new BecaProgressForm2("Exporting circuits images");
                pb.TopLevel = true;
                pb.TopMost = true;
                pb.InitializePB1(SelectedDBs.Count);

                foreach (var db in SelectedDBs)
                {
                    pb.IncrementPB1("Processing DB {0} of " + SelectedDBs.Count + ".");
                    pb.InitializePB2(db.CCTs.Count);

                    var dbFolderImages = Path.Combine(dbRootFolderImages,
                                                db.DB_Element.Name);
                    Directory.CreateDirectory(dbFolderImages);
                    for (int i = 0; i < db.CCTs.Count; i++)
                    {
                        var CCT = db.CCTs[i];

                        pb.IncrementPB2("Processing Circuit " + (i + 1).ToString() + " of " + db.CCTs.Count + ".");

                        List<Element> modelLines = new List<Element>();

                        var circuitView = CreateViewForCircuite(CCT);
                        if (circuitView != null)
                        {
                            uiDoc.ActiveView = circuitView;


                            using (Transaction tx = new Transaction(doc, "Isolate Elements"))
                            {
                                tx.Start();
                                var visibaleElementsIds = CCT.CCT_Electrical_System.Elements.Cast<Element>().Select(e => e.Id).ToList();
                                circuitView.UnhideElements(visibaleElementsIds);
                                var viewElements = new FilteredElementCollector(doc, circuitView.Id).Where(e => e.CanBeHidden(circuitView) && !visibaleElementsIds.Contains(e.Id)).Select(e => e.Id).ToList();

                                if (viewElements.Count > 0)
                                {
                                    circuitView.HideElements(viewElements);//hide all elements

                                }

                                tx.Commit();
                            }


                            using (Transaction tx = new Transaction(doc, "Create Model Line circuit path"))
                            {
                                tx.Start();

                                FailureHandlingOptions failureHandlingOptions = tx.GetFailureHandlingOptions();
                                failureHandlingOptions.SetFailuresPreprocessor(new BecaRevitUtilities.FailureHandlers.IgnoreWarningsFailureHandler());
                                tx.SetFailureHandlingOptions(failureHandlingOptions);

                                var circuitPath = CCT.CCT_Electrical_System.GetCircuitPath();
                                for (int j = 0; j < circuitPath.Count - 1; j++)
                                {
                                    var modelLineCircuit = ModelLineCreator.CreateModelLine(doc, circuitPath[j], circuitPath[j + 1]);
                                    modelLineCircuit.LineStyle = circuitPathLineStyle;
                                    modelLines.Add(modelLineCircuit);
                                }
                                tx.Commit();
                            }


                            var activeUiView = uiDoc.GetOpenUIViews().First(ouiuView => ouiuView.ViewId.IntegerValue == circuitView.Id.IntegerValue);
                            activeUiView.ZoomToFit();


                            using (Transaction tx = new Transaction(doc, "Export single image"))
                            {
                                tx.Start();
                                ViewUtility.ExportToImage(circuitView, Path.Combine(dbFolderImages, CCT.CCT_Number + ".png"));
                                tx.Commit();
                            }

                            using (Transaction tx = new Transaction(doc, "delete model lines"))
                            {
                                tx.Start();
                                doc.Delete(modelLines.Select(ml => ml.Id).ToArray());
                                tx.Commit();
                            }


                        }
                        else
                        {
                            logger.Log("Couldn't create view for circuit and will not be included in exported file."
                                + "Database Name & Circuit Number: " + db.DB_Element.Name + " & " + CCT.CCT_Number, LogType.Error);
                        }


                    }
                }



                txGrp.Commit();
                pb.Close();
            }
            uiDoc.ActiveView = currentActiveDoc;


        }

        private Autodesk.Revit.DB.View CreateViewForCircuite(PowerBIM_CircuitData CCT)
        {
            PowerBIM_3DViewLogic ThreeDLogic = new PowerBIM_3DViewLogic(projInfo); //initialise logic for creating 3D view

            double buffer = RevitUnitConvertor.MmToInternal(4000);

            IList<XYZ> ListOfPathNodes = CCT.CCT_Electrical_System.GetCircuitPath();   //list of revit path node coordinates

            Autodesk.Revit.DB.ElementSet CircuitElems = CCT.CCT_Electrical_System.Elements;
            List<Autodesk.Revit.DB.Element> elements = (from Autodesk.Revit.DB.Element e in CircuitElems
                                                        select e).ToList(); //convert revit element set into list of elements
            BoundingBoxCalculator bound = BoundingBoxCalculator.Create(projInfo.Document, ListOfPathNodes, projInfo.TaskLogger);

            if (bound != null)
            {
                //find template by name using LINQ
                View viewTemplate = (from v in new FilteredElementCollector(projInfo.Document).OfClass(typeof(View)).Cast<View>()
                                     where v.IsTemplate == true && v.Name.Equals("WR_77_3D_POWERBIM")
                                     select v).SingleOrDefault();

                //if view exists edit it to suit circuit. if it does not create a new one from scratch
                if (ThreeDLogic._ViewExists)
                {
                    PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._PB3DView as View3D, buffer, bound, false, projInfo.TaskLogger);//this modifies an already existing view 
                    return ThreeDLogic._PB3DView;
                }
                else if (viewTemplate == null)//if view template does not exist view cant be created
                {
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "PowerBIM view could not be created because PowerBIM view template does not exist \n \n Please create a 3D view named WR_77_3D_POWERBIM and try again");
                    return null;
                }
                else
                {
                    var cView = PowerBIM_3DViewLogic.RunLogic(projInfo.UIDocument, ThreeDLogic._UserViewName, viewTemplate as View, buffer, bound, false, projInfo.TaskLogger); //this creates a new view 
                    return cView;
                }
            }
            else
            {
                //MessageBox.Show("Failed to create 3D View: bounding box is null.");
                return null;
            }

        }

        private void frmPowerBIM_Start_FormClosed(object sender, FormClosedEventArgs e)
        {
            _handler.Logger.PostTaskEnd("PowerBim: Form was closed. check detailed logs.");
        }

        public void ShowOrHideDB_SettingsColumn()
        {
            if (PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally)
            {
                dgvDBSel.Columns[DB_Settings.Index].Visible = false;
            }
            else
            {
                dgvDBSel.Columns[DB_Settings.Index].Visible = true;
            }
        }

        private void cbCircuitRevisionPower_CheckedChanged_1(object sender, EventArgs e)
        {
            if (cbCircuitRevisionPower.Checked)
                guiTxtRevisionPower.Enabled = true;
            else
                guiTxtRevisionPower.Enabled = false;
        }
    }


    #region Modeless Form Handler 

    public class ModelessPowerBIM_StartFormHandler
    {
        #region Fields

        static frmPowerBIM_Start _startForm;
        public static PowerBIM_ProjectInfo ProjInfo;
        public static PowerBIM_CircuitData CCT;
        public static List<PowerBIM_CircuitData> CCTs;
        #endregion

        #region Methods
        public static void RequestExportCircuitPathImages()
        {
            _startForm.RequestExportCircuitPathImages();
        }

        internal static void ExportCircuitPathImages(UIDocument uiDoc, BecaActivityLoggerData logger)
        {
            _startForm.ExportCircuitPathImages(uiDoc, logger);
        }

        internal static void SetManualLock(UIDocument uiDoc, BecaActivityLoggerData logger)
        {
            using (var trans = new Transaction(ProjInfo.Document, "Set DB Manual Lock"))
            {
                trans.Start();
                var lockParameter = _startForm.SelectedDBToManuallyLock.DB_Element.LookupParameter("Beca_PB_IsLocked");

                if (lockParameter != null)
                {
                    if (_startForm.DBIsManuallyLock)
                    {
                        lockParameter.Set(1); // Set to True (1) if DB is manually locked
                    }
                    else
                    {
                        lockParameter.Set(0); // Set to False (0) if DB is not manually locked
                    }
                }
                trans.Commit();
            }
        }

        internal static void SaveUserNotes(UIDocument uiDoc, BecaActivityLoggerData logger)
        {
            var db = _startForm.SelectedDBToSaveUserNote;
            try
            {
                var id = db.DB_Element.Id;
                using (var trans = new Transaction(uiDoc.Document, $"Save {db.DB_Element.Name} notes"))
                {
                    trans.Start();
                    db.DB_Element.LookupParameter("Beca Inst Use")?.Set(db.User_Notes);
                    trans.Commit();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving notes: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            
        }

        public static void SaveSettings()
        {
            _startForm.saveSettings();
        }

        public static void UpdaterRequired_All()
        {
            _startForm.UpdaterRequired_All();
        }

        public static void CommitProjectInfo()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans_CCT.Start();
                ProjInfo.CommitProjectInfo();
                trans_CCT.Commit();
            }

        }

        public static void RegenerateCircuitProperties()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.RegenerateCircuitProperties();
                trans_CCT.Commit();
            }
        }

        public static void RunAutoSizer()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_RunAutoSizer.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.RunAutoSizer();
                trans_CCT.Commit();
            }
        }

        public static void WriteLightingToSchedule()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_WriteLightingToSchedule.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.WriteLightingToSchedule();
                trans_CCT.Commit();
            }
        }

        public static void WritePowerToSchedule()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_WritePowerToSchedule.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.WritePowerToSchedule();
                trans_CCT.Commit();
            }
        }

        public static void WriteOtherToSchedule()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_WriteOtherToSchedule.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.WriteOtherToSchedule();
                trans_CCT.Commit();
            }
        }

        public static void RunCircuitCheckManual()
        {
            using (var trans_CCT = new Transaction(ProjInfo.Document, BecaTransactionsNames.PowerBIM_UpdateDBData.GetHumanReadableString()))
            {
                trans_CCT.Start();
                _startForm.RunCircuitCheckManual();
                trans_CCT.Commit();
            }
        }

        /// <summary>
        ///   This method creates and shows a modeless dialog, unless it already exists.
        /// </summary>
        /// <remarks>
        ///   The external command invokes this on the end-user's request
        /// </remarks>
        /// 


        public static void ShowForm(List<PowerBIM_DBData> AllProjectDBs, PowerBIM_ProjectInfo pi, BecaActivityLoggerData logger)
        {
            // If we do not have a dialog yet, create and show it
            if (_startForm == null || _startForm.IsDisposed)
            {

                // A new handler to handle request posting by the dialog
                RequestHandler handler = new RequestHandler(pi.UIDocument.Application, logger);

                // External Event for the dialog to use (to post requests)
                ExternalEvent exEvent = ExternalEvent.Create(handler);

                // We give the objects to the new dialog;
                // The dialog becomes the owner responsible fore disposing them, eventually.
                _startForm = new frmPowerBIM_Start(logger, exEvent, handler, AllProjectDBs, pi);
                _startForm.Show();
            }
            else
                _startForm.Activate();
            //m_MyForm.BringToFront();
        }

        public static void ShowMsgToTheUser(string title, string message)
        {
            BecaBaseMessageForm frm = new BecaBaseMessageForm(message, title);
            if (_startForm != null && _startForm.IsDisposed)
            {
                frm.Show();
            }
            else
            {
                frm.Show(_startForm);
            }
            frm.BringToFront();
        }
        // PowerBIM needs a view template to create a 3D view. 
        //
        // Please create a new 3D view Template names "WR_77_3D_POWERBIM" and re-launch PowerBIM"
        public static DialogResult ShowDialogMsgToTheUser(string title, string msg)
        {
            BecaBaseMessageForm frm = new BecaBaseMessageForm(msg, title);

            if (_startForm == null || _startForm.IsDisposed)
            {
                return frm.ShowDialog(_startForm);
            }
            else
            {
                return frm.ShowDialog(_startForm);
            }
        }



        /// <summary>
        ///   Waking up the dialog from its waiting state.
        /// </summary>
        /// 
        public static void WakeFormUp()
        {
            if (_startForm != null)
            {
                _startForm.WakeUp();
                _startForm.Activate();
            }
        }

        public static void BringFormsTofront()
        {
            if (_startForm != null)
            {
                _startForm.TopLevel = true;
                _startForm.TopMost = true;

            }
        }
        public static void BringFormsToBack()
        {
            if (_startForm != null)
            {
                _startForm.TopLevel = false;
            }
        }

        /// <summary>
        /// Must be called in OnShutdown(UIControlledApplication a) Event of the App command.
        /// </summary>
        public static void OnRevitShutDown()
        {
            if (_startForm != null)
            {
                _startForm.Close();
            }
        }



        #endregion

    }

    #endregion

}
