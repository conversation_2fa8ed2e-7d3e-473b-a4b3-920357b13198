using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Handlers;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Main ViewModel for PowerBIM 6 WPF application
    /// Manages the main window UI state and coordinates with other ViewModels
    /// </summary>
    public partial class MainViewModel : BaseViewModel
    {
        #region Fields

        private readonly ILogger<MainViewModel> _logger;
        private List<PowerBIM_DBData> _originalDatabases;
        private PowerBIM_ProjectInfo _originalProjectInfo;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Collection of distribution board models for UI binding
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<DistributionBoardItemViewModel> _distributionBoards;

        /// <summary>
        /// Currently selected distribution board
        /// </summary>
        [ObservableProperty]
        private DistributionBoardItemViewModel _selectedDistributionBoard;

        /// <summary>
        /// Project information model
        /// </summary>
        [ObservableProperty]
        private ProjectInfoModel _projectInfo;

        /// <summary>
        /// Indicates if calculations can be run
        /// </summary>
        [ObservableProperty]
        private bool _canRunCalculations;

        /// <summary>
        /// Indicates if a distribution board is selected
        /// </summary>
        [ObservableProperty]
        private bool _hasSelectedDistributionBoard;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the MainViewModel
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        public MainViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger = GetService<ILogger<MainViewModel>>();
            
            // Initialize collections
            DistributionBoards = new ObservableCollection<DistributionBoardItemViewModel>();

            // Initialize state
            CanRunCalculations = false;
            HasSelectedDistributionBoard = false;
            
            _logger?.LogInformation("MainViewModel initialized");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the ViewModel with data from PowerBIM core logic
        /// </summary>
        /// <param name="distributionBoards">Original distribution board data</param>
        /// <param name="projectInfo">Original project information</param>
        /// <param name="requestHandler">Request handler for ExternalEvent</param>
        /// <param name="externalEvent">ExternalEvent for Revit API safety</param>
        public void InitializeWithData(List<PowerBIM_DBData> distributionBoards, PowerBIM_ProjectInfo projectInfo,
            RequestHandler_PB6 requestHandler, ExternalEvent externalEvent)
        {
            try
            {
                // Call base initialization
                Initialize(requestHandler, externalEvent);
                
                // Store original references
                _originalDatabases = distributionBoards ?? throw new ArgumentNullException(nameof(distributionBoards));
                _originalProjectInfo = projectInfo ?? throw new ArgumentNullException(nameof(projectInfo));

                // Convert and load data
                LoadDistributionBoardData();
                LoadProjectData();

                // Update UI state
                UpdateUIState();

                StatusMessage = "PowerBIM 6 loaded successfully";
                _logger?.LogInformation($"MainViewModel initialized with {distributionBoards.Count} distribution boards");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to initialize MainViewModel with data");
                StatusMessage = $"Error loading data: {ex.Message}";
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to run calculations on all distribution boards
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRunCalculationsExecute))]
        private void RunCalculations()
        {
            try
            {
                _logger?.LogInformation("Running calculations for all distribution boards");
                MakeRequest(RequestId_PB6.UpdateCircuits);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to run calculations");
                StatusMessage = $"Error running calculations: {ex.Message}";
            }
        }

        private bool CanRunCalculationsExecute() => CanRunCalculations && !IsBusy;

        /// <summary>
        /// Command to open circuit edit window
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanOpenCircuitEdit))]
        private void OpenCircuitEdit()
        {
            try
            {
                if (SelectedDistributionBoard != null)
                {
                    _logger?.LogInformation($"Opening circuit edit for distribution board: {SelectedDistributionBoard.Name}");
                    // TODO: Pass selected distribution board to circuit edit window
                    ModelessMainWindowHandler.ShowCircuitEditWindow(SelectedDistributionBoard.OriginalData);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to open circuit edit window");
                StatusMessage = $"Error opening circuit editor: {ex.Message}";
            }
        }

        private bool CanOpenCircuitEdit() => HasSelectedDistributionBoard && !IsBusy;

        /// <summary>
        /// Command to open distribution board edit window
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanOpenDbEdit))]
        private void OpenDbEdit()
        {
            try
            {
                if (SelectedDistributionBoard != null)
                {
                    _logger?.LogInformation($"Opening distribution board edit for: {SelectedDistributionBoard.Name}");
                    ModelessMainWindowHandler.ShowDbEditWindow(SelectedDistributionBoard.OriginalData);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to open distribution board edit window");
                StatusMessage = $"Error opening distribution board editor: {ex.Message}";
            }
        }

        private bool CanOpenDbEdit() => HasSelectedDistributionBoard && !IsBusy;

        /// <summary>
        /// Command to open advanced settings window
        /// </summary>
        [RelayCommand]
        private void OpenAdvancedSettings()
        {
            try
            {
                _logger?.LogInformation("Opening advanced settings");
                ModelessMainWindowHandler.ShowAdvancedSettingsWindow();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to open advanced settings");
                StatusMessage = $"Error opening settings: {ex.Message}";
            }
        }

        /// <summary>
        /// Command to export data
        /// </summary>
        [RelayCommand]
        private void ExportData()
        {
            try
            {
                _logger?.LogInformation("Opening export window");
                ModelessMainWindowHandler.ShowExportWindow();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to open export window");
                StatusMessage = $"Error opening export: {ex.Message}";
            }
        }

        /// <summary>
        /// Command to save project
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanSaveProject))]
        private void SaveProject()
        {
            try
            {
                _logger?.LogInformation("Saving project");
                MakeRequest(RequestId_PB6.SaveProject);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to save project");
                StatusMessage = $"Error saving project: {ex.Message}";
            }
        }

        private bool CanSaveProject() => HasUnsavedChanges && !IsBusy;

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle selected distribution board change
        /// </summary>
        /// <param name="value">New selected distribution board</param>
        partial void OnSelectedDistributionBoardChanged(DistributionBoardItemViewModel value)
        {
            HasSelectedDistributionBoard = value != null;

            // Notify commands that depend on selection
            OpenCircuitEditCommand.NotifyCanExecuteChanged();
            OpenDbEditCommand.NotifyCanExecuteChanged();

            _logger?.LogDebug($"Selected distribution board changed to: {value?.Name ?? "None"}");
        }

        /// <summary>
        /// Handle distribution boards collection change
        /// </summary>
        /// <param name="value">New distribution boards collection</param>
        partial void OnDistributionBoardsChanged(ObservableCollection<DistributionBoardItemViewModel> value)
        {
            UpdateUIState();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Load distribution board data from original PowerBIM data
        /// </summary>
        private void LoadDistributionBoardData()
        {
            try
            {
                DistributionBoards.Clear();

                if (_originalDatabases != null)
                {
                    foreach (var dbData in _originalDatabases)
                    {
                        var dbViewModel = new DistributionBoardItemViewModel(dbData);
                        DistributionBoards.Add(dbViewModel);
                    }
                }

                _logger?.LogDebug($"Loaded {DistributionBoards.Count} distribution boards");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load distribution board data");
                throw;
            }
        }

        /// <summary>
        /// Load project data from original PowerBIM data
        /// </summary>
        private void LoadProjectData()
        {
            try
            {
                if (_originalProjectInfo != null)
                {
                    var dataService = GetService<IDataService>();
                    ProjectInfo = dataService.ConvertToModel(_originalProjectInfo);
                }
                
                _logger?.LogDebug("Project data loaded");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load project data");
                throw;
            }
        }

        /// <summary>
        /// Update UI state based on current data
        /// </summary>
        private void UpdateUIState()
        {
            try
            {
                CanRunCalculations = DistributionBoards?.Any(db => !db.IsLocked) == true;

                // Update command states
                RunCalculationsCommand.NotifyCanExecuteChanged();
                SaveProjectCommand.NotifyCanExecuteChanged();

                _logger?.LogDebug("UI state updated");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to update UI state");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refresh distribution board data from original sources
        /// Called after circuit editing to update the main window
        /// </summary>
        public void RefreshDistributionBoards()
        {
            try
            {
                LoadDistributionBoardData();
                UpdateUIState();
                _logger?.LogInformation("Distribution boards refreshed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to refresh distribution boards");
                StatusMessage = $"Error refreshing data: {ex.Message}";
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// Override WakeUp to handle specific main window wake up logic
        /// </summary>
        public override void WakeUp()
        {
            base.WakeUp();
            
            try
            {
                // Refresh data after operations
                LoadDistributionBoardData();
                UpdateUIState();
                
                StatusMessage = "Operation completed";
                _logger?.LogDebug("MainViewModel woken up and refreshed");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to wake up MainViewModel");
                StatusMessage = $"Error refreshing data: {ex.Message}";
            }
        }

        #endregion
    }
}
