﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using Common.Utilities;
using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using TaskDialogIcon = Autodesk.Revit.UI.TaskDialogIcon;
using Microsoft.Office.Interop.Excel;
using Range = Microsoft.Office.Interop.Excel.Range;
using Parameter = Autodesk.Revit.DB.Parameter;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_5.UI.Forms
{
    public partial class frmPowerBIM_AdvancedSettings : BecaBaseForm
    {
        // Modeless
        RequestHandler _handler;
        ExternalEvent _externalEvent;

        // Create local reference to project info class
        public PowerBIM_ProjectInfo ProjInfo { set; get; }

        //
        private OpenFileDialog rqCabDataFld = new OpenFileDialog();

        private Parameter _paramPBGPOCalc;

        //Set defualt values
        public bool rqChkGPOCalc80perc = false;
        public bool rqChkGPOActualLoad = true;
        public bool rqChkGPO1000plus100 = false;
        public bool rqChkClearingTimePower04 = true;
        public bool rqChkClearingTimePower5 = false;
        public bool rqChkClearingTimeLighting04 = true;
        public bool rqChkClearingTimeLighting5 = false;

        public bool blFouldata = false;

        #region Constructors

        public frmPowerBIM_AdvancedSettings(ExternalEvent exEvent, RequestHandler handler, PowerBIM_ProjectInfo pi)
        {
            ProjInfo = pi;

            InitializeComponent();

            // For Modeless
            _handler = handler;
            _externalEvent = exEvent;

            //Inital Cable Data Path
            this.rqCableDataFile.Text = ProjInfo.Database_Path + @"\" + PowerBIM_Constants.CableDatabaseName; //TODO

            WriteSafetyFactorsToTextBox();

            if ((int)ProjInfo.PowerVDCalculation == 1)
                rb_PowerLinearDeprecating.Checked = true;
            else if ((int)ProjInfo.PowerVDCalculation == 2)
                rb_PowerLumpLoad.Checked = true;
            else
            {
                rb_PowerLumpLoad.Checked = false;
                rb_PowerLinearDeprecating.Checked = false;
            }

            if ((int)ProjInfo.LightingVDCalculation == 1)
                rb_LightingLinearDeprecating.Checked = true;
            else if ((int)ProjInfo.LightingVDCalculation == 2)
                rb_LightingLumpLoad.Checked = true;
            else
            {
                rb_LightingLinearDeprecating.Checked = false;
                rb_LightingLumpLoad.Checked = false;
            }

            if ((int)ProjInfo.OtherVDCalculation == 1)
                rb_OtherLinearDeprecating.Checked = true;
            else if ((int)ProjInfo.OtherVDCalculation == 2)
                rb_OtherLumpLoad.Checked = true;
            else
            {
                rb_OtherLinearDeprecating.Checked = false;
                rb_OtherLumpLoad.Checked = false;
            }



            //Set GPO setting from revit
            _paramPBGPOCalc = pi.Document.ProjectInformation.get_Parameter(PowerBIM_Constants.paramGuidPBGPOCalc);
            if (_paramPBGPOCalc.AsInteger() >= 1 && _paramPBGPOCalc.AsInteger() <= 100)
            {
                tb_VoltDropBreakerRatingPercent.Text = _paramPBGPOCalc.AsInteger().ToString();
                rqChkGPOCalc80perc = true;
                rqChkGPOActualLoad = false;
                rqChkGPO1000plus100 = false;
            }
            else if (ProjInfo.GPO_Calc_Integer == -2)
            {
                rqChkGPOCalc80perc = false;
                rqChkGPOActualLoad = true;
                rqChkGPO1000plus100 = false;
            }
            else if (ProjInfo.GPO_Calc_Integer == -1)
            {
                rqChkGPOCalc80perc = false;
                rqChkGPOActualLoad = false;
                rqChkGPO1000plus100 = true;
            }

            //Set clearing time settings from revit
            if (ProjInfo.Clearing_Time_Power == 400)
                rqChkClearingTimePower04 = true;
            else if (ProjInfo.Clearing_Time_Power == 5000)
                rqChkClearingTimePower5 = true;

            //Set clearing time settings from revit
            if (ProjInfo.Clearing_Time_Lighting == 400)
                rqChkClearingTimeLighting04 = true;
            else if (ProjInfo.Clearing_Time_Lighting == 5000)
                rqChkClearingTimeLighting5 = true;

            radioButtonGPOCalcBreakerRating.Checked = rqChkGPOCalc80perc;
            radioButtonGPOCalcAssignedLoad.Checked = rqChkGPOActualLoad;
            radioButtonGPOCalc1000plus100.Checked = rqChkGPO1000plus100;

            radioButtonNonGPO04.Checked = rqChkClearingTimePower04;
            radioButtonNonGPO5.Checked = rqChkClearingTimePower5;

            radioButtonLightingClearning04.Checked = rqChkClearingTimeLighting04;
            radioButtonLightingClearning5.Checked = rqChkClearingTimeLighting5;

            // Get value for Revit Path Tolerance setting
            if (ProjInfo.Length_ExtraPerElement_Lighting == 0)
            {
                this.tbTxtRevitPathNodeTollerance.Text = "00";
            }
            else
            {
                this.tbTxtRevitPathNodeTollerance.Text = (ProjInfo.NodeCircuitPathTolerance / 1000).ToString();
            }

            if (PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally)
            {
                chb_ControlGlobally.Checked = true;
            }
            else
            {
                chb_ControlGlobally.Checked = false;
                gb_MainSettings.Enabled = false;
            }
        }


        #endregion

        #region Methods

        #region Modeless Functionality

        /// <summary>
        ///   A private helper method to make a request
        ///   and put the dialog to sleep at the same time.
        /// </summary>
        /// <remarks>
        ///   It is expected that the process which executes the request 
        ///   (the Idling helper in this particular case) will also
        ///   wake the dialog up after finishing the execution.
        /// </remarks>
        ///
        private void MakeRequest(RequestId request)
        {
            _handler.Request.Make(request);
            _externalEvent.Raise();
            DozeOff();

        }

        /// <summary>
        ///   Control enabler / disabler 
        /// </summary>
        ///
        private void EnableCommands(bool status)
        {
            foreach (System.Windows.Forms.Control ctrl in this.Controls)
            {
                ctrl.Enabled = status;
            }
            if (!status)
            {
                this.rqSettingsCancel.Enabled = true;
            }
        }

        /// <summary>
        ///   DozeOff -> disable all controls (but the Exit button)
        /// </summary>
        /// 
        private void DozeOff()
        {
            EnableCommands(false);
        }

        /// <summary>
        ///   WakeUp -> enable all controls
        /// </summary>
        /// 
        public void WakeUp()
        {
            EnableCommands(true);
        }


        #endregion

        private void WriteSafetyFactorsToTextBox()
        {
            // Lighting CCTs
            if (ProjInfo.Length_ExtraPerElement_Lighting == 0)
            {
                this.tbTxtLenPerElemLighting.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemLighting.Text = (ProjInfo.Length_ExtraPerElement_Lighting / 1000).ToString();
            }

            if (ProjInfo.Length_ExtraPerCCT_Lighting == 0)
            {
                this.tbTxtLenPerCCTLighting.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTLighting.Text = (ProjInfo.Length_ExtraPerCCT_Lighting * 100).ToString();
            }

            // Power CCTs
            if (ProjInfo.Length_ExtraPerElement_Power == 0)
            {
                this.tbTxtLenPerElemPower.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemPower.Text = (ProjInfo.Length_ExtraPerElement_Power / 1000).ToString();
            }

            if (ProjInfo.Length_ExtraPerCCT_Power == 0)
            {
                this.tbTxtLenPerCCTPower.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTPower.Text = (ProjInfo.Length_ExtraPerCCT_Power * 100).ToString();
            }

            // Other CCTs
            if (ProjInfo.Length_ExtraPerElement_Other == 0)
            {
                this.tbTxtLenPerElemOther.Text = "00";
            }
            else
            {
                this.tbTxtLenPerElemOther.Text = (ProjInfo.Length_ExtraPerElement_Other / 1000).ToString();
            }

            if (ProjInfo.Length_ExtraPerCCT_Other == 0)
            {
                this.tbTxtLenPerCCTOther.Text = "00";
            }
            else
            {
                this.tbTxtLenPerCCTOther.Text = (ProjInfo.Length_ExtraPerCCT_Other * 100).ToString();
            }
        }

        private VoltDropCalculation GetVoltDropCalculation(RadioButton linearDeprecatingRB, RadioButton lumpLoadRB)
        {
            if (linearDeprecatingRB.Checked)
            {
                return VoltDropCalculation.LinearDeprecating;
            }
            else if (lumpLoadRB.Checked)
            {
                return VoltDropCalculation.LumpLoad;
            }

            return VoltDropCalculation.Unknown; // Or throw an exception if neither RadioButton is checked
        }

        #region Ui

        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            if (Environment.UserDomainName == "BECAMAIL")
            {
                using (frmPowerBIM_Help rqFrmHelp = new frmPowerBIM_Help())
                {
                    rqFrmHelp.ShowDialog();
                }
            }
            else
            {
                using (var bedarHelpFrm = new frmBedarPowerBIM_Help())
                {
                    bedarHelpFrm.ShowDialog();
                }
            }
        }



        #region Button Clicks

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            // Volt drop calculation breaker rating percent
            if (radioButtonGPOCalcBreakerRating.Checked)
            {
                double breakerRatingPercent;
                if (double.TryParse(tb_VoltDropBreakerRatingPercent.Text, out breakerRatingPercent))
                {

                    ProjInfo.GPO_Calc_Integer = Convert.ToInt32(breakerRatingPercent.ToString());
                }
                else
                {
                    MessageBox.Show("Please input a valid value in GPO percentage value.");
                    return;
                }
            }

            rqChkGPOCalc80perc = radioButtonGPOCalcBreakerRating.Checked;
            rqChkGPOActualLoad = radioButtonGPOCalcAssignedLoad.Checked;
            rqChkGPO1000plus100 = radioButtonGPOCalc1000plus100.Checked;

            rqChkClearingTimePower04 = radioButtonNonGPO04.Checked;
            rqChkClearingTimePower5 = radioButtonNonGPO5.Checked;

            rqChkClearingTimeLighting04 = radioButtonLightingClearning04.Checked;
            rqChkClearingTimeLighting5 = radioButtonLightingClearning5.Checked;

            ProjInfo.PowerVDCalculation = GetVoltDropCalculation(rb_PowerLinearDeprecating, rb_PowerLumpLoad);
            ProjInfo.OtherVDCalculation = GetVoltDropCalculation(rb_OtherLinearDeprecating, rb_OtherLumpLoad);
            ProjInfo.LightingVDCalculation = GetVoltDropCalculation(rb_LightingLinearDeprecating, rb_LightingLumpLoad);

            //Set GPO setting from revit
            if (rqChkGPOCalc80perc == true)
            {
                ProjInfo.GPO_Calc_Integer = Convert.ToInt32(tb_VoltDropBreakerRatingPercent.Text);
                ProjInfo.Selected_GPO_Setting = $"{tb_VoltDropBreakerRatingPercent.Text} %";
            }
            else if (rqChkGPOActualLoad == true)
            {
                ProjInfo.GPO_Calc_Integer = -2;
                ProjInfo.Selected_GPO_Setting = $"{radioButtonGPOCalc1000plus100.Text}";
            } 
            else if (rqChkGPO1000plus100 == true)
            {
                ProjInfo.GPO_Calc_Integer = -1;
                ProjInfo.Selected_GPO_Setting = $"{radioButtonGPOCalcAssignedLoad.Text}";
            }
            //Set clearing time settings from revit
            if (rqChkClearingTimePower04 == true)
                ProjInfo.Clearing_Time_Power = 400;
            else
                ProjInfo.Clearing_Time_Power = 5000;

            //Set clearing time settings from revit
            if (rqChkClearingTimeLighting04 == true)
                ProjInfo.Clearing_Time_Lighting = 400;
            else
                ProjInfo.Clearing_Time_Lighting = 5000;

            //Set Safety Factors
            if (cbExtraLengthElem.Checked)
            {
                ProjInfo.Length_ExtraPerElement_enabled = true;
                ProjInfo.Length_ExtraPerElement_Lighting = double.Parse(tbTxtLenPerElemLighting.Text) * 1000;
                ProjInfo.Length_ExtraPerElement_Power = double.Parse(tbTxtLenPerElemPower.Text) * 1000;
                ProjInfo.Length_ExtraPerElement_Other = double.Parse(tbTxtLenPerElemOther.Text) * 1000;
            }
            else
            {
                ProjInfo.Length_ExtraPerElement_enabled = false;
                ProjInfo.Length_ExtraPerElement_Lighting = 0;
                ProjInfo.Length_ExtraPerElement_Power = 0;
                ProjInfo.Length_ExtraPerElement_Other = 0;
            }


            if (cbExtraLenPerCCT.Checked)
            {
                ProjInfo.Length_ExtraPerCCT_enabled = true;
                ProjInfo.Length_ExtraPerCCT_Lighting = double.Parse(tbTxtLenPerCCTLighting.Text) / 100;
                ProjInfo.Length_ExtraPerCCT_Power = double.Parse(tbTxtLenPerCCTPower.Text) / 100;
                ProjInfo.Length_ExtraPerCCT_Other = double.Parse(tbTxtLenPerCCTOther.Text) / 100;
            }
            else
            {
                ProjInfo.Length_ExtraPerCCT_enabled = false;
                ProjInfo.Length_ExtraPerCCT_Lighting = 0;
                ProjInfo.Length_ExtraPerCCT_Power = 0;
                ProjInfo.Length_ExtraPerCCT_Other = 0;
            }

            // Revit Path Settings
            ProjInfo.NodeCircuitPathTolerance = double.Parse(tbTxtRevitPathNodeTollerance.Text.Substring(0, 4)) * 1000;

            ProjInfo.Parameters_Changed = true;
            //ProjInfo.Commit_AdvancedSettings();
            ModelessPowerBIM_AdvancedSettingsFormHandler.ProjInfo = ProjInfo;
            ModelessPowerBIM_StartFormHandler.ProjInfo = ProjInfo;
            MakeRequest(RequestId.Commit_AdvancedSettings);

            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #endregion

        #endregion


        private void btnEditDatabase_Click(object sender, EventArgs e)
        {
            rqCabDataFld.Filter = "XLSX|*.xlsx";
            rqCabDataFld.InitialDirectory = ProjInfo.Database_Path;

            if (rqCabDataFld.ShowDialog() == DialogResult.OK)
            {
                //Microsoft.Office.Interop.Excel.Application rqExcel = new Microsoft.Office.Interop.Excel.Application();
                //rqExcel.Visible = true;
                //Microsoft.Office.Interop.Excel.Workbook rqWorkBookPro = rqExcel.Workbooks.Open(rqCabDataFld.FileName);
                this.rqCableDataFile.Text = rqCabDataFld.FileName;
                this.rqCableDataFile.Refresh();

                // Update  if there is a difference 
                if (ProjInfo.Database_Path != System.IO.Path.GetDirectoryName(rqCabDataFld.FileName))
                {
                    ProjInfo.Database_Path = System.IO.Path.GetDirectoryName(rqCabDataFld.FileName);
                    ProjInfo.UpdateFilePaths();
                }
                else
                    UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Error", "Unable to complete action! \n\n This is the same file as before??");
            }
        }

        private void btnEnableNewData_Click(object sender, EventArgs e)
        {
            BlProcessXLStoCSV(this.rqCableDataFile.Text);
        }


        internal static bool BlProcessXLStoCSV(string strCableDataProXls)
        {
            bool blProcessXLStoCSV = false;

            TaskDialog rqTD = new TaskDialog("Beca PowerBIM");
            rqTD.Id = "ID_TD_XLStoCSV";
            rqTD.MainIcon = TaskDialogIcon.TaskDialogIconWarning;
            rqTD.AllowCancellation = true;
            rqTD.MainInstruction = "Standby! This process may take a few minutes! Have you saved your prjoect first?";
            rqTD.FooterText = "Import Cable Database from Spreadsheet";
            rqTD.CommonButtons = TaskDialogCommonButtons.Ok | TaskDialogCommonButtons.Cancel;
            rqTD.DefaultButton = TaskDialogResult.Ok;
            TaskDialogResult rqTDres = rqTD.Show();
            if (rqTDres == TaskDialogResult.Cancel)
            {
                return blProcessXLStoCSV;
            }

            // Generating a progress bar for the export so the user can see progress.
            //
            PB_ProgressBar pb = new PB_ProgressBar();
            pb.Reset();
            pb.SetScope(8, 1);
            pb.SetTask("Converting to CVS", 1);
            pb.SetCaption("Initialising ...", 1);
            pb.LevelUp();

            //**** Read All Useful Spreadsheet Tables
            pb.Step("Setting up excel instances ...");
            string strCableDataBaseNZ = "Cable Database NZ";
            string strCableDataBaseAU = "Cable Database AU";
            string strEFLIsheetPro = "Max EFLI";
            string strSCsheetPro = "SC Withstand Simplified";
            string strMCBSchneiderIC60 = "MCB - Schneider iC60";
            string strMCBTerasakiDinT = "MCB - Terasaki DinT";
            string strMCBABB = "MCB - ABB";

            Microsoft.Office.Interop.Excel.Application rqExcelPro = new Microsoft.Office.Interop.Excel.Application();
            rqExcelPro.Visible = false;
            Microsoft.Office.Interop.Excel.Workbook rqWorkBookPro = rqExcelPro.Workbooks.Open(strCableDataProXls);

            // Create worksheets 
            Microsoft.Office.Interop.Excel.Worksheet rqCableDataSheetNZ = rqWorkBookPro.Sheets[strCableDataBaseNZ] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqCableDataSheetAU = rqWorkBookPro.Sheets[strCableDataBaseAU] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqEFLISheetPro = rqWorkBookPro.Sheets[strEFLIsheetPro] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqSCSheetPro = rqWorkBookPro.Sheets[strSCsheetPro] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqMCBSchneiderIC60SheetPro = rqWorkBookPro.Sheets[strMCBSchneiderIC60] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqMCBTerasakiDinTSheetPro = rqWorkBookPro.Sheets[strMCBTerasakiDinT] as Worksheet;
            Microsoft.Office.Interop.Excel.Worksheet rqMCBABBSheetPro = rqWorkBookPro.Sheets[strMCBABB] as Worksheet;

            string[,] strProCableDataNZ = new string[PowerBIM_Constants.file_CableRowsMax, PowerBIM_Constants.file_CableColumnsMax];
            string[,] strProCableDataAU = new string[PowerBIM_Constants.file_CableRowsMax, PowerBIM_Constants.file_CableColumnsMax];
            string[,] strProEFLIdata = new string[PowerBIM_Constants.file_EFLIRowsMax, PowerBIM_Constants.file_EFLIColumnsMax];
            string[,] strProSCdata = new string[PowerBIM_Constants.file_KValueRowsMax, PowerBIM_Constants.file_KValueColumnsMax];
            string[,] strProMCBSchneiderIC60data = new string[PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax];
            string[,] strProTerasakiDinTdata = new string[PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax];
            string[,] strProMCBABBdata = new string[PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax];

            // Configuring the progress bar.
            pb.Reset(pb.Level + 1);
            pb.SetCaption("Initialising Excel read to string operation...", pb.Level + 1);
            pb.LevelUp();

            // Setting the scope of the progress bar.
            pb.SetScope(7, pb.Level);

            pb.Step("Reading NZ Cable Data");
            BlreadXLSsheet(ref strProCableDataNZ, rqCableDataSheetNZ, PowerBIM_Constants.file_CableRowsMax, PowerBIM_Constants.file_CableColumnsMax, PowerBIM_Constants.file_CableRowStart, PowerBIM_Constants.file_CableColumnStart);

            pb.Step("Reading Au Cable Data");
            BlreadXLSsheet(ref strProCableDataAU, rqCableDataSheetAU, PowerBIM_Constants.file_CableRowsMax, PowerBIM_Constants.file_CableColumnsMax, PowerBIM_Constants.file_CableRowStart, PowerBIM_Constants.file_CableColumnStart);

            pb.Step("Reading EFLI Data");
            BlreadXLSsheet(ref strProEFLIdata, rqEFLISheetPro, PowerBIM_Constants.file_EFLIRowsMax, PowerBIM_Constants.file_EFLIColumnsMax, PowerBIM_Constants.file_EFLIRowStart, PowerBIM_Constants.file_EFLIColumnStart);

            pb.Step("Reading SC Data");
            BlreadXLSsheet(ref strProSCdata, rqSCSheetPro, PowerBIM_Constants.file_KValueRowsMax, PowerBIM_Constants.file_KValueColumnsMax, PowerBIM_Constants.file_KValueRowStart, PowerBIM_Constants.file_KValueColumnStart);

            pb.Step("Reading Schneider MCB Data");
            BlreadXLSsheet(ref strProMCBSchneiderIC60data, rqMCBSchneiderIC60SheetPro, PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax, PowerBIM_Constants.file_MCBRowStart, PowerBIM_Constants.file_MCBColumnStart);

            pb.Step("Reading Terasaki MCB Data");
            BlreadXLSsheet(ref strProTerasakiDinTdata, rqMCBTerasakiDinTSheetPro, PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax, PowerBIM_Constants.file_MCBRowStart, PowerBIM_Constants.file_MCBColumnStart);

            pb.Step("Reading ABB MCB Data");
            BlreadXLSsheet(ref strProMCBABBdata, rqMCBABBSheetPro, PowerBIM_Constants.file_MCBRowsMax, PowerBIM_Constants.file_MCBColumnsMax, PowerBIM_Constants.file_MCBRowStart, PowerBIM_Constants.file_MCBColumnStart);
            pb.LevelDown();

            pb.Step("Modifying strings");
            string strCSVCableDataNZ = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strCableDataBaseNZ + ".csv");
            string strCSVCableDataAU = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strCableDataBaseAU + ".csv");
            string strProCSVEFLI = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strEFLIsheetPro + ".csv");
            string strProCSVSC = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strSCsheetPro + ".csv");
            string strProCSVMCBSchneiderIC60SheetPro = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strMCBSchneiderIC60 + ".csv");
            string strProCSVMCTerasakiDinTSheetPro = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strMCBTerasakiDinT + ".csv");
            string strProCSVMCBABBSheetPro = strCableDataProXls.Replace(PowerBIM_Constants.CableDatabaseName, strMCBABB + ".csv");

            // Configuring the progress bar.
            pb.Reset(pb.Level + 1);
            pb.LevelUp();
            pb.Reset(pb.Level + 1);
            pb.SetCaption("Initialising CSV Generation...", pb.Level + 1);

            // Setting the scope of the progress bar.
            pb.SetScope(7, pb.Level);

            pb.Step("Writing NZ Cable Data");
            CSVUtility.TwoDarrayWriteToCsv(strProCableDataNZ, strCSVCableDataNZ);

            pb.Step("Writing Au Cable Data");
            CSVUtility.TwoDarrayWriteToCsv(strProCableDataAU, strCSVCableDataAU);

            pb.Step("Writing EFLI Data");
            CSVUtility.TwoDarrayWriteToCsv(strProEFLIdata, strProCSVEFLI);

            pb.Step("Writing SC Data");
            CSVUtility.TwoDarrayWriteToCsv(strProSCdata, strProCSVSC);

            pb.Step("Writing Schneider MCB Data");
            CSVUtility.TwoDarrayWriteToCsv(strProMCBSchneiderIC60data, strProCSVMCBSchneiderIC60SheetPro);

            pb.Step("Writing Terasaki MCB Data");
            CSVUtility.TwoDarrayWriteToCsv(strProTerasakiDinTdata, strProCSVMCTerasakiDinTSheetPro);

            pb.Step("Writing ABB MCB Data");
            CSVUtility.TwoDarrayWriteToCsv(strProMCBABBdata, strProCSVMCBABBSheetPro);

            pb.LevelDown();
            pb.Step("Closing and saving workbooks");
            rqWorkBookPro.Close(false);

            ///*** Finish Data Pre-Processing
            blProcessXLStoCSV = true;
            UI.Forms.ModelessPowerBIM_StartFormHandler.ShowMsgToTheUser("Beca PowerBIM", "Cable Database Imported");
            return blProcessXLStoCSV;
        }


        internal static bool BlreadXLSsheet(ref string[,] strStoredData, Microsoft.Office.Interop.Excel.Worksheet rqWorksheet, int RowMax, int ColMax, int RowStart, int ColStart)
        {
            //Read Data
            bool blXLSreadSuccess = false;
            for (int I = 0; I <= RowMax - 1; I++)
            {
                for (int J = 0; J <= ColMax - 1; J++)
                {
                    if (null != (rqWorksheet.Cells[I + RowStart, J + ColStart] as Range).Value)
                    {
                        strStoredData[I, J] = (rqWorksheet.Cells[I + RowStart, J + ColStart] as Range).Value.ToString();
                    }
                }
            }
            blXLSreadSuccess = true;
            return blXLSreadSuccess;
        }

        private void frmPowerBIM_AdvancedSettings_Load(object sender, EventArgs e)
        {
            Owner?.Hide();
        }

        private void frmPowerBIM_AdvancedSettings_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (Owner is frmPowerBIM_Start ownerForm)
            {
                ownerForm.Show();
                ownerForm.ShowOrHideDB_SettingsColumn();
            }
        }

        private void tb_VoltDropBreakerRatingPercent_Validating(object sender, CancelEventArgs e)
        {
            int value;
            if (!int.TryParse(tb_VoltDropBreakerRatingPercent.Text, out value) || value < 1 || value > 100)
            {
                MessageBox.Show("Please enter an value between 1 and 100");
                e.Cancel = true;
            }
        }

        private void radioButtonGPOCalcBreakerRating_CheckedChanged(object sender, EventArgs e)
        {
            if (radioButtonGPOCalcBreakerRating.Checked)
                tb_VoltDropBreakerRatingPercent.Enabled = true;
            else
                tb_VoltDropBreakerRatingPercent.Enabled = false;
        }

        private void chb_ControlGlobally_CheckedChanged(object sender, EventArgs e)
        {
            if (chb_ControlGlobally.Checked)
            {
                gb_MainSettings.Enabled = true;
                PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally = true;
            }
            else
            {
                gb_MainSettings.Enabled = false;
                PowerBIM_5.Properties.Settings.Default.SettingsControlledGlobally = false;
            } 
        }

        private void ValidateDoubleInput(System.Windows.Forms.TextBox tb, bool IsPerElement)
        {
            if (!IsDoubleRealNumber(tb.Text))
            {
                MessageBox.Show("Please input a number");
                tb.Text = "0";
                return;
            }

            double value = double.Parse(tb.Text);
            tb.Text = value.ToString();
        }

        private bool IsDoubleRealNumber(string valueToTest)
        {
            if (double.TryParse(valueToTest, out double d) && !Double.IsNaN(d) && !Double.IsInfinity(d))
            {
                return true;
            }

            return false;
        }

        private void tbTxtLenPerElemLighting_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemLighting, true);
        }

        private void tbTxtLenPerElemLighting_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemLighting, true);
        }

        private void tbTxtLenPerElemPower_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemPower, true);
        }

        private void tbTxtLenPerElemPower_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemPower, true);
        }

        private void tbTxtLenPerElemOther_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerElemOther, true);
        }

        private void tbTxtLenPerElemOther_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerElemOther, true);
        }

        private void tbTxtLenPerCCTLighting_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTLighting, false);
        }

        private void tbTxtLenPerCCTLighting_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTLighting, false);
        }

        private void tbTxtLenPerCCTPower_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTPower, false);
        }

        private void tbTxtLenPerCCTPower_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTPower, false);
        }

        private void tbTxtLenPerCCTOther_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                ValidateDoubleInput(tbTxtLenPerCCTOther, false);
        }

        private void tbTxtLenPerCCTOther_Leave(object sender, EventArgs e)
        {
            ValidateDoubleInput(tbTxtLenPerCCTOther, false);
        }

        private void radioButtonGPOCalcAssignedLoad_CheckedChanged(object sender, EventArgs e)
        {

        }
    }

    #region Modeless Form Handler 

    public class ModelessPowerBIM_AdvancedSettingsFormHandler
    {
        #region Fields

        public static frmPowerBIM_AdvancedSettings AdvancedSettings;
        public static PowerBIM_ProjectInfo ProjInfo;

        #endregion

        #region Properties


        #endregion

        #region Methods

        public static void Commit_AdvancedSettings()
        {
            ProjInfo.Commit_AdvancedSettings();
            ProjInfo.SetDatabasePathParameter();
        }

        public static void WakeFormAdvancedSettings()
        {
            if (AdvancedSettings != null)
            {
                AdvancedSettings.WakeUp();
            }
        }

        public static void SetRevitParameters(PowerBIM_ProjectInfo ProjInfo)
        {

        }

        #endregion

    }

    #endregion
}
