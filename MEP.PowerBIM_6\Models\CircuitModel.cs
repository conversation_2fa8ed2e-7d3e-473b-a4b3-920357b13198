using System;
using System.Collections.Generic;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.PowerBIM_5.CoreLogic;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing a PowerBIM Circuit for WPF binding
    /// Wraps PowerBIM_CircuitData with ObservableObject for MVVM
    /// </summary>
    public partial class CircuitModel : ObservableObject
    {
        #region Fields

        private readonly PowerBIM_CircuitData _originalData;

        #endregion

        #region Observable Properties

        /// <summary>
        /// Circuit number
        /// </summary>
        [ObservableProperty]
        private string _cctNumber = string.Empty;

        /// <summary>
        /// Schedule description
        /// </summary>
        [ObservableProperty]
        private string _scheduleDescription = string.Empty;

        /// <summary>
        /// Revit current value
        /// </summary>
        [ObservableProperty]
        private double _revitCurrent;

        /// <summary>
        /// Indicates if manual current is used
        /// </summary>
        [ObservableProperty]
        private bool _manualCurrent;

        /// <summary>
        /// Manual PowerBIM user current
        /// </summary>
        [ObservableProperty]
        private double _manualPowerBimUserCurrent;

        /// <summary>
        /// PowerBIM calculated current
        /// </summary>
        [ObservableProperty]
        private double _cctPowerBimCurrent;

        /// <summary>
        /// Number of poles (1 for single phase, 3 for three phase)
        /// </summary>
        [ObservableProperty]
        private int _numberOfPoles;

        /// <summary>
        /// Number of elements
        /// </summary>
        [ObservableProperty]
        private int _numberOfElements;

        /// <summary>
        /// Schedule cable to first
        /// </summary>
        [ObservableProperty]
        private string _scheduleCableToFirst = string.Empty;

        /// <summary>
        /// Schedule cable to final
        /// </summary>
        [ObservableProperty]
        private string _scheduleCableToFinal = string.Empty;

        /// <summary>
        /// Circuit length (manual or calculated)
        /// </summary>
        [ObservableProperty]
        private double _cctLength;

        /// <summary>
        /// Indicates if circuit length is manual
        /// </summary>
        [ObservableProperty]
        private bool _circuitLengthIsManual;

        /// <summary>
        /// Install method index
        /// </summary>
        [ObservableProperty]
        private int _installMethodIndex;

        /// <summary>
        /// Voltage drop percentage
        /// </summary>
        [ObservableProperty]
        private double _voltageDropPercent;

        /// <summary>
        /// Undiversified current Phase A
        /// </summary>
        [ObservableProperty]
        private double _cctUndiversifiedCurrentPhaseA;

        /// <summary>
        /// Undiversified current Phase B
        /// </summary>
        [ObservableProperty]
        private double _cctUndiversifiedCurrentPhaseB;

        /// <summary>
        /// Undiversified current Phase C
        /// </summary>
        [ObservableProperty]
        private double _cctUndiversifiedCurrentPhaseC;

        /// <summary>
        /// Indicates if circuit is spare or space
        /// </summary>
        [ObservableProperty]
        private bool _cctIsSpareOrSpace;

        /// <summary>
        /// Indicates if circuit is power
        /// </summary>
        [ObservableProperty]
        private bool _cctIsPower;

        /// <summary>
        /// Indicates if circuit is lighting
        /// </summary>
        [ObservableProperty]
        private bool _cctIsLighting;

        /// <summary>
        /// Indicates if GPO is present
        /// </summary>
        [ObservableProperty]
        private bool _cctGpoPresent;

        /// <summary>
        /// Indicates if RCD element is present
        /// </summary>
        [ObservableProperty]
        private bool _cctRcdElementIsPresent;

        /// <summary>
        /// RCD name
        /// </summary>
        [ObservableProperty]
        private string _cctRcdName = string.Empty;

        /// <summary>
        /// GPO count
        /// </summary>
        [ObservableProperty]
        private int _gpoCount;

        /// <summary>
        /// Circuit clearing time
        /// </summary>
        [ObservableProperty]
        private double _cctClearingTime;

        /// <summary>
        /// Indicates if data is good
        /// </summary>
        [ObservableProperty]
        private bool _dataGood;

        /// <summary>
        /// Error message
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        /// <summary>
        /// Indicates if parameters are good
        /// </summary>
        [ObservableProperty]
        private bool _parametersGood;

        /// <summary>
        /// Indicates if values are missing
        /// </summary>
        [ObservableProperty]
        private bool _valuesMissing;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        #endregion

        #region Properties

        /// <summary>
        /// Get the original PowerBIM_CircuitData
        /// </summary>
        public PowerBIM_CircuitData OriginalData => _originalData;

        /// <summary>
        /// Indicates if the circuit data is valid
        /// </summary>
        public bool IsValid => DataGood && ParametersGood && !ValuesMissing;

        /// <summary>
        /// Cable to first model
        /// </summary>
        public CableModel CableToFirst { get; private set; }

        /// <summary>
        /// Cable to final model
        /// </summary>
        public CableModel CableToFinal { get; private set; }

        /// <summary>
        /// Breaker model
        /// </summary>
        public BreakerModel Breaker { get; private set; }

        /// <summary>
        /// Circuit type description
        /// </summary>
        public string CircuitType
        {
            get
            {
                if (CctIsSpareOrSpace) return "Spare/Space";
                if (CctIsPower) return "Power";
                if (CctIsLighting) return "Lighting";
                return "Other";
            }
        }

        /// <summary>
        /// Phase description
        /// </summary>
        public string PhaseDescription => NumberOfPoles == 1 ? "Single Phase" : "Three Phase";

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the CircuitModel with PowerBIM_CircuitData
        /// </summary>
        /// <param name="circuitData">Original circuit data</param>
        public CircuitModel(PowerBIM_CircuitData circuitData)
        {
            _originalData = circuitData ?? throw new ArgumentNullException(nameof(circuitData));
            
            // Initialize cable and breaker models
            InitializeChildModels();
            
            // Initialize properties from original data
            LoadFromOriginalData();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Load properties from the original PowerBIM_CircuitData
        /// </summary>
        public void LoadFromOriginalData()
        {
            if (_originalData != null)
            {
                CctNumber = _originalData.CCT_Number ?? string.Empty;
                ScheduleDescription = _originalData.Schedule_Description ?? string.Empty;
                RevitCurrent = _originalData.Revit_Current;
                ManualCurrent = _originalData.ManualCurrent;
                ManualPowerBimUserCurrent = _originalData.Manual_PowerBim_User_Current;
                CctPowerBimCurrent = _originalData.CCT_PowerBIM_Current;
                NumberOfPoles = _originalData.Number_Of_Poles;
                NumberOfElements = _originalData.Number_Of_Elements;
                ScheduleCableToFirst = _originalData.Schedule_Cable_To_First ?? string.Empty;
                ScheduleCableToFinal = _originalData.Schedule_Cable_To_Final ?? string.Empty;
                CctLength = _originalData.CCT_Length;
                CircuitLengthIsManual = _originalData.CircuitLengthIsManual;
                InstallMethodIndex = _originalData.Install_Method_Index;
                VoltageDropPercent = _originalData.VoltageDropPercent;
                CctUndiversifiedCurrentPhaseA = _originalData.CCT_Undiversified_Current_Phase_A;
                CctUndiversifiedCurrentPhaseB = _originalData.CCT_Undiversified_Current_Phase_B;
                CctUndiversifiedCurrentPhaseC = _originalData.CCT_Undiversified_Current_Phase_C;
                CctIsSpareOrSpace = _originalData.CCT_Is_Spare_Or_Space;
                CctIsPower = _originalData.CCT_Is_Power;
                CctIsLighting = _originalData.CCT_Is_Lighting;
                CctGpoPresent = _originalData.CCT_GPO_Present;
                CctRcdElementIsPresent = _originalData.CCT_RCD_ElementIsPresent;
                CctRcdName = _originalData.CCT_RCD_Name ?? string.Empty;
                GpoCount = _originalData.GPO_Count;
                CctClearingTime = _originalData.CCT_Clearing_Time;
                DataGood = _originalData.Data_Good;
                ErrorMessage = _originalData.Error_Message ?? string.Empty;
                ParametersGood = _originalData.Parameters_Good;
                ValuesMissing = _originalData.Values_Missing;
                
                // Update child models
                CableToFirst?.LoadFromOriginalData();
                CableToFinal?.LoadFromOriginalData();
                Breaker?.LoadFromOriginalData();
                
                // Update validation status
                ValidateData();
            }
        }

        /// <summary>
        /// Save changes back to the original PowerBIM_CircuitData
        /// </summary>
        public void SaveToOriginalData()
        {
            if (_originalData != null)
            {
                _originalData.CCT_Number = CctNumber;
                _originalData.Schedule_Description = ScheduleDescription;
                _originalData.ManualCurrent = ManualCurrent;
                _originalData.Manual_PowerBim_User_Current = ManualPowerBimUserCurrent;
                _originalData.CCT_PowerBIM_Current = CctPowerBimCurrent;
                _originalData.Number_Of_Poles = NumberOfPoles;
                _originalData.Number_Of_Elements = NumberOfElements;
                _originalData.Schedule_Cable_To_First = ScheduleCableToFirst;
                _originalData.Schedule_Cable_To_Final = ScheduleCableToFinal;
                _originalData.CCT_Length = CctLength;
                _originalData.CircuitLengthIsManual = CircuitLengthIsManual;
                _originalData.Install_Method_Index = InstallMethodIndex;
                _originalData.VoltageDropPercent = VoltageDropPercent;
                _originalData.CCT_Undiversified_Current_Phase_A = CctUndiversifiedCurrentPhaseA;
                _originalData.CCT_Undiversified_Current_Phase_B = CctUndiversifiedCurrentPhaseB;
                _originalData.CCT_Undiversified_Current_Phase_C = CctUndiversifiedCurrentPhaseC;
                _originalData.CCT_Is_Spare_Or_Space = CctIsSpareOrSpace;
                _originalData.CCT_Is_Power = CctIsPower;
                _originalData.CCT_Is_Lighting = CctIsLighting;
                _originalData.CCT_GPO_Present = CctGpoPresent;
                _originalData.CCT_RCD_ElementIsPresent = CctRcdElementIsPresent;
                _originalData.CCT_RCD_Name = CctRcdName;
                _originalData.GPO_Count = GpoCount;
                _originalData.CCT_Clearing_Time = CctClearingTime;
                _originalData.Data_Good = DataGood;
                _originalData.Error_Message = ErrorMessage;
                _originalData.Parameters_Good = ParametersGood;
                _originalData.Values_Missing = ValuesMissing;
                
                // Save child models
                CableToFirst?.SaveToOriginalData();
                CableToFinal?.SaveToOriginalData();
                Breaker?.SaveToOriginalData();
            }
        }

        /// <summary>
        /// Refresh the model from the original data
        /// </summary>
        public void Refresh()
        {
            LoadFromOriginalData();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initialize child models for cables and breaker
        /// </summary>
        private void InitializeChildModels()
        {
            if (_originalData != null)
            {
                if (_originalData.Cable_To_First != null)
                    CableToFirst = new CableModel(_originalData.Cable_To_First);
                
                if (_originalData.Cable_To_Final != null)
                    CableToFinal = new CableModel(_originalData.Cable_To_Final);
                
                if (_originalData.Breaker != null)
                    Breaker = new BreakerModel(_originalData.Breaker);
            }
        }

        /// <summary>
        /// Validate the circuit data
        /// </summary>
        private void ValidateData()
        {
            try
            {
                var errors = new List<string>();
                
                // Basic validation
                if (string.IsNullOrWhiteSpace(CctNumber))
                    errors.Add("Circuit number is required");
                
                if (NumberOfPoles != 1 && NumberOfPoles != 3)
                    errors.Add("Number of poles must be 1 or 3");
                
                if (CctPowerBimCurrent < 0)
                    errors.Add("Current cannot be negative");
                
                // Check child model validation
                if (CableToFirst?.HasErrors == true)
                    errors.Add("Cable to first has errors");
                
                if (CableToFinal?.HasErrors == true)
                    errors.Add("Cable to final has errors");
                
                if (Breaker?.HasErrors == true)
                    errors.Add("Breaker has errors");
                
                HasErrors = !DataGood || !ParametersGood || ValuesMissing || errors.Count > 0;
                
                if (errors.Count > 0)
                    ErrorMessage = string.Join("; ", errors);
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle circuit type property changes
        /// </summary>
        partial void OnCctIsPowerChanged(bool value)
        {
            OnPropertyChanged(nameof(CircuitType));
        }

        partial void OnCctIsLightingChanged(bool value)
        {
            OnPropertyChanged(nameof(CircuitType));
        }

        partial void OnCctIsSpareOrSpaceChanged(bool value)
        {
            OnPropertyChanged(nameof(CircuitType));
        }

        /// <summary>
        /// Handle number of poles change
        /// </summary>
        partial void OnNumberOfPolesChanged(int value)
        {
            OnPropertyChanged(nameof(PhaseDescription));
        }

        /// <summary>
        /// Handle validation property changes
        /// </summary>
        partial void OnDataGoodChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        partial void OnParametersGoodChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        partial void OnValuesMissingChanged(bool value)
        {
            ValidateData();
            OnPropertyChanged(nameof(IsValid));
        }

        #endregion
    }
}
