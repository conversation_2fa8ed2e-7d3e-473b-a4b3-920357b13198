&lt;Window x:Class="MEP.PowerBIM_6.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
        mc:Ignorable="d"
        Title="PowerBIM 6" 
        Height="800" 
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="White">

    &lt;Window.Resources&gt;
        &lt;ResourceDictionary&gt;
            &lt;ResourceDictionary.MergedDictionaries&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.DeepPurple.xaml" /&gt;
                &lt;ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" /&gt;
            &lt;/ResourceDictionary.MergedDictionaries&gt;
            
            &lt;converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" /&gt;
            &lt;converters:StatusColorConverter x:Key="StatusColorConverter" /&gt;
        &lt;/ResourceDictionary&gt;
    &lt;/Window.Resources&gt;

    &lt;materialDesign:DialogHost&gt;
        &lt;Grid&gt;
            &lt;Grid.RowDefinitions&gt;
                &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Header --&gt;
                &lt;RowDefinition Height="*"/&gt;    &lt;!-- Main Content --&gt;
                &lt;RowDefinition Height="Auto"/&gt; &lt;!-- Status Bar --&gt;
            &lt;/Grid.RowDefinitions&gt;

            &lt;!-- Header with Logo and Title --&gt;
            &lt;materialDesign:ColorZone Grid.Row="0" 
                                      Mode="PrimaryMid" 
                                      Padding="16"
                                      materialDesign:ElevationAssist.Elevation="Dp4"&gt;
                &lt;StackPanel Orientation="Horizontal"&gt;
                    &lt;Image Source="../Resources/Images/BecaLogoBlack.png" 
                           Height="32" 
                           Margin="0,0,16,0"
                           VerticalAlignment="Center"/&gt;
                    &lt;TextBlock Text="PowerBIM 6" 
                               Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                               VerticalAlignment="Center"
                               Foreground="White"/&gt;
                    &lt;TextBlock Text="- Electrical Design Analysis Tool" 
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               VerticalAlignment="Center"
                               Foreground="White"
                               Opacity="0.8"
                               Margin="8,0,0,0"/&gt;
                &lt;/StackPanel&gt;
            &lt;/materialDesign:ColorZone&gt;

            &lt;!-- Main Tabbed Interface --&gt;
            &lt;TabControl Grid.Row="1" 
                        Style="{StaticResource MaterialDesignNavigationRailTabControl}"
                        Margin="8"&gt;
                
                &lt;!-- Distribution Board Overview Tab --&gt;
                &lt;TabItem Header="Distribution Board Overview"&gt;
                    &lt;Grid Margin="16"&gt;
                        &lt;Grid.ColumnDefinitions&gt;
                            &lt;ColumnDefinition Width="2*"/&gt;
                            &lt;ColumnDefinition Width="*"/&gt;
                        &lt;/Grid.ColumnDefinitions&gt;

                        &lt;!-- Distribution Board List with Status --&gt;
                        &lt;materialDesign:Card Grid.Column="0"
                                             Margin="0,0,8,0"
                                             materialDesign:ElevationAssist.Elevation="Dp2"&gt;
                            &lt;Grid&gt;
                                &lt;Grid.RowDefinitions&gt;
                                    &lt;RowDefinition Height="Auto"/&gt;
                                    &lt;RowDefinition Height="*"/&gt;
                                &lt;/Grid.RowDefinitions&gt;

                                &lt;TextBlock Grid.Row="0"
                                           Text="Distribution Board Summary"
                                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                           Margin="16,16,16,8"/&gt;

                                &lt;DataGrid Grid.Row="1"
                                          ItemsSource="{Binding DistributionBoards}"
                                          SelectedItem="{Binding SelectedDistributionBoard}"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          Margin="16,0,16,16"
                                          Style="{StaticResource MaterialDesignDataGrid}"&gt;
                                    &lt;DataGrid.Columns&gt;
                                        &lt;DataGridTextColumn Header="Distribution Board Name"
                                                            Binding="{Binding Name}"
                                                            IsReadOnly="True"
                                                            Width="*"/&gt;
                                        &lt;DataGridTextColumn Header="Circuits"
                                                            Binding="{Binding CircuitCount}"
                                                            IsReadOnly="True"
                                                            Width="80"/&gt;
                                        &lt;DataGridTextColumn Header="Status"
                                                            Binding="{Binding Status}"
                                                            IsReadOnly="True"
                                                            Width="100"/&gt;
                                        &lt;DataGridCheckBoxColumn Header="Locked"
                                                                Binding="{Binding IsLocked}"
                                                                IsReadOnly="True"
                                                                Width="60"/&gt;
                                    &lt;/DataGrid.Columns&gt;
                                &lt;/DataGrid&gt;
                            &lt;/Grid&gt;
                        &lt;/materialDesign:Card&gt;

                        &lt;!-- Distribution Board Actions Panel --&gt;
                        &lt;materialDesign:Card Grid.Column="1"
                                             Margin="8,0,0,0"
                                             materialDesign:ElevationAssist.Elevation="Dp2"&gt;
                            &lt;StackPanel Margin="16"&gt;
                                &lt;TextBlock Text="Distribution Board Actions"
                                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                           Margin="0,0,0,16"/&gt;
                                
                                &lt;Button Content="Run Calculations" 
                                        Command="{Binding RunCalculationsCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding CanRunCalculations}"/&gt;
                                
                                &lt;Button Content="Enhanced Circuit Edit" 
                                        Command="{Binding OpenCircuitEditCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasSelectedDistributionBoard}"/&gt;
                                
                                &lt;Button Content="Distribution Board Edit"
                                        Command="{Binding OpenDbEditCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasSelectedDistributionBoard}"/&gt;
                                
                                &lt;Separator Margin="0,16"/&gt;
                                
                                &lt;Button Content="Export Data" 
                                        Command="{Binding ExportDataCommand}"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,4"/&gt;
                                
                                &lt;Button Content="Advanced Settings" 
                                        Command="{Binding OpenAdvancedSettingsCommand}"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,4"/&gt;
                                
                                &lt;Separator Margin="0,16"/&gt;
                                
                                &lt;Button Content="Save Project" 
                                        Command="{Binding SaveProjectCommand}"
                                        Style="{StaticResource MaterialDesignRaisedAccentButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasUnsavedChanges}"/&gt;
                            &lt;/StackPanel&gt;
                        &lt;/materialDesign:Card&gt;
                    &lt;/Grid&gt;
                &lt;/TabItem&gt;

                &lt;!-- Project Settings Tab --&gt;
                &lt;TabItem Header="Project Settings"&gt;
                    &lt;materialDesign:Card Margin="16" 
                                         materialDesign:ElevationAssist.Elevation="Dp2"&gt;
                        &lt;StackPanel Margin="16"&gt;
                            &lt;TextBlock Text="Project Configuration" 
                                       Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                       Margin="0,0,0,16"/&gt;
                            
                            &lt;!-- Project settings controls will be added here --&gt;
                            &lt;TextBlock Text="Project settings will be implemented in the next phase" 
                                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                                       Foreground="Gray"/&gt;
                        &lt;/StackPanel&gt;
                    &lt;/materialDesign:Card&gt;
                &lt;/TabItem&gt;

                &lt;!-- Bulk Operations Tab --&gt;
                &lt;TabItem Header="Bulk Operations"&gt;
                    &lt;materialDesign:Card Margin="16" 
                                         materialDesign:ElevationAssist.Elevation="Dp2"&gt;
                        &lt;StackPanel Margin="16"&gt;
                            &lt;TextBlock Text="Bulk Edit Operations" 
                                       Style="{StaticResource MaterialDesignHeadline6TextBlock}" 
                                       Margin="0,0,0,16"/&gt;
                            
                            &lt;!-- Bulk operations controls will be added here --&gt;
                            &lt;TextBlock Text="Bulk operations will be implemented in the next phase" 
                                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                                       Foreground="Gray"/&gt;
                        &lt;/StackPanel&gt;
                    &lt;/materialDesign:Card&gt;
                &lt;/TabItem&gt;
            &lt;/TabControl&gt;

            &lt;!-- Status Bar --&gt;
            &lt;StatusBar Grid.Row="2" 
                       Background="{DynamicResource MaterialDesignPaper}"
                       materialDesign:ElevationAssist.Elevation="Dp1"&gt;
                &lt;StatusBarItem&gt;
                    &lt;TextBlock Text="{Binding StatusMessage}" 
                               Margin="8,0"/&gt;
                &lt;/StatusBarItem&gt;
                &lt;StatusBarItem HorizontalAlignment="Right"&gt;
                    &lt;StackPanel Orientation="Horizontal"&gt;
                        &lt;ProgressBar Value="{Binding Progress}" 
                                     Width="100" 
                                     Height="16"
                                     Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}"
                                     Margin="0,0,8,0"/&gt;
                        &lt;TextBlock Text="{Binding Progress, StringFormat={}{0:F0}%}" 
                                   Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/&gt;
                    &lt;/StackPanel&gt;
                &lt;/StatusBarItem&gt;
            &lt;/StatusBar&gt;
        &lt;/Grid&gt;
    &lt;/materialDesign:DialogHost&gt;
&lt;/Window&gt;
