&lt;Window x:Class="MEP.PowerBIM_6.Views.DbEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Distribution Board Edit - PowerBIM 6"
        Height="500" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        Background="White"&gt;

    &lt;Grid&gt;
        &lt;materialDesign:Card Margin="16"&gt;
            &lt;StackPanel Margin="16"&gt;
                &lt;TextBlock Text="Distribution Board Editor"
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                           Margin="0,0,0,16"/&gt;

                &lt;TextBlock Text="{Binding DistributionBoardName, StringFormat='Distribution Board: {0}'}"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Margin="0,0,0,16"/&gt;

                &lt;TextBlock Text="Distribution Board editing functionality will be implemented in Phase 4"
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="Gray"
                           Margin="0,0,0,32"/&gt;
                
                &lt;StackPanel Orientation="Horizontal" HorizontalAlignment="Right"&gt;
                    &lt;Button Content="Import CSV" Command="{Binding ImportCsvCommand}" Margin="4"/&gt;
                    &lt;Button Content="Activate Path Edit" Command="{Binding ActivatePathEditCommand}" Margin="4"/&gt;
                    &lt;Button Content="Cancel" Command="{Binding CancelCommand}" Margin="4"/&gt;
                    &lt;Button Content="Save" Command="{Binding SaveCommand}" Margin="4"/&gt;
                &lt;/StackPanel&gt;
            &lt;/StackPanel&gt;
        &lt;/materialDesign:Card&gt;
    &lt;/Grid&gt;
&lt;/Window&gt;
