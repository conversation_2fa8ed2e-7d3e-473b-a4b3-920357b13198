using System;
using System.Collections.Generic;
using System.IO;
using CommunityToolkit.Mvvm.ComponentModel;

namespace MEP.PowerBIM_6.Models
{
    /// <summary>
    /// Model representing export settings for WPF binding
    /// </summary>
    public partial class ExportSettingsModel : ObservableObject
    {
        #region Observable Properties

        /// <summary>
        /// Output file path
        /// </summary>
        [ObservableProperty]
        private string _outputPath = string.Empty;

        /// <summary>
        /// Include images in export
        /// </summary>
        [ObservableProperty]
        private bool _includeImages = true;

        /// <summary>
        /// Export format (Excel, PDF, CSV)
        /// </summary>
        [ObservableProperty]
        private string _exportFormat = "Excel";

        /// <summary>
        /// Include distribution board summary
        /// </summary>
        [ObservableProperty]
        private bool _includeDbSummary = true;

        /// <summary>
        /// Include circuit details
        /// </summary>
        [ObservableProperty]
        private bool _includeCircuitDetails = true;

        /// <summary>
        /// Include cable calculations
        /// </summary>
        [ObservableProperty]
        private bool _includeCableCalculations = true;

        /// <summary>
        /// Include breaker calculations
        /// </summary>
        [ObservableProperty]
        private bool _includeBreakerCalculations = true;

        /// <summary>
        /// Include voltage drop calculations
        /// </summary>
        [ObservableProperty]
        private bool _includeVoltageDropCalculations = true;

        /// <summary>
        /// Include project information
        /// </summary>
        [ObservableProperty]
        private bool _includeProjectInfo = true;

        /// <summary>
        /// Include warnings and errors
        /// </summary>
        [ObservableProperty]
        private bool _includeWarningsAndErrors = true;

        /// <summary>
        /// Open file after export
        /// </summary>
        [ObservableProperty]
        private bool _openAfterExport = true;

        /// <summary>
        /// Overwrite existing files
        /// </summary>
        [ObservableProperty]
        private bool _overwriteExisting = false;

        /// <summary>
        /// Include timestamp in filename
        /// </summary>
        [ObservableProperty]
        private bool _includeTimestamp = true;

        /// <summary>
        /// Custom filename prefix
        /// </summary>
        [ObservableProperty]
        private string _filenamePrefix = "PowerBIM_Export";

        /// <summary>
        /// Page orientation for PDF (Portrait, Landscape)
        /// </summary>
        [ObservableProperty]
        private string _pageOrientation = "Portrait";

        /// <summary>
        /// Paper size for PDF (A4, A3, Letter)
        /// </summary>
        [ObservableProperty]
        private string _paperSize = "A4";

        /// <summary>
        /// Include header and footer
        /// </summary>
        [ObservableProperty]
        private bool _includeHeaderFooter = true;

        /// <summary>
        /// Custom header text
        /// </summary>
        [ObservableProperty]
        private string _customHeaderText = string.Empty;

        /// <summary>
        /// Custom footer text
        /// </summary>
        [ObservableProperty]
        private string _customFooterText = string.Empty;

        /// <summary>
        /// Indicates if there are validation errors
        /// </summary>
        [ObservableProperty]
        private bool _hasErrors;

        /// <summary>
        /// Error message
        /// </summary>
        [ObservableProperty]
        private string _errorMessage = string.Empty;

        #endregion

        #region Properties

        /// <summary>
        /// Available export formats
        /// </summary>
        public string[] AvailableFormats => new[] { "Excel", "PDF", "CSV" };

        /// <summary>
        /// Available page orientations
        /// </summary>
        public string[] PageOrientations => new[] { "Portrait", "Landscape" };

        /// <summary>
        /// Available paper sizes
        /// </summary>
        public string[] PaperSizes => new[] { "A4", "A3", "Letter", "Legal" };

        /// <summary>
        /// File extension based on export format
        /// </summary>
        public string FileExtension
        {
            get
            {
                return ExportFormat switch
                {
                    "Excel" => ".xlsx",
                    "PDF" => ".pdf",
                    "CSV" => ".csv",
                    _ => ".xlsx"
                };
            }
        }

        /// <summary>
        /// Generated filename based on settings
        /// </summary>
        public string GeneratedFilename
        {
            get
            {
                var filename = FilenamePrefix;
                
                if (IncludeTimestamp)
                {
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    filename += $"_{timestamp}";
                }
                
                filename += FileExtension;
                return filename;
            }
        }

        /// <summary>
        /// Full output file path
        /// </summary>
        public string FullOutputPath
        {
            get
            {
                if (string.IsNullOrEmpty(OutputPath))
                    return string.Empty;
                
                try
                {
                    return Path.Combine(OutputPath, GeneratedFilename);
                }
                catch
                {
                    return string.Empty;
                }
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the ExportSettingsModel with default values
        /// </summary>
        public ExportSettingsModel()
        {
            // Set default output path to user's Documents folder
            try
            {
                OutputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
            catch
            {
                OutputPath = string.Empty;
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validate current export settings
        /// </summary>
        public bool ValidateSettings()
        {
            try
            {
                var errors = new List<string>();
                
                // Validate output path
                if (string.IsNullOrWhiteSpace(OutputPath))
                {
                    errors.Add("Output path is required");
                }
                else
                {
                    try
                    {
                        if (!Directory.Exists(OutputPath))
                            errors.Add("Output directory does not exist");
                    }
                    catch
                    {
                        errors.Add("Invalid output path format");
                    }
                }
                
                // Validate filename prefix
                if (string.IsNullOrWhiteSpace(FilenamePrefix))
                    errors.Add("Filename prefix is required");
                else if (FilenamePrefix.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
                    errors.Add("Filename prefix contains invalid characters");
                
                // Validate export format
                if (!Array.Exists(AvailableFormats, f => f == ExportFormat))
                    errors.Add("Invalid export format");
                
                // Validate page orientation for PDF
                if (ExportFormat == "PDF" && !Array.Exists(PageOrientations, o => o == PageOrientation))
                    errors.Add("Invalid page orientation for PDF export");
                
                // Validate paper size for PDF
                if (ExportFormat == "PDF" && !Array.Exists(PaperSizes, s => s == PaperSize))
                    errors.Add("Invalid paper size for PDF export");
                
                // Check if at least one content option is selected
                if (!IncludeDbSummary && !IncludeCircuitDetails && !IncludeCableCalculations && 
                    !IncludeBreakerCalculations && !IncludeVoltageDropCalculations && 
                    !IncludeProjectInfo && !IncludeWarningsAndErrors)
                {
                    errors.Add("At least one content option must be selected");
                }
                
                // Check for file overwrite if file exists
                if (!OverwriteExisting && !string.IsNullOrEmpty(FullOutputPath) && File.Exists(FullOutputPath))
                {
                    errors.Add($"File already exists: {GeneratedFilename}. Enable 'Overwrite Existing' or change filename.");
                }
                
                HasErrors = errors.Count > 0;
                ErrorMessage = HasErrors ? string.Join("; ", errors) : string.Empty;
                
                return !HasErrors;
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Validation error: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// Reset settings to default values
        /// </summary>
        public void ResetToDefaults()
        {
            try
            {
                OutputPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
            catch
            {
                OutputPath = string.Empty;
            }
            
            IncludeImages = true;
            ExportFormat = "Excel";
            IncludeDbSummary = true;
            IncludeCircuitDetails = true;
            IncludeCableCalculations = true;
            IncludeBreakerCalculations = true;
            IncludeVoltageDropCalculations = true;
            IncludeProjectInfo = true;
            IncludeWarningsAndErrors = true;
            OpenAfterExport = true;
            OverwriteExisting = false;
            IncludeTimestamp = true;
            FilenamePrefix = "PowerBIM_Export";
            PageOrientation = "Portrait";
            PaperSize = "A4";
            IncludeHeaderFooter = true;
            CustomHeaderText = string.Empty;
            CustomFooterText = string.Empty;
            
            HasErrors = false;
            ErrorMessage = string.Empty;
        }

        /// <summary>
        /// Browse for output directory
        /// </summary>
        public void BrowseForOutputDirectory()
        {
            try
            {
                using var dialog = new System.Windows.Forms.FolderBrowserDialog();
                dialog.Description = "Select Export Output Directory";
                dialog.SelectedPath = OutputPath;
                
                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    OutputPath = dialog.SelectedPath;
                }
            }
            catch (Exception ex)
            {
                HasErrors = true;
                ErrorMessage = $"Error browsing for directory: {ex.Message}";
            }
        }

        #endregion

        #region Property Change Handlers

        /// <summary>
        /// Handle property changes that affect generated filename
        /// </summary>
        partial void OnFilenameePrefixChanged(string value)
        {
            OnPropertyChanged(nameof(GeneratedFilename));
            OnPropertyChanged(nameof(FullOutputPath));
            ValidateSettings();
        }

        partial void OnIncludeTimestampChanged(bool value)
        {
            OnPropertyChanged(nameof(GeneratedFilename));
            OnPropertyChanged(nameof(FullOutputPath));
        }

        partial void OnExportFormatChanged(string value)
        {
            OnPropertyChanged(nameof(FileExtension));
            OnPropertyChanged(nameof(GeneratedFilename));
            OnPropertyChanged(nameof(FullOutputPath));
            ValidateSettings();
        }

        partial void OnOutputPathChanged(string value)
        {
            OnPropertyChanged(nameof(FullOutputPath));
            ValidateSettings();
        }

        /// <summary>
        /// Handle validation-related property changes
        /// </summary>
        partial void OnOverwriteExistingChanged(bool value)
        {
            ValidateSettings();
        }

        #endregion
    }
}
