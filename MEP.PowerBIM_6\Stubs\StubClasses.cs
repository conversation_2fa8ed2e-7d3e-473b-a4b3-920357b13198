using System;
using System.Collections.Generic;
using Autodesk.Revit.UI;

// Stub classes for missing dependencies during infrastructure testing
// These will be replaced with actual references in Phase 2

namespace BecaCommand
{
    public abstract class BecaBaseCommand : IExternalCommand
    {
        protected StubLogger _taskLogger = new StubLogger();

        public abstract Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, Autodesk.Revit.DB.ElementSet elements);

        public Result Execute(ExternalCommandData commandData, ref string message, Autodesk.Revit.DB.ElementSet elements)
        {
            return ExecuteBecaCommand(commandData, ref message, elements);
        }

        public abstract string GetAddinAuthor();
        public abstract string GetAddinName();
        public abstract string GetCommandSubName();
    }
}

namespace BecaActivityLogger.CoreLogic.Data
{
    public class BecaActivityLoggerData
    {
        public void PreTaskStart() { }
        public void PostTaskEnd(string message) { }
        public void LogInfo(string message) { }
        public void LogError(string message) { }
        public void LogWarning(string message) { }
    }

    public class StubLogger
    {
        public void PreTaskStart() { }
        public void PostTaskEnd(string message) { }
        public void LogInfo(string message) { }
        public void LogError(string message) { }
        public void LogWarning(string message) { }
    }
}

namespace MEP.PowerBIM_5.CoreLogic
{
    public class PowerBIM_ProjectInfo
    {
        public UIDocument UIDocument { get; set; }
        public Autodesk.Revit.DB.Document Document { get; set; }
        public string JobName { get; set; } = "Test Project";
        public string Engineer { get; set; } = "Test Engineer";
        public double SystemVoltageDropMax { get; set; } = 5.0;
        public int AmbientTemperature { get; set; } = 30;

        public PowerBIM_ProjectInfo(UIDocument uidoc)
        {
            UIDocument = uidoc;
            Document = uidoc?.Document;
        }

        public void CommitProjectInfo()
        {
            // Stub implementation
        }
    }

    public class PowerBIM_DBData
    {
        public string Schedule_DB_Name { get; set; } = "Default Distribution Board";
        public List<PowerBIM_CircuitData> AllCircuits { get; set; } = new List<PowerBIM_CircuitData>();
        public bool IsManuallyLocked { get; set; }
        public int PassCount { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class PowerBIM_CircuitData
    {
        public string CCT_Number { get; set; } = "1";
        public double Load { get; set; }
        public string Description { get; set; } = "Circuit";
        public bool IsManualOverride { get; set; }
    }
}

namespace BecaTransactionsNamesManager
{
    public static class AddinNames
    {
        public static class PowerBIM6
        {
            public static string Value => "PowerBIM 6";
        }
    }
}

namespace Common.UI.Forms
{
    // Stub for Common.UI.Forms if needed
}

namespace BecaRevitUtilities.ElementUtilities
{
    // Stub for BecaRevitUtilities if needed
}
