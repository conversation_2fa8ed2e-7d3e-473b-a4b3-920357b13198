﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace MEP.PowerBIM_5.DataSets {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("EnhancedCCTDataSet")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class EnhancedCCTDataSet : global::System.Data.DataSet {
        
        private EnhancedCCTTableDataTable tableEnhancedCCTTable;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public EnhancedCCTDataSet() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected EnhancedCCTDataSet(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["EnhancedCCTTable"] != null)) {
                    base.Tables.Add(new EnhancedCCTTableDataTable(ds.Tables["EnhancedCCTTable"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public EnhancedCCTTableDataTable EnhancedCCTTable {
            get {
                return this.tableEnhancedCCTTable;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public override global::System.Data.DataSet Clone() {
            EnhancedCCTDataSet cln = ((EnhancedCCTDataSet)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["EnhancedCCTTable"] != null)) {
                    base.Tables.Add(new EnhancedCCTTableDataTable(ds.Tables["EnhancedCCTTable"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        internal void InitVars(bool initTable) {
            this.tableEnhancedCCTTable = ((EnhancedCCTTableDataTable)(base.Tables["EnhancedCCTTable"]));
            if ((initTable == true)) {
                if ((this.tableEnhancedCCTTable != null)) {
                    this.tableEnhancedCCTTable.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void InitClass() {
            this.DataSetName = "EnhancedCCTDataSet";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/EnhancedCCTDataSet.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableEnhancedCCTTable = new EnhancedCCTTableDataTable();
            base.Tables.Add(this.tableEnhancedCCTTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private bool ShouldSerializeEnhancedCCTTable() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            EnhancedCCTDataSet ds = new EnhancedCCTDataSet();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public delegate void EnhancedCCTTableRowChangeEventHandler(object sender, EnhancedCCTTableRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class EnhancedCCTTableDataTable : global::System.Data.TypedTableBase<EnhancedCCTTableRow> {
            
            private global::System.Data.DataColumn columnCircuit_Number;
            
            private global::System.Data.DataColumn columnDevice_Rating;
            
            private global::System.Data.DataColumn columnDevice_Curve_Type;
            
            private global::System.Data.DataColumn columnProtection_Device;
            
            private global::System.Data.DataColumn columnRCD_Protection;
            
            private global::System.Data.DataColumn columnOther_Controls;
            
            private global::System.Data.DataColumn columnCable_To_First_Circuit_Component;
            
            private global::System.Data.DataColumn columnCable_To_Remainder_Of_Circuit_Components;
            
            private global::System.Data.DataColumn columnCable_Installation_Method;
            
            private global::System.Data.DataColumn columnDerating_Factor;
            
            private global::System.Data.DataColumn columnDiversity;
            
            private global::System.Data.DataColumn columnPath_Mode;
            
            private global::System.Data.DataColumn columnLength_To_First;
            
            private global::System.Data.DataColumn columnLength_To_Final;
            
            private global::System.Data.DataColumn columnPowerBimCurrent;
            
            private global::System.Data.DataColumn columnNumber_Of_Elements;
            
            private global::System.Data.DataColumn columnCircuit_Description;
            
            private global::System.Data.DataColumn columnCheck_Trip_Rating;
            
            private global::System.Data.DataColumn columnCable_1_Valid;
            
            private global::System.Data.DataColumn columnCable_2_Valid;
            
            private global::System.Data.DataColumn columnCheck_CPD_Descriminates;
            
            private global::System.Data.DataColumn columnCheck_Load_Current;
            
            private global::System.Data.DataColumn columnCheck_Cable_1_Current;
            
            private global::System.Data.DataColumn columnCheck_Cable_2_Current;
            
            private global::System.Data.DataColumn columnCheck_EFLI;
            
            private global::System.Data.DataColumn columnCheck_Final_CCT_VD;
            
            private global::System.Data.DataColumn columnCheck_System_Max_VD;
            
            private global::System.Data.DataColumn columnCheck_Cable_1_SC_Withstand;
            
            private global::System.Data.DataColumn columnCheck_Cable_2_SC_Withstand;
            
            private global::System.Data.DataColumn columnCircuit_Check_Summary;
            
            private global::System.Data.DataColumn columnCircuit_Check_Result;
            
            private global::System.Data.DataColumn columnCircuit_Revision;
            
            private global::System.Data.DataColumn columnisSpareOrSpace;
            
            private global::System.Data.DataColumn columnManual;
            
            private global::System.Data.DataColumn columnManualCurrent;
            
            private global::System.Data.DataColumn columnManualCurrentValue;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableDataTable() {
                this.TableName = "EnhancedCCTTable";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal EnhancedCCTTableDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected EnhancedCCTTableDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Circuit_NumberColumn {
                get {
                    return this.columnCircuit_Number;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Device_RatingColumn {
                get {
                    return this.columnDevice_Rating;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Device_Curve_TypeColumn {
                get {
                    return this.columnDevice_Curve_Type;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Protection_DeviceColumn {
                get {
                    return this.columnProtection_Device;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn RCD_ProtectionColumn {
                get {
                    return this.columnRCD_Protection;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Other_ControlsColumn {
                get {
                    return this.columnOther_Controls;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Cable_To_First_Circuit_ComponentColumn {
                get {
                    return this.columnCable_To_First_Circuit_Component;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Cable_To_Remainder_Of_Circuit_ComponentsColumn {
                get {
                    return this.columnCable_To_Remainder_Of_Circuit_Components;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Cable_Installation_MethodColumn {
                get {
                    return this.columnCable_Installation_Method;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Derating_FactorColumn {
                get {
                    return this.columnDerating_Factor;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn DiversityColumn {
                get {
                    return this.columnDiversity;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Path_ModeColumn {
                get {
                    return this.columnPath_Mode;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Length_To_FirstColumn {
                get {
                    return this.columnLength_To_First;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Length_To_FinalColumn {
                get {
                    return this.columnLength_To_Final;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn PowerBimCurrentColumn {
                get {
                    return this.columnPowerBimCurrent;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Number_Of_ElementsColumn {
                get {
                    return this.columnNumber_Of_Elements;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Circuit_DescriptionColumn {
                get {
                    return this.columnCircuit_Description;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Trip_RatingColumn {
                get {
                    return this.columnCheck_Trip_Rating;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Cable_1_ValidColumn {
                get {
                    return this.columnCable_1_Valid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Cable_2_ValidColumn {
                get {
                    return this.columnCable_2_Valid;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_CPD_DescriminatesColumn {
                get {
                    return this.columnCheck_CPD_Descriminates;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Load_CurrentColumn {
                get {
                    return this.columnCheck_Load_Current;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Cable_1_CurrentColumn {
                get {
                    return this.columnCheck_Cable_1_Current;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Cable_2_CurrentColumn {
                get {
                    return this.columnCheck_Cable_2_Current;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_EFLIColumn {
                get {
                    return this.columnCheck_EFLI;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Final_CCT_VDColumn {
                get {
                    return this.columnCheck_Final_CCT_VD;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_System_Max_VDColumn {
                get {
                    return this.columnCheck_System_Max_VD;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Cable_1_SC_WithstandColumn {
                get {
                    return this.columnCheck_Cable_1_SC_Withstand;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Check_Cable_2_SC_WithstandColumn {
                get {
                    return this.columnCheck_Cable_2_SC_Withstand;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Circuit_Check_SummaryColumn {
                get {
                    return this.columnCircuit_Check_Summary;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Circuit_Check_ResultColumn {
                get {
                    return this.columnCircuit_Check_Result;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn Circuit_RevisionColumn {
                get {
                    return this.columnCircuit_Revision;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn isSpareOrSpaceColumn {
                get {
                    return this.columnisSpareOrSpace;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ManualColumn {
                get {
                    return this.columnManual;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ManualCurrentColumn {
                get {
                    return this.columnManualCurrent;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataColumn ManualCurrentValueColumn {
                get {
                    return this.columnManualCurrentValue;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableRow this[int index] {
                get {
                    return ((EnhancedCCTTableRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event EnhancedCCTTableRowChangeEventHandler EnhancedCCTTableRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event EnhancedCCTTableRowChangeEventHandler EnhancedCCTTableRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event EnhancedCCTTableRowChangeEventHandler EnhancedCCTTableRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public event EnhancedCCTTableRowChangeEventHandler EnhancedCCTTableRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void AddEnhancedCCTTableRow(EnhancedCCTTableRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableRow AddEnhancedCCTTableRow(
                        string Circuit_Number, 
                        double Device_Rating, 
                        string Device_Curve_Type, 
                        string Protection_Device, 
                        string RCD_Protection, 
                        string Other_Controls, 
                        string Cable_To_First_Circuit_Component, 
                        string Cable_To_Remainder_Of_Circuit_Components, 
                        string Cable_Installation_Method, 
                        double Derating_Factor, 
                        double Diversity, 
                        string Path_Mode, 
                        double Length_To_First, 
                        double Length_To_Final, 
                        double PowerBimCurrent, 
                        double Number_Of_Elements, 
                        string Circuit_Description, 
                        string Check_Trip_Rating, 
                        string Cable_1_Valid, 
                        string Cable_2_Valid, 
                        string Check_CPD_Descriminates, 
                        string Check_Load_Current, 
                        string Check_Cable_1_Current, 
                        string Check_Cable_2_Current, 
                        string Check_EFLI, 
                        string Check_Final_CCT_VD, 
                        string Check_System_Max_VD, 
                        string Check_Cable_1_SC_Withstand, 
                        string Check_Cable_2_SC_Withstand, 
                        string Circuit_Check_Summary, 
                        string Circuit_Check_Result, 
                        string Circuit_Revision, 
                        bool isSpareOrSpace, 
                        string Manual, 
                        bool ManualCurrent, 
                        double ManualCurrentValue) {
                EnhancedCCTTableRow rowEnhancedCCTTableRow = ((EnhancedCCTTableRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        Circuit_Number,
                        Device_Rating,
                        Device_Curve_Type,
                        Protection_Device,
                        RCD_Protection,
                        Other_Controls,
                        Cable_To_First_Circuit_Component,
                        Cable_To_Remainder_Of_Circuit_Components,
                        Cable_Installation_Method,
                        Derating_Factor,
                        Diversity,
                        Path_Mode,
                        Length_To_First,
                        Length_To_Final,
                        PowerBimCurrent,
                        Number_Of_Elements,
                        Circuit_Description,
                        Check_Trip_Rating,
                        Cable_1_Valid,
                        Cable_2_Valid,
                        Check_CPD_Descriminates,
                        Check_Load_Current,
                        Check_Cable_1_Current,
                        Check_Cable_2_Current,
                        Check_EFLI,
                        Check_Final_CCT_VD,
                        Check_System_Max_VD,
                        Check_Cable_1_SC_Withstand,
                        Check_Cable_2_SC_Withstand,
                        Circuit_Check_Summary,
                        Circuit_Check_Result,
                        Circuit_Revision,
                        isSpareOrSpace,
                        Manual,
                        ManualCurrent,
                        ManualCurrentValue};
                rowEnhancedCCTTableRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowEnhancedCCTTableRow);
                return rowEnhancedCCTTableRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public override global::System.Data.DataTable Clone() {
                EnhancedCCTTableDataTable cln = ((EnhancedCCTTableDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new EnhancedCCTTableDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal void InitVars() {
                this.columnCircuit_Number = base.Columns["Circuit_Number"];
                this.columnDevice_Rating = base.Columns["Device_Rating"];
                this.columnDevice_Curve_Type = base.Columns["Device_Curve_Type"];
                this.columnProtection_Device = base.Columns["Protection_Device"];
                this.columnRCD_Protection = base.Columns["RCD_Protection"];
                this.columnOther_Controls = base.Columns["Other_Controls"];
                this.columnCable_To_First_Circuit_Component = base.Columns["Cable_To_First_Circuit_Component"];
                this.columnCable_To_Remainder_Of_Circuit_Components = base.Columns["Cable_To_Remainder_Of_Circuit_Components"];
                this.columnCable_Installation_Method = base.Columns["Cable_Installation_Method"];
                this.columnDerating_Factor = base.Columns["Derating_Factor"];
                this.columnDiversity = base.Columns["Diversity"];
                this.columnPath_Mode = base.Columns["Path_Mode"];
                this.columnLength_To_First = base.Columns["Length_To_First"];
                this.columnLength_To_Final = base.Columns["Length_To_Final"];
                this.columnPowerBimCurrent = base.Columns["PowerBimCurrent"];
                this.columnNumber_Of_Elements = base.Columns["Number_Of_Elements"];
                this.columnCircuit_Description = base.Columns["Circuit_Description"];
                this.columnCheck_Trip_Rating = base.Columns["Check_Trip_Rating"];
                this.columnCable_1_Valid = base.Columns["Cable_1_Valid"];
                this.columnCable_2_Valid = base.Columns["Cable_2_Valid"];
                this.columnCheck_CPD_Descriminates = base.Columns["Check_CPD_Descriminates"];
                this.columnCheck_Load_Current = base.Columns["Check_Load_Current"];
                this.columnCheck_Cable_1_Current = base.Columns["Check_Cable_1_Current"];
                this.columnCheck_Cable_2_Current = base.Columns["Check_Cable_2_Current"];
                this.columnCheck_EFLI = base.Columns["Check_EFLI"];
                this.columnCheck_Final_CCT_VD = base.Columns["Check_Final_CCT_VD"];
                this.columnCheck_System_Max_VD = base.Columns["Check_System_Max_VD"];
                this.columnCheck_Cable_1_SC_Withstand = base.Columns["Check_Cable_1_SC_Withstand"];
                this.columnCheck_Cable_2_SC_Withstand = base.Columns["Check_Cable_2_SC_Withstand"];
                this.columnCircuit_Check_Summary = base.Columns["Circuit_Check_Summary"];
                this.columnCircuit_Check_Result = base.Columns["Circuit_Check_Result"];
                this.columnCircuit_Revision = base.Columns["Circuit_Revision"];
                this.columnisSpareOrSpace = base.Columns["isSpareOrSpace"];
                this.columnManual = base.Columns["Manual"];
                this.columnManualCurrent = base.Columns["ManualCurrent"];
                this.columnManualCurrentValue = base.Columns["ManualCurrentValue"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            private void InitClass() {
                this.columnCircuit_Number = new global::System.Data.DataColumn("Circuit_Number", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCircuit_Number);
                this.columnDevice_Rating = new global::System.Data.DataColumn("Device_Rating", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDevice_Rating);
                this.columnDevice_Curve_Type = new global::System.Data.DataColumn("Device_Curve_Type", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDevice_Curve_Type);
                this.columnProtection_Device = new global::System.Data.DataColumn("Protection_Device", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnProtection_Device);
                this.columnRCD_Protection = new global::System.Data.DataColumn("RCD_Protection", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRCD_Protection);
                this.columnOther_Controls = new global::System.Data.DataColumn("Other_Controls", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnOther_Controls);
                this.columnCable_To_First_Circuit_Component = new global::System.Data.DataColumn("Cable_To_First_Circuit_Component", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCable_To_First_Circuit_Component);
                this.columnCable_To_Remainder_Of_Circuit_Components = new global::System.Data.DataColumn("Cable_To_Remainder_Of_Circuit_Components", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCable_To_Remainder_Of_Circuit_Components);
                this.columnCable_Installation_Method = new global::System.Data.DataColumn("Cable_Installation_Method", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCable_Installation_Method);
                this.columnDerating_Factor = new global::System.Data.DataColumn("Derating_Factor", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDerating_Factor);
                this.columnDiversity = new global::System.Data.DataColumn("Diversity", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDiversity);
                this.columnPath_Mode = new global::System.Data.DataColumn("Path_Mode", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPath_Mode);
                this.columnLength_To_First = new global::System.Data.DataColumn("Length_To_First", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnLength_To_First);
                this.columnLength_To_Final = new global::System.Data.DataColumn("Length_To_Final", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnLength_To_Final);
                this.columnPowerBimCurrent = new global::System.Data.DataColumn("PowerBimCurrent", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnPowerBimCurrent);
                this.columnNumber_Of_Elements = new global::System.Data.DataColumn("Number_Of_Elements", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnNumber_Of_Elements);
                this.columnCircuit_Description = new global::System.Data.DataColumn("Circuit_Description", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCircuit_Description);
                this.columnCheck_Trip_Rating = new global::System.Data.DataColumn("Check_Trip_Rating", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Trip_Rating);
                this.columnCable_1_Valid = new global::System.Data.DataColumn("Cable_1_Valid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCable_1_Valid);
                this.columnCable_2_Valid = new global::System.Data.DataColumn("Cable_2_Valid", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCable_2_Valid);
                this.columnCheck_CPD_Descriminates = new global::System.Data.DataColumn("Check_CPD_Descriminates", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_CPD_Descriminates);
                this.columnCheck_Load_Current = new global::System.Data.DataColumn("Check_Load_Current", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Load_Current);
                this.columnCheck_Cable_1_Current = new global::System.Data.DataColumn("Check_Cable_1_Current", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Cable_1_Current);
                this.columnCheck_Cable_2_Current = new global::System.Data.DataColumn("Check_Cable_2_Current", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Cable_2_Current);
                this.columnCheck_EFLI = new global::System.Data.DataColumn("Check_EFLI", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_EFLI);
                this.columnCheck_Final_CCT_VD = new global::System.Data.DataColumn("Check_Final_CCT_VD", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Final_CCT_VD);
                this.columnCheck_System_Max_VD = new global::System.Data.DataColumn("Check_System_Max_VD", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_System_Max_VD);
                this.columnCheck_Cable_1_SC_Withstand = new global::System.Data.DataColumn("Check_Cable_1_SC_Withstand", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Cable_1_SC_Withstand);
                this.columnCheck_Cable_2_SC_Withstand = new global::System.Data.DataColumn("Check_Cable_2_SC_Withstand", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCheck_Cable_2_SC_Withstand);
                this.columnCircuit_Check_Summary = new global::System.Data.DataColumn("Circuit_Check_Summary", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCircuit_Check_Summary);
                this.columnCircuit_Check_Result = new global::System.Data.DataColumn("Circuit_Check_Result", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCircuit_Check_Result);
                this.columnCircuit_Revision = new global::System.Data.DataColumn("Circuit_Revision", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnCircuit_Revision);
                this.columnisSpareOrSpace = new global::System.Data.DataColumn("isSpareOrSpace", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnisSpareOrSpace);
                this.columnManual = new global::System.Data.DataColumn("Manual", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnManual);
                this.columnManualCurrent = new global::System.Data.DataColumn("ManualCurrent", typeof(bool), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnManualCurrent);
                this.columnManualCurrentValue = new global::System.Data.DataColumn("ManualCurrentValue", typeof(double), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnManualCurrentValue);
                this.columnDevice_Rating.AllowDBNull = false;
                this.columnDevice_Rating.DefaultValue = ((double)(0D));
                this.columnDerating_Factor.AllowDBNull = false;
                this.columnDerating_Factor.DefaultValue = ((double)(0D));
                this.columnDiversity.AllowDBNull = false;
                this.columnDiversity.DefaultValue = ((double)(0D));
                this.columnPath_Mode.DefaultValue = ((string)(""));
                this.columnLength_To_First.AllowDBNull = false;
                this.columnLength_To_First.DefaultValue = ((double)(0D));
                this.columnLength_To_Final.AllowDBNull = false;
                this.columnLength_To_Final.DefaultValue = ((double)(0D));
                this.columnPowerBimCurrent.AllowDBNull = false;
                this.columnPowerBimCurrent.DefaultValue = ((double)(0D));
                this.columnNumber_Of_Elements.AllowDBNull = false;
                this.columnNumber_Of_Elements.DefaultValue = ((double)(0D));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableRow NewEnhancedCCTTableRow() {
                return ((EnhancedCCTTableRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new EnhancedCCTTableRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override global::System.Type GetRowType() {
                return typeof(EnhancedCCTTableRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.EnhancedCCTTableRowChanged != null)) {
                    this.EnhancedCCTTableRowChanged(this, new EnhancedCCTTableRowChangeEvent(((EnhancedCCTTableRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.EnhancedCCTTableRowChanging != null)) {
                    this.EnhancedCCTTableRowChanging(this, new EnhancedCCTTableRowChangeEvent(((EnhancedCCTTableRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.EnhancedCCTTableRowDeleted != null)) {
                    this.EnhancedCCTTableRowDeleted(this, new EnhancedCCTTableRowChangeEvent(((EnhancedCCTTableRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.EnhancedCCTTableRowDeleting != null)) {
                    this.EnhancedCCTTableRowDeleting(this, new EnhancedCCTTableRowChangeEvent(((EnhancedCCTTableRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void RemoveEnhancedCCTTableRow(EnhancedCCTTableRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                EnhancedCCTDataSet ds = new EnhancedCCTDataSet();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "EnhancedCCTTableDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class EnhancedCCTTableRow : global::System.Data.DataRow {
            
            private EnhancedCCTTableDataTable tableEnhancedCCTTable;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            internal EnhancedCCTTableRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableEnhancedCCTTable = ((EnhancedCCTTableDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Circuit_Number {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Circuit_NumberColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Circuit_Number\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Circuit_NumberColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Device_Rating {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.Device_RatingColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.Device_RatingColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Device_Curve_Type {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Device_Curve_TypeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Device_Curve_Type\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Device_Curve_TypeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Protection_Device {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Protection_DeviceColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Protection_Device\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Protection_DeviceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string RCD_Protection {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.RCD_ProtectionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'RCD_Protection\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.RCD_ProtectionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Other_Controls {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Other_ControlsColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Other_Controls\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Other_ControlsColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Cable_To_First_Circuit_Component {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Cable_To_First_Circuit_ComponentColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Cable_To_First_Circuit_Component\' in table \'EnhancedCCTTabl" +
                                "e\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Cable_To_First_Circuit_ComponentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Cable_To_Remainder_Of_Circuit_Components {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Cable_To_Remainder_Of_Circuit_ComponentsColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Cable_To_Remainder_Of_Circuit_Components\' in table \'Enhance" +
                                "dCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Cable_To_Remainder_Of_Circuit_ComponentsColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Cable_Installation_Method {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Cable_Installation_MethodColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Cable_Installation_Method\' in table \'EnhancedCCTTable\' is D" +
                                "BNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Cable_Installation_MethodColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Derating_Factor {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.Derating_FactorColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.Derating_FactorColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Diversity {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.DiversityColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.DiversityColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Path_Mode {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Path_ModeColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Path_Mode\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Path_ModeColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Length_To_First {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.Length_To_FirstColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.Length_To_FirstColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Length_To_Final {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.Length_To_FinalColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.Length_To_FinalColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double PowerBimCurrent {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.PowerBimCurrentColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.PowerBimCurrentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double Number_Of_Elements {
                get {
                    return ((double)(this[this.tableEnhancedCCTTable.Number_Of_ElementsColumn]));
                }
                set {
                    this[this.tableEnhancedCCTTable.Number_Of_ElementsColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Circuit_Description {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Circuit_DescriptionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Circuit_Description\' in table \'EnhancedCCTTable\' is DBNull." +
                                "", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Circuit_DescriptionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Trip_Rating {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Trip_RatingColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Trip_Rating\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Trip_RatingColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Cable_1_Valid {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Cable_1_ValidColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Cable_1_Valid\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Cable_1_ValidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Cable_2_Valid {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Cable_2_ValidColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Cable_2_Valid\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Cable_2_ValidColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_CPD_Descriminates {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_CPD_DescriminatesColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_CPD_Descriminates\' in table \'EnhancedCCTTable\' is DBN" +
                                "ull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_CPD_DescriminatesColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Load_Current {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Load_CurrentColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Load_Current\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Load_CurrentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Cable_1_Current {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Cable_1_CurrentColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Cable_1_Current\' in table \'EnhancedCCTTable\' is DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Cable_1_CurrentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Cable_2_Current {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Cable_2_CurrentColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Cable_2_Current\' in table \'EnhancedCCTTable\' is DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Cable_2_CurrentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_EFLI {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_EFLIColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_EFLI\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_EFLIColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Final_CCT_VD {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Final_CCT_VDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Final_CCT_VD\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Final_CCT_VDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_System_Max_VD {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_System_Max_VDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_System_Max_VD\' in table \'EnhancedCCTTable\' is DBNull." +
                                "", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_System_Max_VDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Cable_1_SC_Withstand {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Cable_1_SC_WithstandColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Cable_1_SC_Withstand\' in table \'EnhancedCCTTable\' is " +
                                "DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Cable_1_SC_WithstandColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Check_Cable_2_SC_Withstand {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Check_Cable_2_SC_WithstandColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Check_Cable_2_SC_Withstand\' in table \'EnhancedCCTTable\' is " +
                                "DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Check_Cable_2_SC_WithstandColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Circuit_Check_Summary {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Circuit_Check_SummaryColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Circuit_Check_Summary\' in table \'EnhancedCCTTable\' is DBNul" +
                                "l.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Circuit_Check_SummaryColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Circuit_Check_Result {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Circuit_Check_ResultColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Circuit_Check_Result\' in table \'EnhancedCCTTable\' is DBNull" +
                                ".", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Circuit_Check_ResultColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Circuit_Revision {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.Circuit_RevisionColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Circuit_Revision\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.Circuit_RevisionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool isSpareOrSpace {
                get {
                    try {
                        return ((bool)(this[this.tableEnhancedCCTTable.isSpareOrSpaceColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'isSpareOrSpace\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.isSpareOrSpaceColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public string Manual {
                get {
                    try {
                        return ((string)(this[this.tableEnhancedCCTTable.ManualColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Manual\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.ManualColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool ManualCurrent {
                get {
                    try {
                        return ((bool)(this[this.tableEnhancedCCTTable.ManualCurrentColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'ManualCurrent\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.ManualCurrentColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public double ManualCurrentValue {
                get {
                    try {
                        return ((double)(this[this.tableEnhancedCCTTable.ManualCurrentValueColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'ManualCurrentValue\' in table \'EnhancedCCTTable\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableEnhancedCCTTable.ManualCurrentValueColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCircuit_NumberNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Circuit_NumberColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCircuit_NumberNull() {
                this[this.tableEnhancedCCTTable.Circuit_NumberColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsDevice_Curve_TypeNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Device_Curve_TypeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetDevice_Curve_TypeNull() {
                this[this.tableEnhancedCCTTable.Device_Curve_TypeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsProtection_DeviceNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Protection_DeviceColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetProtection_DeviceNull() {
                this[this.tableEnhancedCCTTable.Protection_DeviceColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsRCD_ProtectionNull() {
                return this.IsNull(this.tableEnhancedCCTTable.RCD_ProtectionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetRCD_ProtectionNull() {
                this[this.tableEnhancedCCTTable.RCD_ProtectionColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsOther_ControlsNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Other_ControlsColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetOther_ControlsNull() {
                this[this.tableEnhancedCCTTable.Other_ControlsColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCable_To_First_Circuit_ComponentNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Cable_To_First_Circuit_ComponentColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCable_To_First_Circuit_ComponentNull() {
                this[this.tableEnhancedCCTTable.Cable_To_First_Circuit_ComponentColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCable_To_Remainder_Of_Circuit_ComponentsNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Cable_To_Remainder_Of_Circuit_ComponentsColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCable_To_Remainder_Of_Circuit_ComponentsNull() {
                this[this.tableEnhancedCCTTable.Cable_To_Remainder_Of_Circuit_ComponentsColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCable_Installation_MethodNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Cable_Installation_MethodColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCable_Installation_MethodNull() {
                this[this.tableEnhancedCCTTable.Cable_Installation_MethodColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsPath_ModeNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Path_ModeColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetPath_ModeNull() {
                this[this.tableEnhancedCCTTable.Path_ModeColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCircuit_DescriptionNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Circuit_DescriptionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCircuit_DescriptionNull() {
                this[this.tableEnhancedCCTTable.Circuit_DescriptionColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Trip_RatingNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Trip_RatingColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Trip_RatingNull() {
                this[this.tableEnhancedCCTTable.Check_Trip_RatingColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCable_1_ValidNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Cable_1_ValidColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCable_1_ValidNull() {
                this[this.tableEnhancedCCTTable.Cable_1_ValidColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCable_2_ValidNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Cable_2_ValidColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCable_2_ValidNull() {
                this[this.tableEnhancedCCTTable.Cable_2_ValidColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_CPD_DescriminatesNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_CPD_DescriminatesColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_CPD_DescriminatesNull() {
                this[this.tableEnhancedCCTTable.Check_CPD_DescriminatesColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Load_CurrentNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Load_CurrentColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Load_CurrentNull() {
                this[this.tableEnhancedCCTTable.Check_Load_CurrentColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Cable_1_CurrentNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Cable_1_CurrentColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Cable_1_CurrentNull() {
                this[this.tableEnhancedCCTTable.Check_Cable_1_CurrentColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Cable_2_CurrentNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Cable_2_CurrentColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Cable_2_CurrentNull() {
                this[this.tableEnhancedCCTTable.Check_Cable_2_CurrentColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_EFLINull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_EFLIColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_EFLINull() {
                this[this.tableEnhancedCCTTable.Check_EFLIColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Final_CCT_VDNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Final_CCT_VDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Final_CCT_VDNull() {
                this[this.tableEnhancedCCTTable.Check_Final_CCT_VDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_System_Max_VDNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_System_Max_VDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_System_Max_VDNull() {
                this[this.tableEnhancedCCTTable.Check_System_Max_VDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Cable_1_SC_WithstandNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Cable_1_SC_WithstandColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Cable_1_SC_WithstandNull() {
                this[this.tableEnhancedCCTTable.Check_Cable_1_SC_WithstandColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCheck_Cable_2_SC_WithstandNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Check_Cable_2_SC_WithstandColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCheck_Cable_2_SC_WithstandNull() {
                this[this.tableEnhancedCCTTable.Check_Cable_2_SC_WithstandColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCircuit_Check_SummaryNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Circuit_Check_SummaryColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCircuit_Check_SummaryNull() {
                this[this.tableEnhancedCCTTable.Circuit_Check_SummaryColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCircuit_Check_ResultNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Circuit_Check_ResultColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCircuit_Check_ResultNull() {
                this[this.tableEnhancedCCTTable.Circuit_Check_ResultColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsCircuit_RevisionNull() {
                return this.IsNull(this.tableEnhancedCCTTable.Circuit_RevisionColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetCircuit_RevisionNull() {
                this[this.tableEnhancedCCTTable.Circuit_RevisionColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsisSpareOrSpaceNull() {
                return this.IsNull(this.tableEnhancedCCTTable.isSpareOrSpaceColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetisSpareOrSpaceNull() {
                this[this.tableEnhancedCCTTable.isSpareOrSpaceColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsManualNull() {
                return this.IsNull(this.tableEnhancedCCTTable.ManualColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetManualNull() {
                this[this.tableEnhancedCCTTable.ManualColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsManualCurrentNull() {
                return this.IsNull(this.tableEnhancedCCTTable.ManualCurrentColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetManualCurrentNull() {
                this[this.tableEnhancedCCTTable.ManualCurrentColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public bool IsManualCurrentValueNull() {
                return this.IsNull(this.tableEnhancedCCTTable.ManualCurrentValueColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public void SetManualCurrentValueNull() {
                this[this.tableEnhancedCCTTable.ManualCurrentValueColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
        public class EnhancedCCTTableRowChangeEvent : global::System.EventArgs {
            
            private EnhancedCCTTableRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableRowChangeEvent(EnhancedCCTTableRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public EnhancedCCTTableRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591