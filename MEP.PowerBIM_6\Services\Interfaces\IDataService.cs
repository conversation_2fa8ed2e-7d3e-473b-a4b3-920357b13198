using System;
using System.Collections.Generic;
using MEP.PowerBIM_5.CoreLogic;
using MEP.PowerBIM_6.Models;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Service interface for data operations and business logic
    /// Handles data transformation between PowerBIM core logic and WPF models
    /// </summary>
    public interface IDataService
    {
        #region Data Transformation

        /// <summary>
        /// Convert PowerBIM_ProjectInfo to ProjectInfoModel
        /// </summary>
        /// <param name="projectInfo">Core project info</param>
        /// <returns>Project info model for WPF binding</returns>
        ProjectInfoModel ConvertToModel(PowerBIM_ProjectInfo projectInfo);

        /// <summary>
        /// Convert ProjectInfoModel back to PowerBIM_ProjectInfo
        /// </summary>
        /// <param name="model">Project info model</param>
        /// <param name="originalProjectInfo">Original project info to update</param>
        /// <returns>Updated core project info</returns>
        PowerBIM_ProjectInfo ConvertFromModel(ProjectInfoModel model, PowerBIM_ProjectInfo originalProjectInfo);

        /// <summary>
        /// Convert PowerBIM_DBData to DistributionBoardModel
        /// </summary>
        /// <param name="dbData">Core distribution board data</param>
        /// <returns>Distribution board model for WPF binding</returns>
        DistributionBoardModel ConvertToModel(PowerBIM_DBData dbData);

        /// <summary>
        /// Convert PowerBIM_CircuitData to CircuitModel
        /// </summary>
        /// <param name="circuitData">Core circuit data</param>
        /// <returns>Circuit model for WPF binding</returns>
        CircuitModel ConvertToModel(PowerBIM_CircuitData circuitData);

        /// <summary>
        /// Convert list of PowerBIM_DBData to list of DatabaseModel
        /// </summary>
        /// <param name="dbDataList">List of core database data</param>
        /// <returns>List of database models</returns>
        List<DatabaseModel> ConvertToModels(List<PowerBIM_DBData> dbDataList);

        #endregion

        #region Data Validation

        /// <summary>
        /// Validate project information
        /// </summary>
        /// <param name="projectInfo">Project info to validate</param>
        /// <returns>Validation result with errors if any</returns>
        ValidationResult ValidateProjectInfo(ProjectInfoModel projectInfo);

        /// <summary>
        /// Validate database information
        /// </summary>
        /// <param name="database">Database to validate</param>
        /// <returns>Validation result with errors if any</returns>
        ValidationResult ValidateDatabase(DatabaseModel database);

        /// <summary>
        /// Validate circuit information
        /// </summary>
        /// <param name="circuit">Circuit to validate</param>
        /// <returns>Validation result with errors if any</returns>
        ValidationResult ValidateCircuit(CircuitModel circuit);

        /// <summary>
        /// Validate bulk edit settings
        /// </summary>
        /// <param name="settings">Settings to validate</param>
        /// <returns>Validation result with errors if any</returns>
        ValidationResult ValidateBulkEditSettings(BulkEditSettings settings);

        #endregion

        #region Data Processing

        /// <summary>
        /// Process database summary calculations (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="databases">Databases to process</param>
        /// <returns>Updated databases with summary information</returns>
        List<DatabaseModel> ProcessDatabaseSummary(List<DatabaseModel> databases);

        /// <summary>
        /// Calculate circuit properties (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuit">Circuit to calculate</param>
        /// <returns>Updated circuit with calculated properties</returns>
        CircuitModel CalculateCircuitProperties(CircuitModel circuit);

        /// <summary>
        /// Process circuit validation and status updates (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="circuits">Circuits to process</param>
        /// <returns>Updated circuits with validation status</returns>
        List<CircuitModel> ProcessCircuitValidation(List<CircuitModel> circuits);

        #endregion

        #region Filtering and Searching

        /// <summary>
        /// Filter circuits based on search criteria
        /// </summary>
        /// <param name="circuits">Circuits to filter</param>
        /// <param name="searchText">Search text</param>
        /// <param name="filterCriteria">Additional filter criteria</param>
        /// <returns>Filtered circuits</returns>
        List<CircuitModel> FilterCircuits(List<CircuitModel> circuits, string searchText, FilterCriteria filterCriteria = null);

        /// <summary>
        /// Filter databases based on search criteria
        /// </summary>
        /// <param name="databases">Databases to filter</param>
        /// <param name="searchText">Search text</param>
        /// <param name="filterCriteria">Additional filter criteria</param>
        /// <returns>Filtered databases</returns>
        List<DatabaseModel> FilterDatabases(List<DatabaseModel> databases, string searchText, FilterCriteria filterCriteria = null);

        #endregion

        #region Settings Management

        /// <summary>
        /// Load application settings (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>Application settings</returns>
        SettingsModel LoadSettings();

        /// <summary>
        /// Save application settings (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <param name="settings">Settings to save</param>
        /// <returns>True if successful</returns>
        bool SaveSettings(SettingsModel settings);

        /// <summary>
        /// Reset settings to defaults
        /// </summary>
        /// <returns>Default settings</returns>
        SettingsModel GetDefaultSettings();

        #endregion

        #region Data Caching

        /// <summary>
        /// Clear all cached data
        /// </summary>
        void ClearCache();

        /// <summary>
        /// Refresh cached data (REVIT-SAFE: Synchronous)
        /// </summary>
        /// <returns>True if successful</returns>
        bool RefreshCache();

        /// <summary>
        /// Get cached data if available
        /// </summary>
        /// <typeparam name="T">Type of cached data</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached data or null if not found</returns>
        T GetCachedData<T>(string key) where T : class;

        /// <summary>
        /// Set cached data
        /// </summary>
        /// <typeparam name="T">Type of data to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="data">Data to cache</param>
        /// <param name="expiration">Cache expiration time</param>
        void SetCachedData<T>(string key, T data, TimeSpan? expiration = null) where T : class;

        #endregion
    }

    /// <summary>
    /// Validation result container
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();

        public ValidationResult()
        {
            IsValid = true;
        }

        public ValidationResult(bool isValid, params string[] errors)
        {
            IsValid = isValid;
            if (errors != null)
            {
                Errors.AddRange(errors);
            }
        }

        public void AddError(string error)
        {
            Errors.Add(error);
            IsValid = false;
        }

        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }

    /// <summary>
    /// Filter criteria for data filtering operations
    /// </summary>
    public class FilterCriteria
    {
        public string CircuitType { get; set; }
        public string Status { get; set; }
        public bool? IsLocked { get; set; }
        public bool? HasErrors { get; set; }
        public double? MinLoad { get; set; }
        public double? MaxLoad { get; set; }
        public Dictionary<string, object> CustomFilters { get; set; } = new Dictionary<string, object>();
    }
}
