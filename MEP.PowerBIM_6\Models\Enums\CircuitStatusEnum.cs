namespace MEP.PowerBIM_6.Models.Enums
{
    /// <summary>
    /// Enumeration of circuit status values for PowerBIM 6
    /// </summary>
    public enum CircuitStatusEnum
    {
        /// <summary>
        /// Circuit status is unknown or not determined
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Circuit is valid and passes all checks
        /// </summary>
        Valid = 1,

        /// <summary>
        /// Circuit has warnings but is still functional
        /// </summary>
        Warning = 2,

        /// <summary>
        /// Circuit has errors and requires attention
        /// </summary>
        Error = 3,

        /// <summary>
        /// Circuit is a spare circuit
        /// </summary>
        Spare = 4,

        /// <summary>
        /// Circuit is a space (placeholder)
        /// </summary>
        Space = 5,

        /// <summary>
        /// Circuit is pending calculation
        /// </summary>
        Pending = 6,

        /// <summary>
        /// Circuit calculation is in progress
        /// </summary>
        Calculating = 7,

        /// <summary>
        /// Circuit is locked and cannot be modified
        /// </summary>
        Locked = 8,

        /// <summary>
        /// Circuit has been manually overridden
        /// </summary>
        ManualOverride = 9,

        /// <summary>
        /// Circuit requires user input
        /// </summary>
        RequiresInput = 10,

        /// <summary>
        /// Circuit data is incomplete
        /// </summary>
        Incomplete = 11,

        /// <summary>
        /// Circuit has voltage drop issues
        /// </summary>
        VoltageDropError = 12,

        /// <summary>
        /// Circuit has cable sizing issues
        /// </summary>
        CableSizingError = 13,

        /// <summary>
        /// Circuit has breaker sizing issues
        /// </summary>
        BreakerSizingError = 14,

        /// <summary>
        /// Circuit has short circuit issues
        /// </summary>
        ShortCircuitError = 15
    }

    /// <summary>
    /// Enumeration of path mode for circuit routing
    /// </summary>
    public enum PathModeEnum
    {
        /// <summary>
        /// Automatic path calculation
        /// </summary>
        Automatic = 0,

        /// <summary>
        /// Manual path definition
        /// </summary>
        Manual = 1,

        /// <summary>
        /// Path calculated from Revit model
        /// </summary>
        FromRevit = 2,

        /// <summary>
        /// Path imported from external source
        /// </summary>
        Imported = 3,

        /// <summary>
        /// Path is estimated
        /// </summary>
        Estimated = 4,

        /// <summary>
        /// Path is user-defined
        /// </summary>
        UserDefined = 5
    }

    /// <summary>
    /// Enumeration of calculation status
    /// </summary>
    public enum CalculationStatusEnum
    {
        /// <summary>
        /// Not calculated
        /// </summary>
        NotCalculated = 0,

        /// <summary>
        /// Calculation in progress
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// Calculation completed successfully
        /// </summary>
        Completed = 2,

        /// <summary>
        /// Calculation failed
        /// </summary>
        Failed = 3,

        /// <summary>
        /// Calculation completed with warnings
        /// </summary>
        CompletedWithWarnings = 4,

        /// <summary>
        /// Calculation requires user input
        /// </summary>
        RequiresInput = 5,

        /// <summary>
        /// Calculation was cancelled
        /// </summary>
        Cancelled = 6
    }

    /// <summary>
    /// Enumeration of circuit types
    /// </summary>
    public enum CircuitTypeEnum
    {
        /// <summary>
        /// Unknown or unspecified circuit type
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// Power circuit
        /// </summary>
        Power = 1,

        /// <summary>
        /// Lighting circuit
        /// </summary>
        Lighting = 2,

        /// <summary>
        /// Other circuit type
        /// </summary>
        Other = 3,

        /// <summary>
        /// Emergency lighting circuit
        /// </summary>
        EmergencyLighting = 4,

        /// <summary>
        /// Motor circuit
        /// </summary>
        Motor = 5,

        /// <summary>
        /// HVAC circuit
        /// </summary>
        HVAC = 6,

        /// <summary>
        /// Data/Communication circuit
        /// </summary>
        Data = 7,

        /// <summary>
        /// Fire alarm circuit
        /// </summary>
        FireAlarm = 8,

        /// <summary>
        /// Security circuit
        /// </summary>
        Security = 9,

        /// <summary>
        /// Spare circuit
        /// </summary>
        Spare = 10,

        /// <summary>
        /// Space (placeholder)
        /// </summary>
        Space = 11
    }

    /// <summary>
    /// Enumeration of validation result types
    /// </summary>
    public enum ValidationResultEnum
    {
        /// <summary>
        /// Validation passed
        /// </summary>
        Pass = 0,

        /// <summary>
        /// Validation failed
        /// </summary>
        Fail = 1,

        /// <summary>
        /// Validation passed with warnings
        /// </summary>
        Warning = 2,

        /// <summary>
        /// Validation not performed
        /// </summary>
        NotValidated = 3,

        /// <summary>
        /// Validation in progress
        /// </summary>
        InProgress = 4,

        /// <summary>
        /// Validation error occurred
        /// </summary>
        Error = 5
    }
}
