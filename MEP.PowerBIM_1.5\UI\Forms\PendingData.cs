﻿using MEP.PowerBIM_1._5.UI.ModelessRevitForm;
using MEP.PowerBIM_5.CoreLogic;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace MEP.PowerBIM_5.UI.Forms
{
    public class PendingData
    {
        public PowerBIM_CircuitData CCT;
        public DataRow Row;
        public DataGridViewCell Cell;
        public RequestId requestId;

        public PendingData(RequestId rQSTtId)
        {
            requestId = rQSTtId;
        }


        public override bool Equals(object obj)
        {
            if (obj == null || GetType() != obj.GetType())
                return false;

            PendingData other = (PendingData)obj;
            return requestId == other.requestId;
        }

        public override int GetHashCode()
        {
            return requestId.GetHashCode();
        }
    }
}